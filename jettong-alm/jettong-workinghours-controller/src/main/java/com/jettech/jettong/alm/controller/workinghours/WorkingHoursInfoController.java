package com.jettech.jettong.alm.controller.workinghours;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.database.mybatis.conditions.query.QueryWrap;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.StrPool;
import com.jettech.basic.utils.StringUtil;
import com.jettech.jettong.alm.api.ProjectApi;
import com.jettech.jettong.alm.api.TaskApi;
import com.jettech.jettong.alm.issue.dto.TaskCountQuery;
import com.jettech.jettong.alm.issue.entity.Task;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.alm.workinghours.dto.*;
import com.jettech.jettong.alm.workinghours.entity.WorkingHoursInfo;
import com.jettech.jettong.alm.workinghours.enumeration.WorkingHoursType;
import com.jettech.jettong.alm.workinghours.service.WorkItemService;
import com.jettech.jettong.alm.workinghours.service.WorkingHoursConfirmService;
import com.jettech.jettong.alm.workinghours.service.WorkingHoursInfoService;
import com.jettech.jettong.alm.workinghours.vo.*;
import com.jettech.jettong.base.api.OrgApi;
import com.jettech.jettong.base.api.PersonalizedTableViewApi;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.dto.PeriodQuery;
import com.jettech.jettong.common.util.poi.ExcelDownLoadUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.jettech.jettong.common.constant.SwaggerConstants.DATA_TYPE_LONG;
import static com.jettech.jettong.common.constant.SwaggerConstants.PARAM_TYPE_PATH;
import static com.jettech.jettong.common.constant.SwaggerConstants.PARAM_TYPE_QUERY;


/**
 * 工时填报信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 工时填报信息控制器
 * @projectName jettong
 * @package com.jettech.jettong.workinghours.controller
 * @className WorkingHoursInfoController
 * @date 2022-08-08
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/alm/workingHoursInfo")
@Api(value = "WorkingHoursInfo", tags = "工时填报信息")
@RequiredArgsConstructor
@PreAuth(enabled = false)
public class WorkingHoursInfoController extends SuperController<WorkingHoursInfoService, Long, WorkingHoursInfo,
        WorkingHoursInfoPageQuery, WorkingHoursInfoSaveDTO, WorkingHoursInfoUpdateDTO> {

    private static final String FILLING_TIME = "fillingTime";
    private static final String FILLED_BY = "filledBy";
    private static final String PROJECT = "project";

    private final WorkingHoursConfirmService workingHoursConfirmService;
    private final EchoService echoService;
    private final WorkItemService workItemService;
    private final UserApi userApi;
    private final ProjectApi projectApi;
    private final OrgApi orgApi;
    private final PersonalizedTableViewApi tableViewApi;
    private final TaskApi taskApi;


    @ApiOperation(value = "分页获取工时填报和分摊信息", notes = "分页获取工时填报和分摊信息")
    @PostMapping("/newPage")
    public R<IPage<WorkingHoursInfoAllocationVO>> newPage(@RequestBody PageParams<WorkingHoursPageQuery> params){
        WorkingHoursPageQuery query = params.getModel();
        dealQueryParam(query);
        IPage<WorkingHoursInfoAllocationVO> page = baseService.getWorkingHoursInfosAndAllocation(params.buildPage(), query);
        echoService.action(page.getRecords());
        workItemService.extendWorkItem(page.getRecords());

        // 保存最后一次查询条件
        tableViewApi.saveLastSearch(getUserId(), "hours-table", params);
        return success(page);
    }

    @ApiOperation(value = "获取工作项下的工时填报记录", notes = "获取工作项下的工时填报记录")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "工作项类型", paramType = PARAM_TYPE_PATH),
            @ApiImplicitParam(name = "bizId", value = "工作项ID", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @GetMapping("/{type}/{bizId}")
    public List<WorkingHoursInfo> getWorkingHoursInfos(@PathVariable WorkingHoursType type,
            @PathVariable Long bizId) {
        List<WorkingHoursInfo> workingHoursInfos = baseService.getWorkingHoursInfos(type, bizId);

        workItemService.extendWorkItem(workingHoursInfos);
        echoService.action(workingHoursInfos);
        return workingHoursInfos;
    }

    @ApiOperation(value = "获取用户的填报记录", notes = "获取用户的填报记录")
    @GetMapping("/history")
    public R<WorkingHoursHistory> getUserHistoryWorkingHours(@RequestParam List<Long> userIds, @RequestParam  LocalDate startDate, @RequestParam  LocalDate endDate, @RequestParam(required = false) Long projectId) {
        List<WorkingHoursInfoVO> userHistoryWorkingHours =
                baseService.getUserHistoryWorkingHours(userIds, startDate, endDate,projectId );
        Set<Serializable> collect = userHistoryWorkingHours.stream().filter(w->w.getType().equals(WorkingHoursType.TASK))
                .map(WorkingHoursInfoVO::getBizId).collect(Collectors.toSet());
        Map<Serializable, Object> taskApiByIds = taskApi.findByIds(collect);
        Map<Long, Task> taskMap = new HashMap<>();
        if (taskApiByIds != null) {
            for (Map.Entry<Serializable, Object> entry : taskApiByIds.entrySet()) {
                taskMap.put(Long.valueOf(entry.getKey().toString()), BeanPlusUtil.toBean(entry.getValue(), Task.class));
            }
        }
        //查询已经填报工时
        List<WorkingHoursInfo> workingHoursInfos = baseService.list(Wraps.<WorkingHoursInfo>lbQ()
                .in(WorkingHoursInfo::getBizId, collect)
                .eq(WorkingHoursInfo::getType, WorkingHoursType.TASK)
                .ne(WorkingHoursInfo::getValid,false));
        //计算出任务工时
        Map<Long, Double> workingHoursInfoMap = workingHoursInfos.stream()
                .collect(Collectors.groupingBy(WorkingHoursInfo::getBizId,
                        Collectors.summingDouble(WorkingHoursInfo::getDuration)));
        userHistoryWorkingHours.stream().forEach(
                workingHoursInfo -> {
                    if(workingHoursInfo.getType().equals(WorkingHoursType.TASK)) {
                        if(taskMap.get(workingHoursInfo.getBizId())!=null) {
                            Task task = taskMap.get(workingHoursInfo.getBizId());
                            workingHoursInfo.setEstimateHour(task.getEstimateHour());
                            if(workingHoursInfoMap.get(workingHoursInfo.getBizId())!=null){
                                workingHoursInfo.setIsFillInHour(workingHoursInfo.getEstimateHour() - workingHoursInfoMap.get(workingHoursInfo.getBizId()));
                            }else {
                                workingHoursInfo.setIsFillInHour(workingHoursInfo.getEstimateHour());
                            }
                            workingHoursInfo.setTestreqId(task.getTestreqId());
                        }
                    }
                }
        );

        echoService.action(userHistoryWorkingHours);
        List<WorkingHoursDay> hoursDays = baseService.getStatDayHoursByProject(userIds,projectId, startDate, endDate);
        return success(new WorkingHoursHistory(hoursDays, userHistoryWorkingHours));
    }

    @Override
    public R<WorkingHoursInfo> handlerSave(WorkingHoursInfoSaveDTO model) {
        // 补充项目ID
        Optional<Long> projectId = workItemService.getProjectId(model.getType(), model.getBizId());
        model.setProjectId(projectId.orElse(null));
        model.setOrgId(ContextUtil.getOrgId());
        return super.handlerSave(model);
    }

    @ApiOperation(value = "批量添加工时记录", notes = "批量添加工时记录")
    @PostMapping("/batch")
    public R<Boolean> batchSaveWorkingHoursInfos(@RequestBody List<WorkingHoursInfo> infoList) {


        // 补充项目ID
        Map<String, Long> projectMap = new HashMap<>(infoList.size());
        infoList.forEach(info -> {
            String key = info.getType().name() + info.getBizId();
            Long projectId = projectMap.computeIfAbsent(key, k ->  workItemService.getProjectId(info.getType(), info.getBizId()).orElse(null));
            info.setProjectId(projectId);
            info.setOrgId(ContextUtil.getOrgId());
        });

        return success(baseService.batchSaveWorkingHoursInfos(infoList));
    }

    /**
     * 修改是否计费
     *
     * @param billedUpdateDTO 工时记录集合
     * @return 是否修改成功
     */
    @ApiOperation(value = "修改是否计费", notes = "修改是否计费")
    @PutMapping("/billed")
    public R<Boolean> batchUpdateBilled(@RequestBody @Validated WorkingHoursInfoBilledUpdateDTO billedUpdateDTO) {

        return success(baseService.batchUpdateBilled(billedUpdateDTO));
    }

    /**
     * 分组查询工时填报记录
     *
     * @param pageParams 分组类别
     * @param pageParams    筛选条件
     * @return 分组key列表
     */
    @ApiOperation(value = "分组查询工时填报记录", notes = "分组查询工时填报记录")
    @PostMapping("/group")
    public IPage<Map<String, Object>> groupInfos(
            @RequestBody PageParams<WorkingHoursInfoPageQuery> pageParams) {
        pageParams.setSort(null);
        pageParams.setOrder(null);
        IPage<Map<String, Object>> page = pageParams.buildPage(Map.class);
        WorkingHoursInfoPageQuery model = pageParams.getModel();
        WorkingHoursInfo query = BeanPlusUtil.copyProperties(model, WorkingHoursInfo.class);
        QueryWrap<WorkingHoursInfo> wrapper = Wraps.q(query);

        String group = model.getGroupBy();
        if (StringUtil.isEmpty(group)) {
            throw BizException.validFail("当前分组仅支持 填报人(filledBy)、填报时间(fillingTime) 、项目(project)分组");
        }
        switch (group) {
            case FILLING_TIME:
                wrapper.select("date(filling_time) as bizid", "count(*) as count", "'filling_time' as grouptype");
                wrapper.groupBy("date(filling_time)");
                wrapper.isNotNull("date(filling_time)");
                break;
            case FILLED_BY:
                wrapper.select("filled_by as bizid", "count(*) as count", "'filled_by' as grouptype");
                wrapper.groupBy("filled_by");
                wrapper.isNotNull("filled_by");
                break;
            case PROJECT:
                wrapper.select("project_id as bizid", "count(*) as count", "'project_id' as grouptype");
                wrapper.groupBy("project_id");
                wrapper.isNotNull("project_id");
                break;
            default:
                throw BizException.validFail("当前分组仅支持 填报人(filledBy)、填报时间(fillingTime) 、项目(project)分组");
        }

        baseService.pageMaps(page, wrapper);
        page.getRecords().forEach( column -> {
            String type = column.get("grouptype").toString();
            Object biz = column.get("bizid");
            column.put("bizId", biz);
            column.put("groupType", column.get("grouptype"));
            if (biz == null) {
                return;
            }
            if (type.equals("filled_by")) {
                String bizId = biz.toString();
                User user = userApi.findUserById(Long.valueOf(bizId));
                column.put("data", user);
            } else if (type.equals("project_id")) {
                String bizId = biz.toString();
                ProjectInfo projectInfo = projectApi.findById(Long.valueOf(bizId));
                column.put("data", projectInfo);
            }
        });
        return page;
    }

    /**
     * 分组查询工时填报记录
     *
     * @param pageParams 分组类别
     * @param pageParams    筛选条件
     * @return 分组key列表
     */
    @ApiOperation(value = "分组查询工时填报记录和分摊", notes = "分组查询工时填报记录和分摊")
    @PostMapping("/allocation/group")
    public IPage<WorkingHoursGroup> groupInfosAndAllocation(
            @RequestBody PageParams<WorkingHoursPageQuery> pageParams) {

        IPage<WorkingHoursGroup> page = null;
        WorkingHoursPageQuery model = pageParams.getModel();
        String group = model.getGroupBy();
        if (StringUtil.isEmpty(group)) {
            throw BizException.validFail("当前分组仅支持 填报人(filledBy)、填报时间(fillingTime) 、项目(project)分组");
        }
        switch (group) {
            case FILLING_TIME:
                page = baseService.getPageWorkingHoursInfosAndAllocationGroupByFillingTime(pageParams.buildPage(), model);
                break;
            case FILLED_BY:
                page = baseService.getPageWorkingHoursInfosAndAllocationGroupByFillingBy(pageParams.buildPage(), model);
                break;
            case PROJECT:
                page = baseService.getPageWorkingHoursInfosAndAllocationGroupByProject(pageParams.buildPage(), model);
                break;
            default:
                throw BizException.validFail("当前分组仅支持 填报人(filledBy)、填报时间(fillingTime) 、项目(project)分组");
        }

        page.getRecords().forEach(this::echoGroupData);
        return page;
    }

    @PostMapping(value="/export")
    @ApiOperation(value = "导出", notes = "导出")
    public void export(@RequestBody WorkingHoursPageQuery query, HttpServletResponse response) throws IOException
    {
        dealQueryParam(query);
        List<WorkingHoursInfoAllocationVO> infos = baseService.getWorkingHoursInfosAndAllocation(query);
        if (CollUtil.isEmpty(infos)) {
            return;
        }
        echoService.action(infos);
        workItemService.extendWorkItem(infos);
        List<WorkingHoursInfoExport> infoExports = infos.stream().map(info ->
        {
            WorkingHoursInfoExport infoExport = new WorkingHoursInfoExport();
            Map<String, Object> echoMap = info.getEchoMap();
            infoExport.setFillingTime(info.getFillingTime());
            ProjectInfo project = (ProjectInfo) echoMap.get("projectId");
            if (project != null) {
                infoExport.setProjectName(project.getName());
            }
            ProjectInfo sourceProject = (ProjectInfo) echoMap.get("sourceProjectId");
            if (sourceProject != null) {
                infoExport.setSourceProjectName(sourceProject.getName());
            }
            infoExport.setType(info.getType().getDesc());
            Map<String,Object> biz = (Map<String, Object>) echoMap.get("biz");
            if (biz != null)
            {
                infoExport.setName((String) biz.get("code") + StrPool.SPACE + (String) biz.get("name"));
            }else {
                infoExport.setName(info.getName());
            }
            infoExport.setDescription(info.getDescription());
            infoExport.setDuration(info.getDuration());
            infoExport.setCreateTime(info.getCreateTime());
            if (info.getValid()!=null&&!info.getValid()){
                infoExport.setApprovalStatus("已驳回");
            }else if(info.getVerified()!=null&&info.getVerified()){
                infoExport.setApprovalStatus("已确认");
            }else {
                infoExport.setApprovalStatus("未审批");
            }
            User user = (User) echoMap.get("filledBy");
            if (user != null)
            {
                infoExport.setFilledBy(user.getName());
            }
            if (info.getSource()==2){
                infoExport.setSource("工时分摊");
                User createdBy = (User) echoMap.get("createdBy");
                if (createdBy!=null){
                    infoExport.setAllocationBy(createdBy.getName());
                }
            }else {
                infoExport.setSource("项目填报");
            }
            return infoExport;
        }).collect(Collectors.toList());
        ExportParams exportParams = new ExportParams(null, "工时日志");
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, WorkingHoursInfoExport.class, infoExports);

        try
        {
            ExcelDownLoadUtil.export(response, workbook, "工时日志"+System.currentTimeMillis()+".xls");
        }
        catch (IOException e)
        {
            log.error("导出文件失败，原因:{}", e.getMessage(), e);
        }
    }

    @ApiOperation(value = "批量审批工时记录", notes = "批量审批工时记录")
    @PostMapping("/allConfirm")
    public R<Boolean> allConfirm(@RequestBody WorkingHoursPageQuery query) {
        WorkingHoursInfo param = BeanPlusUtil.copyProperties(query, WorkingHoursInfo.class);
        param.setValid(null);
        param.setOrgId(null);
        QueryWrap<WorkingHoursInfo> wrapper = Wraps.q(param);
        wrapper.lambda().select(WorkingHoursInfo::getId).eq(WorkingHoursInfo::getVerified,0);

        PeriodQuery.appendQuery(wrapper.lambda(), query.getFillingTime(), WorkingHoursInfo::getFillingTime);

        Long orgId = query.getOrgId();
        if (orgId != null) {
            List<Org> childrenOrgList = orgApi.findChildrenOrgList(orgId);
            if (CollUtil.isNotEmpty(childrenOrgList)) {
                List<Long> orgIds = childrenOrgList.stream().map(SuperEntity::getId).collect(Collectors.toList());
                orgIds.add(orgId);
                wrapper.lambda().in(WorkingHoursInfo::getOrgId, orgIds);
            } else {
                wrapper.lambda().eq(WorkingHoursInfo::getOrgId, orgId);
            }
        }
        List<WorkingHoursInfo> hoursInfos = baseService.list(wrapper);
        if (CollUtil.isEmpty(hoursInfos)) {
            return success(true);
        }
        WorkingHoursConfirmSaveDTO saveDTO = new WorkingHoursConfirmSaveDTO();
        saveDTO.setWorkingHoursIds(hoursInfos.stream().map(SuperEntity::getId).collect(Collectors.toList()));
        saveDTO.setValid(query.getValid());
        boolean update = workingHoursConfirmService.bathConfirm(saveDTO);
        return success(update);
    }

    /**
     * 补充分组数据ID的对应的实体类数据
     *
     * @param group
     */
    private void echoGroupData(WorkingHoursGroup group) {
        String type = group.getGroupType();

        Object biz = group.getBizId();
        if (biz == null) {
            return;
        }
        if (type.equals("filled_by")) {
            String bizId = biz.toString();
            User user = userApi.findUserById(Long.valueOf(bizId));
            group.setData(user);
        } else if (type.equals("project_id")) {
            String bizId = biz.toString();
            ProjectInfo projectInfo = projectApi.findById(Long.valueOf(bizId));
            group.setData(projectInfo);
        }
    }

    @Override
    public R<IPage<WorkingHoursInfo>> page(PageParams<WorkingHoursInfoPageQuery> params) {
        IPage<WorkingHoursInfo> page = params.buildPage(WorkingHoursInfo.class);
        WorkingHoursInfoPageQuery model = params.getModel();
        WorkingHoursInfo query = BeanPlusUtil.copyProperties(model, WorkingHoursInfo.class);
        query.setOrgId(null);
        if (model.getStatus()!=null){
            if (model.getStatus()==0){
                query.setVerified(false);
            } else if (model.getStatus()==1){
                query.setValid(true);
            } else if (model.getStatus()==2){
                query.setValid(false);
            }
        }
        QueryWrap<WorkingHoursInfo> wrapper = Wraps.q(query);
        wrapper.lambda()
                .ge(model.getStartTime()!=null, WorkingHoursInfo::getFillingTime, model.getStartTime())
                .le(model.getEndTime()!=null, WorkingHoursInfo::getFillingTime, model.getEndTime());
        Long orgId = params.getModel().getOrgId();
        if (orgId!=null){
            List<Org> childrenOrgList = orgApi.findChildrenOrgList(orgId);
            if (CollUtil.isNotEmpty(childrenOrgList)){
                Set<Long> orgIds = childrenOrgList.stream().map(Org::getId).collect(Collectors.toSet());
                orgIds.add(orgId);
                wrapper.lambda().in(WorkingHoursInfo::getOrgId,orgIds);
            }else {
                wrapper.lambda().eq(WorkingHoursInfo::getOrgId,orgId);
            }
        }
        baseService.page(page, wrapper);
        handlerResult(page);
        return R.success(page);
    }

    @Override
    public void handlerResult(IPage<WorkingHoursInfo> page) {
        super.handlerResult(page);
        workItemService.extendWorkItem(page);
      //  baseService.actionAllocation(page.getRecords());
        echoService.action(page);
    }

    @Override
    public R<List<WorkingHoursInfo>> query(WorkingHoursInfo data) {
        Long orgId = data.getOrgId();
        data.setOrgId(null);
        QueryWrap<WorkingHoursInfo> wrapper = Wraps.q(data);
        if (orgId!=null){
            List<Org> childrenOrgList = orgApi.findChildrenOrgList(orgId);
            if (CollUtil.isNotEmpty(childrenOrgList)){
                Set<Long> orgIds = childrenOrgList.stream().map(Org::getId).collect(Collectors.toSet());
                orgIds.add(orgId);
                wrapper.lambda().in(WorkingHoursInfo::getOrgId,orgIds);
            }else {
                wrapper.lambda().eq(WorkingHoursInfo::getOrgId,orgId);
            }
        }
        List<WorkingHoursInfo> list = getBaseService().list(wrapper);
        workItemService.extendWorkItem(list);
        echoService.action(list);
       // baseService.actionAllocation(list);
        return success(list);
    }

    @Override
    public R<WorkingHoursInfo> get(Long id) {
        WorkingHoursInfo info = getBaseService().getById(id);
        workItemService.extendWorkItem(info);
        echoService.action(info);
        return success(info);
    }

    @Override
    public R<Boolean> handlerDelete(List<Long> ids) {
        LbqWrapper<WorkingHoursInfo> queryWrapper = Wraps.lbQ();
        queryWrapper.in(SuperEntity::getId, ids);
        queryWrapper.eq(WorkingHoursInfo::getVerified, true);
        long count = baseService.count(queryWrapper);
        if (count > 0) {
            throw BizException.validFail("已确认的记录不能被删除");
        }

        return super.handlerDelete(ids);
    }

    @GetMapping("/statistics/day")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "userIds", value = "用户id",example = "1,2,3", required = true, paramType = PARAM_TYPE_QUERY)
    })
    @ApiOperation(value = "统计用户每天已填报工时", notes = "统计用户每天已填报工时")
    public R<List<WorkingHoursDay>> statisticsDay(@RequestParam LocalDate startTime, @RequestParam LocalDate endTime, @RequestParam List<Long> userIds) {

        List<WorkingHoursDay> statDayHours = baseService.getStatDayHours(userIds, startTime, endTime);
        return success(statDayHours);
    }

    /**
     * 统计视图
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计视图
     */
    @GetMapping("/statistics/type")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true, paramType = PARAM_TYPE_QUERY)
    })
    @ApiOperation(value = "统计视图-类型", notes = "统计视图-类型")
    public R<List<Pair<String, Double>>> statisticsType(
            @RequestParam(required = true) LocalDate startTime,
            @RequestParam(required = true) LocalDate endTime) {
        // 分组统计
        QueryWrap<WorkingHoursInfo> queryWrapper = Wraps.q();
        queryWrapper.select("type", "sum(duration) as duration");
        queryWrapper.groupBy("type");
        queryWrapper.between("filling_time", startTime, endTime.plusDays(1L));
        List<WorkingHoursInfo> infoList = baseService.list(queryWrapper);

        List<Pair<String, Double>> collect =
                infoList.stream().map(info -> new Pair<>(info.getType().getDesc(), info.getDuration()))
                        .collect(Collectors.toList());


        return success(collect);
    }


    /**
     * 统计视图
     *
     * @param projectId 项目ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计视图
     */
    @ApiOperation(value = "统计视图-填报人", notes = "统计视图-填报人")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "projectId", value = "项目ID", required = false, paramType = PARAM_TYPE_QUERY)
    })
    @GetMapping("/statistics/filledBy")
    public R<WorkingHoursStatisticsVO<Double>> statisticsFilledBy(
            @RequestParam(required = false) Long projectId,
            @RequestParam(required = true) LocalDate startTime,
            @RequestParam(required = true) LocalDate endTime) {
        // 分组统计
        QueryWrap<WorkingHoursInfo> queryWrapper = Wraps.q();
        queryWrapper.select("filled_by", "sum(duration) as duration");
        queryWrapper.groupBy("filled_by");
        queryWrapper.between("filling_time", startTime, endTime.plusDays(1L));
        queryWrapper.eq(projectId != null, "project_id", projectId);
        List<WorkingHoursInfo> infoList = baseService.list(queryWrapper);

        List<Long> userIds = infoList.stream().map(WorkingHoursInfo::getFilledBy).collect(Collectors.toList());
        Map<Long, User> userMap = userApi.selectByIds(userIds);

        WorkingHoursStatisticsVO<Double> statisticsVO = WorkingHoursStatisticsVO.<Double>builder()
                .name(new ArrayList<>())
                .data(new ArrayList<>())
                .build();

        infoList.forEach(info -> {
            String username = Optional.of(info)
                    .map(WorkingHoursInfo::getFilledBy)
                    .map(userMap::get)
                    .map(User::getName)
                    .orElse("null");
            statisticsVO.getName().add(username);
            statisticsVO.getData().add(info.getDuration());
        });

        return success(statisticsVO);
    }

    /**
     * 统计视图
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 统计视图
     */
    @GetMapping("/statistics/project")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间", required = true, paramType = PARAM_TYPE_QUERY),
            @ApiImplicitParam(name = "endTime", value = "结束时间", required = true, paramType = PARAM_TYPE_QUERY),
    })
    @ApiOperation(value = "统计视图-项目", notes = "统计视图-项目")
    public R<WorkingHoursStatisticsVO<WorkingHoursStatisticsVO.BarVO>> statisticsProject(
            @RequestParam(required = true) LocalDate startTime,
            @RequestParam(required = true) LocalDate endTime) {

        // 分组统计
        QueryWrap<WorkingHoursInfo> queryWrapper = Wraps.q();
        queryWrapper.select("project_id", "valid", "sum(duration) as duration");
        queryWrapper.groupBy("project_id, valid");
        queryWrapper.between("filling_time", startTime, endTime.plusDays(1L));
        List<WorkingHoursInfo> infoList = baseService.list(queryWrapper);

        // 整理数据格式
        Map<String, List<WorkingHoursInfo>> map = infoList.stream()
                // 过滤空项目数据
                .filter(info -> Objects.nonNull(info.getProjectId()))
                .collect(Collectors.groupingBy(this::getProjectName));

        WorkingHoursStatisticsVO.BarVO unverified = WorkingHoursStatisticsVO.BarVO.builder()
                .name("未审批").data(new ArrayList<>()).build();
        WorkingHoursStatisticsVO.BarVO valid = WorkingHoursStatisticsVO.BarVO.builder()
                .name("确认").data(new ArrayList<>()).build();
        WorkingHoursStatisticsVO.BarVO unvalid = WorkingHoursStatisticsVO.BarVO.builder()
                .name("驳回").data(new ArrayList<>()).build();

        WorkingHoursStatisticsVO<WorkingHoursStatisticsVO.BarVO> statisticsVO =
                WorkingHoursStatisticsVO.<WorkingHoursStatisticsVO.BarVO>builder()
                        .name(new ArrayList<>())
                        .data(CollUtil.newArrayList(unverified, valid, unvalid))
                        .build();

        map.forEach((projectName, list) -> {
            statisticsVO.getName().add(projectName);
            double unverifiedSum = list.stream()
                    .filter(info -> info.getValid() == null)
                    .mapToDouble(WorkingHoursInfo::getDuration).sum();
            unverified.getData().add(unverifiedSum);


            double validSum = list.stream()
                    .filter(info -> info.getValid() != null)
                    .filter(WorkingHoursInfo::getValid)
                    .mapToDouble(WorkingHoursInfo::getDuration).sum();
            valid.getData().add(validSum);

            double unvalidSum = list.stream()
                    .filter(info -> info.getValid() != null)
                    .filter(info -> !info.getValid())
                    .mapToDouble(WorkingHoursInfo::getDuration).sum();
            unvalid.getData().add(unvalidSum);

        });
        return success(statisticsVO);

    }

    @ApiOperation(value = "项目研发度量分页", notes = "项目研发度量分页")
    @SysLog(value = "项目研发度量工时填报分页")
    @PostMapping("/findCountHourPageByQuery")
    public R<IPage<WorkingHoursInfo>> findCountHourPageByQuery(@RequestBody PageParams<TaskCountQuery> query) {
        query.getModel().setQueryType(1);
        IPage<WorkingHoursInfo> page = baseService.findCountHourPageByQuery(query);
        page.getRecords().forEach(item ->
        {
            item.setSourceType(item.getType());
        });
        handlerResult(page);
        return success(page);
    }
    @ApiOperation(value = "项目研发度量分页", notes = "项目研发度量分页")
    @SysLog(value = "项目研发度量工时填报分页")
    @PostMapping("/findComponentCountHourPageByQuery")
    public R<IPage<WorkingHoursInfo>> findComponentCountHourPageByQuery(@RequestBody PageParams<TaskCountQuery> query) {
        query.getModel().setQueryType(2);
        IPage<WorkingHoursInfo> page = baseService.findCountHourPageByQuery(query);
        page.getRecords().forEach(item ->
        {
            item.setSourceType(item.getType());
        });
        handlerResult(page);
        return success(page);
    }

    private String getProjectName(WorkingHoursInfo info) {
        return Optional.ofNullable(info)
                .map(WorkingHoursInfo::getProjectId)
                .map(projectApi::findById)
                .map(ProjectInfo::getName)
                .orElse("null");
    }

    private void dealQueryParam(WorkingHoursPageQuery query){
        if (query.getOrgId()!=null){
            List<Org> childrenOrgList = orgApi.findChildrenOrgList(query.getOrgId());
            if (CollUtil.isNotEmpty(childrenOrgList)){
                List<Long> orgIds = childrenOrgList.stream().map(Org::getId).distinct().collect(Collectors.toList());
                query.setOrgIds(orgIds);
            } else {
                query.setOrgIds(Arrays.asList(query.getOrgId()));
            }
        }
        if (query.getStatus()!=null){
            if (query.getStatus()==0){
                query.setVerified(false);
            } else if (query.getStatus()==1){
                query.setValid(true);
            } else if (query.getStatus()==2){
                query.setValid(false);
            }
        }
    }
}
