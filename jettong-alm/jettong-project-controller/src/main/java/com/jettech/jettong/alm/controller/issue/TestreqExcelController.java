package com.jettech.jettong.alm.controller.issue;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.jettong.alm.issue.dto.RequirementExportQuery;
import com.jettech.jettong.alm.issue.entity.Idea;
import com.jettech.jettong.alm.issue.entity.Requirement;
import com.jettech.jettong.alm.issue.entity.Testreq;
import com.jettech.jettong.alm.issue.entity.Type;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.issue.poi.JettongExportHandler;
import com.jettech.jettong.alm.issue.poi.issue.TestreqDictHandler;
import com.jettech.jettong.alm.issue.service.IdeaService;
import com.jettech.jettong.alm.issue.service.TestreqService;
import com.jettech.jettong.alm.issue.service.TypeService;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.alm.project.service.ProjectInfoService;
import com.jettech.jettong.alm.project.service.ProjectWorkflowService;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;
import com.jettech.jettong.base.entity.sys.form.CustomForm;
import com.jettech.jettong.common.util.poi.ExcelDownLoadUtil;
import com.jettech.jettong.common.util.poi.ExcelExportPlusUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.jettech.jettong.common.constant.SwaggerConstants.DATA_TYPE_LONG;
import static com.jettech.jettong.common.constant.SwaggerConstants.PARAM_TYPE_QUERY;

/**
 * 测试需求导入导出信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong1
 * @package com.jettech.jettong.alm.controller.issue
 * @className TestreqExcelController
 * @date 2025/7/10 10:33
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/alm/testreq/excel")
@Api(value = "Testreq", tags = "测试需求导入导出")
@RequiredArgsConstructor
public class TestreqExcelController extends
        SuperSimpleController<TestreqService, Testreq>
{

    private final TestreqDictHandler testreqDictHandler;
    private final TypeService typeService;
    private final IdeaService ideaService;
    private final ProjectInfoService projectInfoService;
    private final ProjectWorkflowService projectWorkflowService;

    @ApiOperation(value = "导出Excel")
    @PostMapping(value = "/export", produces = "application/octet-stream")
    @SysLog(value = "'导出Excel", optType = OptLogTypeEnum.EXPORT)
    public void exportExcel(@Validated @RequestBody RequirementExportQuery model, HttpServletResponse response)
    {
        // 1.查询导出数据
        LbqWrapper<Testreq> wrapper = Wraps.lbQ();

        wrapper.like(Testreq::getCode, model.getCode())
                .like(Testreq::getName, model.getName())
                .in(Testreq::getParentId, model.getParentId())
                .in(Testreq::getStateCode, model.getStateCode())
                .in(Testreq::getPriorityCode, model.getPriorityCode())
                .in(Testreq::getTypeCode, model.getTypeCode())
                .eq(Testreq::getDelay, model.getDelay())
                .in(Testreq::getProductVersionId, model.getProductVersionId())
                .in(Testreq::getProgramId, model.getProgramId())
                .in(Testreq::getProjectId, model.getProjectId())
                .in(Testreq::getProductId, model.getProductId())
                .in(Testreq::getPutBy, model.getPutBy())
                .in(Testreq::getLeadingBy, model.getLeadingBy())
                .in(Testreq::getHandleBy, model.getHandleBy())
                .in(Testreq::getSourceCode, model.getSourceCode());
        if (null != model.getParentId() && 1 == model.getParentId().size() &&
                Convert.toLong(-1).equals(model.getParentId().get(0)))
        {
            wrapper.isNull(Testreq::getParentId);
        } else {
            wrapper.in(Testreq::getParentId, model.getParentId());
        }

        List<Testreq> list = baseService.list(wrapper);

        // 将需求按事项类型分组
        Map<String, List<Testreq>> groupTypeCode =
                list.stream().collect(Collectors.groupingBy(Testreq::getTypeCode));

        // 2.动态生成sheet
        // 2.1 查询平台所有需求类型
        // 获取需求类型对应的自定义表单
        List<Long> projectId = model.getProjectId();
        List<Long> programId = model.getProgramId();
        Map<Type, CustomForm> typeCustomFormIdMap = JettongExportHandler.getTypeCustomFormIdMapFromProjectOrProgram(
                TypeClassify.TESTREQ, projectId, programId);

        // 2.2 数据库分类整理成 easy-poi 的格式
        List<Map<String, Object>> sheets = JettongExportHandler.excelExportSheet(
                typeCustomFormIdMap, groupTypeCode, testreqDictHandler);

        // 3.执行excel导出
        Workbook workbook = ExcelExportPlusUtil.exportExcel(sheets);

        testreqDictHandler.clear();

        try
        {
            ExcelDownLoadUtil.export(response, workbook, "需求信息.xls");
        }
        catch (IOException e)
        {
            log.error("导出文件失败，原因:{}", e.getMessage(), e);
        }
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_QUERY)
    })
    @ApiOperation(value = "下载需求导入模板")
    @GetMapping(value = "/downloadImportTemplate", produces = "application/octet-stream")
    @SysLog(value = "下载需求导入模板", optType = OptLogTypeEnum.OTHER)
    public void downloadImportTemplate(Long projectId, HttpServletResponse response)
    {
        // 获取需求类型对应的自定义表单
        Map<Type, CustomForm> typeCustomFormIdMap = JettongExportHandler.getTypeCustomFormIdMapFromProjectOrProgram(
                TypeClassify.TESTREQ, CollUtil.newArrayList(projectId), null);

        Map<String, String[]> dropMap = testreqDictHandler.dropMap(projectId);
        dropMap.put("parentId", getRequirementCodeAndNameArray(projectId));
        dropMap.put("typeCode", gettypeCodeeArray());
        dropMap.put("ideaId", getIdeaCodeAndNameArray());
        Workbook workbook = JettongExportHandler.importTemplate(
                typeCustomFormIdMap, testreqDictHandler, dropMap);

        testreqDictHandler.clear();
        // 下载导入模板
        try
        {
            ExcelDownLoadUtil.export(response, workbook, "需求导入模板.xls");
        }
        catch (IOException e)
        {
            log.error("下载需求导入模板失败，原因:{}", e.getMessage(), e);
        }
    }
    private String[] getRequirementCodeAndNameArray(Long projectId)
    {
        List<Testreq> testreqs =
                baseService.list(Wraps.<Testreq>lbQ().eq(Testreq::getProjectId, projectId));

        return testreqs.stream().map(item -> item.getCode() + " " + item.getName()).toArray(String[]::new);
    }

    private String[] gettypeCodeeArray()
    {
        List<Type> types = typeService.list(Wraps.<Type>lbQ().eq(Type::getClassify, "TESTREQ"));
        return types.stream().map(Type::getName).toArray(String[]::new);
    }
    private String[] getIdeaCodeAndNameArray()
    {
        List<Idea> ideas = ideaService.list();
        return ideas.stream().map(item -> item.getCode() + " " + item.getName()).toArray(String[]::new);
    }

    @ApiOperation(value = "导入Excel, 错误excel在extra中，fileName为文件名，fileStream为错误excel的base64编码字符串")
    @PostMapping(value = "/import")
    @SysLog(value = "'导入Excel:' + #simpleFile?.originalFilename", request = false, optType = OptLogTypeEnum.IMPORT)
    public R<Boolean> importExcel(@RequestParam(value = "file") MultipartFile simpleFile,
            @RequestParam(value = "projectId", required = false) Long importProjectId) throws Exception
    {
        // todo

        // 获取缺陷类型对应的自定义表单
        Map<Type, CustomForm> typeCustomFormIdMap = JettongExportHandler.getTypeCustomFormIdMapFromProjectOrProgram(
                TypeClassify.TESTREQ, CollUtil.newArrayList(importProjectId), null);
        List<Type> types = typeService.list(Wraps.<Type>lbQ().eq(Type::getClassify, TypeClassify.TESTREQ));
        Map<String, String> typeCodePrefix = types.stream().collect(Collectors.toMap(Type::getCode, Type::getPrefix));

        R<Boolean> r = success();

        // 读取excel文件，并封装返回前端响应结果
        List<Map<String, String>> allMapList = JettongExportHandler.importExcel(simpleFile,
                typeCustomFormIdMap, testreqDictHandler, r);

        // 清理ThreadLocal
        testreqDictHandler.clear();

        if (!allMapList.isEmpty())
        {
            // 获取所有项目信息
            List<ProjectInfo> projectInfos = projectInfoService.list();
            Map<Long, Long> projectIdProgramId = projectInfos.stream().filter(item -> item.getProgramId() != null)
                    .collect(Collectors.toMap(ProjectInfo::getId, ProjectInfo::getProgramId));

            List<Testreq> testreqList = allMapList.stream().map(item ->
            {
                Testreq testreq = new Testreq();
                BeanUtil.copyProperties(item, testreq);
                Long projectId = testreq.getProjectId();
                if (projectId != null)
                {
                    testreq.setProgramId(
                            projectIdProgramId.getOrDefault(projectId, null));
                }

                testreq.setCode(typeService.getCode(typeCodePrefix.get(testreq.getTypeCode()),TypeClassify.TESTREQ));
                testreq.setStateCode(getInitStateCode(projectId, testreq.getTypeCode()));
                testreq.setRateProgress(0);
                return testreq;
            }).collect(Collectors.toList());

            baseService.saveBatch(testreqList, 20);
        }

        return r;
    }

    private String getInitStateCode(Long projectId, String typeCode)
    {
        WorkflowNode workflowNode =
                projectWorkflowService.findProjectFirstNode(projectId, TypeClassify.TESTREQ,
                        typeCode);
        if (workflowNode == null)
        {
            return "";
        }
        return workflowNode.getStateCode();
    }
}
