package com.jettech.jettong.alm.controller.project;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.database.mybatis.conditions.update.LbuWrapper;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.StrPool;
import com.jettech.basic.utils.TreeUtil;
import com.jettech.jettong.alm.api.StateApi;
import com.jettech.jettong.alm.issue.entity.Bug;
import com.jettech.jettong.alm.issue.entity.State;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.issue.service.BugService;
import com.jettech.jettong.alm.project.dto.*;

import com.jettech.jettong.alm.project.entity.*;
import com.jettech.jettong.alm.project.enumeration.ProjectStageEnum;

import com.jettech.jettong.alm.project.service.*;
import com.jettech.jettong.alm.project.vo.ProjectDetailComponentResult;
import com.jettech.jettong.alm.project.vo.ProjectWorkItemBaseResult;
import com.jettech.jettong.base.api.DictionaryApi;
import com.jettech.jettong.base.api.PersonalizedTableViewApi;
import com.jettech.jettong.base.dto.sys.dictionary.SysDictionaryRelationSaveDTO;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.base.enumeration.dictionary.DictionaryRelationBizType;
import com.jettech.jettong.common.constant.DictionaryType;
import com.jettech.jettong.product.api.ProductInfoApi;
import com.jettech.jettong.product.entity.ProductInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
import java.util.stream.Collectors;
import static com.jettech.jettong.common.constant.SwaggerConstants.*;


/**
 * 项目信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 项目信息控制器
 * @projectName jettong
 * @package com.jettech.jettong.alm.controller.project
 * @className ProjectInfoController
 * @date 2021-11-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/alm/project")
@Api(value = "ProjectInfo", tags = "项目信息")
@RequiredArgsConstructor
public class ProjectInfoController extends
        SuperController<ProjectInfoService, Long, ProjectInfo, ProjectInfoPageQuery, ProjectInfoSaveDTO,
                ProjectInfoUpdateDTO>
{

    private final EchoService echoService;
    private final ProjectUserService projectUserService;
    private final ProjectProductService projectProductService;
    private final ProjectProductsetService projectProductsetService;
    private final ProjectInfoProjectService projectInfoProjectService;
    private final PersonalizedTableViewApi tableViewApi;
    private final ProjectUserRoleService projectUserRoleService;
    private final ProjectRoleService projectRoleService;
    private final BugService bugService;
    private final DictionaryApi dictionaryApi;
    private final ProductInfoApi productInfoApi;
    private final StateApi stateApi;
    private final ProjectPlanService projectPlanService;
    @Override
    public R<ProjectInfo> handlerSave(ProjectInfoSaveDTO model)
    {
        ProjectInfo project = BeanPlusUtil.toBean(model, ProjectInfo.class);
        if (null == project.getOrgId())
        {
            project.setOrgId(getOrgId());
        }
        List<String> testModes = project.getTestModes();
        if(!project.getTestModes().isEmpty()){
            //子平台赋值
            int sum = testModes.stream()
                    .mapToInt(Integer::parseInt)
                    .sum();
            project.setTestMode(String.valueOf(sum));
        } else {
            project.setTestMode("0");
        }
        baseService.saveProject(project);
        //添加项目和项目关联关系
        if (CollUtil.isNotEmpty(model.getProjectIds()))
        {
            List<ProjectInfoProject> projectInfoProjects =
                    model.getProjectIds().stream().map(item -> new ProjectInfoProject(project.getId(), item))
                            .collect(Collectors.toList());
            projectInfoProjectService.saveBatch(projectInfoProjects);
        }
        SysDictionaryRelationSaveDTO sysDictionaryRelation = new SysDictionaryRelationSaveDTO()
                .setBizId(project.getId())
                .setBizType(DictionaryRelationBizType.PROJECT.getCode())
                .setDictionaryType(DictionaryType.PROJECT_TESTMODE)
                .setDictionaryCodes(project.getTestModes());
        dictionaryApi.saveOrUpdateDictionaryRelation(sysDictionaryRelation);
        return success(project);
    }

    @ApiOperation(value = "项目通过审批", notes = "项目通过审批")
    @PutMapping("/passApproval")
    public R<Boolean> passApproval(@RequestBody ProjectInfo projectInfo)
    {

        Long id = projectInfo.getId();

        LbuWrapper<ProjectInfo> wrapper = Wraps.lbU();
        wrapper.set(ProjectInfo::getProjectStage, ProjectStageEnum.RUNNING);
        wrapper.eq(SuperEntity::getId, id);
        baseService.update(wrapper);
        return success();
    }

    @Override
    public R<Boolean> handlerDelete(List<Long> ids)
    {
        if (CollUtil.isEmpty(ids))
        {
            throw BizException.wrap(400, "id不能为空");
        }
        baseService.deleteProjectByIds(ids);
        return success();
    }

    @Override
    public R<ProjectInfo> handlerUpdate(ProjectInfoUpdateDTO model)
    {
        ProjectInfo project = BeanPlusUtil.toBean(model, ProjectInfo.class);
        List<String> testModes = model.getTestModes();
        if (!testModes.isEmpty()) {
            // 保存字典关系
            SysDictionaryRelationSaveDTO sysDictionaryRelation = new SysDictionaryRelationSaveDTO()
                    .setBizId(project.getId())
                    .setBizType(DictionaryRelationBizType.PROJECT.getCode())
                    .setDictionaryType(DictionaryType.PROJECT_TESTMODE)
                    .setDictionaryCodes(testModes);
            dictionaryApi.saveOrUpdateDictionaryRelation(sysDictionaryRelation);
            //子平台赋值
            int sum = testModes.stream()
                    .mapToInt(Integer::parseInt)
                    .sum();
            project.setTestMode(String.valueOf(sum));
        } else {
            project.setTestMode("0");
        }
        baseService.updateProject(project);
        projectInfoProjectService.remove(
                Wraps.<ProjectInfoProject>lbQ().eq(ProjectInfoProject::getProjectId, project.getId()));
        //添加项目和项目关联关系
        if (CollUtil.isNotEmpty(model.getProjectIds()))
        {
            List<ProjectInfoProject> projectInfoProjects =
                    model.getProjectIds().stream().map(item -> new ProjectInfoProject(project.getId(), item))
                            .collect(Collectors.toList());
            projectInfoProjectService.saveBatch(projectInfoProjects);
        }

        return success(project);
    }

    @ApiOperation(value = "项目结项", notes = "项目结项")
    @PutMapping("/closure")
    @SysLog(value = "'项目结项:' + #updateDTO?.id", request = false, optType = OptLogTypeEnum.EDIT)
    @PreAuth("hasAnyPermission('{}edit')")
    public R<Boolean> closureProject(@Validated @RequestBody ProjectInfoUpdateDTO model)
    {
        ProjectInfo projectInfo = ProjectInfo.builder().id(model.getId()).isClosure(true).build();
        return success(baseService.updateById(projectInfo));
    }

    @Override
    public R<ProjectInfo> get(Long id)
    {
        ProjectInfo projectInfo = baseService.findById(id);
        echoService.action(projectInfo);
        projectProductService.actionProduct(projectInfo);
        List<ProjectInfoProject> projectInfoProjects = projectInfoProjectService.list(
                Wraps.<ProjectInfoProject>lbQ().eq(ProjectInfoProject::getProjectId, id));
        if (CollUtil.isNotEmpty(projectInfoProjects))
        {
            List<Long> projectIds = projectInfoProjects.stream().map(ProjectInfoProject::getAssociatedProjectId)
                    .collect(Collectors.toList());
            projectInfo.setProjects(baseService.listByIds(projectIds));
        }
        if (null != projectInfo)
        {
            projectInfo.setProjectProductsets(projectProductsetService.list(
                    Wraps.<ProjectProductset>lbQ().eq(ProjectProductset::getProjectId, id)));
        }

        //回显
        List<Dictionary> dictionaries = dictionaryApi.echoDictionaryByBizIdAndType(
                id, DictionaryRelationBizType.PROJECT.getCode());
        if (dictionaries != null && !dictionaries.isEmpty()) {
//            List<String> testModes = dictionaries.stream()
//                    .map(Dictionary::getCode)
//                    .collect(Collectors.toList());
           // projectInfo.setTestModes(testModes);
            projectInfo.getEchoMap().put("testModes",dictionaries);
        }
        return success(projectInfo);
    }

    @Override
    public IPage<ProjectInfo> query(PageParams<ProjectInfoPageQuery> params)
    {
        IPage<ProjectInfo> page = params.buildPage(ProjectRole.class);
        ProjectInfoPageQuery projectPage = params.getModel();

        // 置顶排序
        page.orders().add(0, OrderItem.desc("`top`"));
        page.orders().add(1, OrderItem.desc("pt.create_time"));
        // 收藏后不改变排序
//        page.orders().add(2, OrderItem.desc("collection"));

        Map<String, Object> map = BeanPlusUtil.beanToMap(projectPage);
        if (null == projectPage.getProductsetId() && null == projectPage.getProductId() && null == projectPage.getUserId())
        {
            map.put("userId", getUserId());
        }

        if (StrUtil.isNotEmpty(projectPage.getClassify()))
        {
            map.put("classify", projectPage.getClassify().split(StrPool.COMMA));
        }
        baseService.findPage(page, map);
        echoService.action(page);
        baseService.actionProject(page.getRecords());
        projectProductService.actionProduct(page.getRecords());
        // 保存最后一次查询的视图
        tableViewApi.saveLastSearch(getUserId(), "project-table", params);
        return page;
    }

    @Override
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编号，模糊匹配", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "name", value = "名称，模糊匹配", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "typeCode", value = "项目模式", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "classify", value = "项目类型", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "programId", value = "项目集id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "productId", value = "产品id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "userId", value = "用户id,不传默认查询当前登录用户", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY)
    })
    public R<List<ProjectInfo>> query(ProjectInfo projectInfo)
    {
        Map<String, Object> map = BeanPlusUtil.beanToMap(projectInfo);
        if (projectInfo.getUserId() == null)
        {
            map.put("userId", getUserId());
        }
        if (StrUtil.isNotEmpty(projectInfo.getClassify()))
        {
            map.put("classify", projectInfo.getClassify().split(StrPool.COMMA));
        }
        List<ProjectInfo> list = baseService.findByParam(map);
        echoService.action(list);
        projectProductService.actionProduct(list);
        baseService.actionProject(list);
        return success(list);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码", required = true, dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_QUERY)
    })
    @ApiOperation(value = "检测编码是否可用", notes = "检测编码是否可用")
    @GetMapping("/check")
    @SysLog(value = "检测编码是否可用", request = false)
    public R<Boolean> check(@RequestParam("code") String code)
    {
        return success(
                baseService.check(code));
    }

    @ApiOperation(value = "查询可以关联的项目", notes = "查询可以关联的项目")
    @PostMapping("/getProjectInfoByProgramIdIsEmpty")
    @SysLog(value = "查询可以关联的项目", optType = OptLogTypeEnum.QUERY)
    public R<List<ProjectInfo>> getProjectInfoByProgramIdIsEmpty(@RequestBody ProjectInfo projectInfo)
    {
        return success(baseService.getProjectInfoByProgramIdIsEmpty(projectInfo));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @ApiOperation(value = "查询项目基本信息组件", notes = "查询项目基本信息组件")
    @GetMapping("/findDetailByProjectId/{projectId}")
    @SysLog("查询项目基本信息组件")
    public R<ProjectDetailComponentResult> findDetailByProjectId(@PathVariable("projectId") Long projectId)
    {
        return R.success(baseService.findDetailByProjectId(projectId));
    }


    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编号，模糊匹配", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "name", value = "名称，模糊匹配", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "typeCode", value = "项目类型Code", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "programId", value = "项目集id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "productId", value = "产品id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "userId", value = "用户id,不传默认查询当前登录用户", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY)
    })
    @ApiOperation(value = "条件查询项目列表无权限", notes = "条件查询项目列表无权限")
    @PostMapping("/findProjectInfoByCondition")
    @SysLog("条件查询项目列表无权限")
    public R<List<ProjectInfo>> findProjectInfoByCondition(@RequestBody ProjectInfo projectInfo)
    {
        Map<String, Object> map = BeanPlusUtil.beanToMap(projectInfo);
        List<ProjectInfo> list = baseService.findByParam(map);
        echoService.action(list);
        projectProductService.actionProduct(list);
        return success(list);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "name", value = "名称，模糊匹配", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "code", value = "标识，模糊匹配", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY)
    })
    @ApiOperation(value = "下拉条件查询项目列表无权限", notes = "下拉条件查询项目列表无权限")
    @PostMapping("/queryListByCondition")
    @SysLog("下拉条件查询项目列表无权限")
    public R<List<ProjectInfo>> queryListByCondition(@ApiIgnore @RequestBody ProjectInfo projectInfo)
    {
        LbqWrapper<ProjectInfo> wrapper = Wraps.lbQ();
        wrapper.select(ProjectInfo::getId, ProjectInfo::getCode, ProjectInfo::getName)
                .like(ProjectInfo::getName, projectInfo.getName())
                .like(ProjectInfo::getCode, projectInfo.getCode())
                .ne(projectInfo.getId()!=null, ProjectInfo::getId, projectInfo.getId())
                .orderByDesc(ProjectInfo::getUpdateTime);
        wrapper.last(" limit 50");
        List<ProjectInfo> list = baseService.list(wrapper);

        List<ProjectInfo> listProject = new ArrayList<>();
        if(projectInfo.getId()!=null){
            LbqWrapper<ProjectInfo> lbqWrapper = Wraps.lbQ();
            lbqWrapper.select(ProjectInfo::getId, ProjectInfo::getCode, ProjectInfo::getName)
                    .eq(ProjectInfo::getId, projectInfo.getId());
            listProject = baseService.list(lbqWrapper);
        }
        List<ProjectInfo> finalList = new ArrayList<>();

        finalList.addAll(listProject);
        finalList.addAll(list);
        return success(finalList);
    }


    @GetMapping("/auth/check")
    @ApiOperation(value = "校验当前用户是否有权限访问该项目")
    public R<Boolean> checkAuth(@RequestParam Long projectId)
    {
        Long userId = ContextUtil.getUserId();
        if (projectId == null || userId == null)
        {
            return fail("参数错误");
        }
        LbqWrapper<ProjectUser> wrapper = Wraps.<ProjectUser>lbQ()
                .eq(ProjectUser::getUserId, userId)
                .eq(ProjectUser::getProjectId, projectId);
        long count = projectUserService.count(wrapper);
        return R.success(count > 0);
    }

    @ApiOperation(value = "根据项目id集合和工作项分类集合查询项目工作项信息", notes = "根据项目id集合和工作项分类集合查询项目工作项信息")
    @PostMapping("/getProjectWorkItemByProjectIdAndTypeClassify")
    @SysLog(value = "根据项目id集合和工作项分类集合查询项目工作项信息", optType = OptLogTypeEnum.QUERY)
    public IPage<ProjectWorkItemBaseResult> getProjectWorkItemByProjectIdAndTypeClassify(@RequestBody
            PageParams<ProjectWorkItemBasePageQuery> params)
    {
        IPage<ProjectWorkItemBaseResult> page = params.buildPage(ProjectWorkItemBaseResult.class);
        ProjectWorkItemBasePageQuery query = params.getModel();
        List<TypeClassify> typeClassifies = query.getTypeClassifies();
        // 如果为空时，查询需求、任务和缺陷工作项
        if (null == typeClassifies || typeClassifies.isEmpty())
        {
            typeClassifies.add(TypeClassify.ISSUE);
            typeClassifies.add(TypeClassify.TASK);
            typeClassifies.add(TypeClassify.BUG);
        }

        // 删除不属于需求、任务和缺陷工作项的工作项分类
        typeClassifies.removeIf(typeClassify -> !TypeClassify.ISSUE.equals(typeClassify)
                && !TypeClassify.TASK.equals(typeClassify)
                && !TypeClassify.BUG.equals(typeClassify));


        baseService.getProjectWorkItemBaseByProjectIdAndHandleBy(page, query.getProjectIds(), typeClassifies,
                getUserId());

        echoService.action(page);

        return page;

    }

    @ApiOperation(value = "收藏项目", notes = "收藏项目")
    @PutMapping("/collect")
    @SysLog(value = "收藏项目")
    public R<Boolean> collect(@RequestParam Long projectId, @RequestParam Boolean collection)
    {
        baseService.collect(projectId, getUserId(), collection);
        return success();
    }

    @ApiOperation(value = "置顶项目", notes = "置顶项目")
    @PutMapping("/top")
    @SysLog(value = "置顶项目")
    public R<Boolean> top(@RequestParam Long projectId, @RequestParam Boolean top)
    {
        baseService.top(projectId, getUserId(), top);
        return success();
    }

    @ApiOperation(value = "一键添加/移除项目的项目经理", notes = "一键添加/移除项目的项目经理")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userIds", value = "要添加/移除的用户id", required = true , dataType = DATA_TYPE_LONG_ARRAY,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "opt", value = "操作，add-添加，del-移除", required = true, dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_PATH)
    })
    @PutMapping("/batchUserToProject/{opt}")
    @SysLog(value = "一键添加/移除项目的项目经理")
    public R<Boolean> batchUserToProject(@RequestBody List<Long> userIds, @PathVariable("opt") String opt)
    {
        if (userIds.isEmpty())
        {
            return fail("非法参数");
        }
        // 添加
        if ("add".equals(opt))
        {
            // 获取所有项目
            List<ProjectInfo> projectInfos = baseService.list();

            // 获取所有项目的项目经理
            List<ProjectUserRole> projectUserRoles = projectUserRoleService.list(Wraps.<ProjectUserRole>lbQ().exists("select 1 from project_role where `code` = 'PM' and id = role_id"));

            Map<String, ProjectUserRole> projectIdUserIdMap = projectUserRoles.stream().collect(Collectors.toMap(item -> item.getProjectId() + "_" + item.getUserId(), item -> item, (existing, replacement) -> existing));

            // 获取项目经理角色
            List<ProjectRole> projectRoles = projectRoleService.list(Wraps.<ProjectRole>lbQ().eq(ProjectRole::getCode, "PM"));

            Map<String, Long> projectTypePMId = projectRoles.stream().collect(Collectors.toMap(ProjectRole::getTypeCode,
                    ProjectRole::getId));

            List<ProjectUserRole> addProjectUserRoles = new ArrayList<>();
            for (ProjectInfo projectInfo : projectInfos)
            {
                for (Long userId : userIds)
                {
                    if (!projectIdUserIdMap.containsKey(projectInfo.getId() + "_" + userId))
                    {
                        addProjectUserRoles.add(ProjectUserRole.builder()
                                        .projectId(projectInfo.getId())
                                        .userId(userId)
                                        .type(true)
                                        .roleId(projectTypePMId.getOrDefault(projectInfo.getTypeCode(), null))
                                .build());
                    }
                }
            }
            if (!addProjectUserRoles.isEmpty())
            {
                projectUserRoleService.saveBatch(addProjectUserRoles, 20);
            }

            return success();
        }
        // 移除
        else if ("del".equals(opt))
        {
            projectUserRoleService.remove(Wraps.<ProjectUserRole>lbQ().isNull(ProjectUserRole::getTeamId)
                    .in(ProjectUserRole::getUserId, userIds)
                    .eq(ProjectUserRole::getType, true)
                    .exists("select 1 from project_role where `code` = 'PM' and id = role_id"));
            return success();
        }

        return fail("非法参数");
    }
    @ApiOperation(value = "分页查询项目并以层级结构返回", notes = "分页查询项目并以层级结构返回")
    @PostMapping("/tree")
    @SysLog(value = "分页查询项目并以层级结构返回", optType = OptLogTypeEnum.QUERY)
    public R<IPage<ProjectTreeDto>> getProjectTree(@RequestBody PageParams<ProjectInfoPageQuery> params)
    {
        // 使用原有的查询方法获取分页数据
        IPage<ProjectInfo> page = this.query(params);

        // 将ProjectInfo转换为ProjectTreeDto
        List<ProjectTreeDto> treeDtos = page.getRecords().stream()
                .map(ProjectTreeDto::fromProjectInfo)
                .collect(Collectors.toList());

        // 构建树形结构
        List<ProjectTreeDto> treeList = TreeUtil.buildTreeDto(treeDtos);

        // 创建新的分页对象，保留原分页信息，但替换内容为树形结构
        IPage<ProjectTreeDto> treePageResult = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(
                page.getCurrent(), page.getSize(), page.getTotal());
        treePageResult.setRecords(treeList);

        // 保存最后一次查询的视图
        tableViewApi.saveLastSearch(getUserId(), "project-hierarchy-table", params);

        return success(treePageResult);
    }

    @ApiOperation(value = "分页查询父项目列表", notes = "第一步：获取所有父项目的分页列表，包含是否有子项目的标识")
    @PostMapping("/getParentProjects")
    @SysLog(value = "分页查询父项目列表", optType = OptLogTypeEnum.QUERY)
    public R<IPage<ProjectInfo>> getParentProjects(@RequestBody PageParams<ProjectInfoPageQuery> params)
    {
        IPage<ProjectInfo> page = params.buildPage(ProjectInfo.class);
        ProjectInfoPageQuery projectPage = params.getModel();

        // 设置只查询父项目的标识
        projectPage.setParentOnly(true);

        // 置顶排序
        page.orders().add(0, OrderItem.desc("top"));
        page.orders().add(1, OrderItem.desc("pt.create_time"));

        Map<String, Object> map = BeanPlusUtil.beanToMap(projectPage);
        map.put("userId", getUserId());
        if (StrUtil.isNotEmpty(projectPage.getClassify()))
        {
            map.put("classify", projectPage.getClassify().split(StrPool.COMMA));
        }

        // 添加父项目查询条件
        map.put("parentOnly", true);

        baseService.findPage(page, map);
        echoService.action(page);
        baseService.actionProject(page.getRecords());
        projectProductService.actionProduct(page.getRecords());

        // 为每个父项目设置子项目信息
        baseService.setChildrenInfo(page.getRecords());

        // 保存最后一次查询的视图
//        tableViewApi.saveLastSearch(getUserId(), "parent-project-table", params);

        return success(page);
    }

    @ApiOperation(value = "分页查询子项目列表", notes = "第二步：根据父项目ID懒加载查询子项目列表")
    @PostMapping("/getChildProjects")
    @SysLog(value = "分页查询子项目列表", optType = OptLogTypeEnum.QUERY)
    public R<IPage<ProjectInfo>> getChildProjects(@RequestBody @Validated PageParams<ProjectInfoPageQuery> params)
    {
        IPage<ProjectInfo> page = params.buildPage(ProjectInfo.class);
        ProjectInfoPageQuery projectPage = params.getModel();

        // 验证父项目ID
        if (projectPage.getParentProjectId() == null)
        {
            throw BizException.wrap(400, "父项目ID不能为空");
        }

        // 置顶排序
        page.orders().add(0, OrderItem.desc("top"));
        page.orders().add(1, OrderItem.desc("pt.create_time"));

        Map<String, Object> map = BeanPlusUtil.beanToMap(projectPage);
        map.put("userId", getUserId());
        map.put("parentProjectId", projectPage.getParentProjectId());

        if (StrUtil.isNotEmpty(projectPage.getClassify()))
        {
            map.put("classify", projectPage.getClassify().split(StrPool.COMMA));
        }

        baseService.findPage(page, map);
        echoService.action(page);
        baseService.actionProject(page.getRecords());
        projectProductService.actionProduct(page.getRecords());

        // 保存最后一次查询的视图
        tableViewApi.saveLastSearch(getUserId(), "child-project-table", params);

        return success(page);
    }

    @ApiOperation(value = "查询所有父项目", notes = "查询项目中所有parentId为空或为0的项目")
    @GetMapping("/getAllParentProjects")
    @SysLog(value = "查询所有父项目", optType = OptLogTypeEnum.QUERY)
    public R<List<ProjectInfo>> getAllParentProjects()
    {
        Map<String, Object> map = new HashMap<>();
        map.put("userId", getUserId());
        map.put("parentOnly", true);
        // 构建查询条件：parentId为空或为0

        List<ProjectInfo> list = baseService.findByParam(map);
        
        // 执行回显和关联数据处理
        echoService.action(list);
        baseService.actionProject(list);
        projectProductService.actionProduct(list);
        
        // 为每个父项目设置子项目信息
        baseService.setChildrenInfo(list);
        
        return success(list);
    }
    //主系统及关联系统的缺陷具体情况（以柱状图的方式、区分缺陷级别展示系统下的缺陷数据。存量缺陷和无效缺陷不计数）
    @ApiOperation(value = "获取主系统及关联系统缺陷统计", notes = "以柱状图方式展示系统下按缺陷级别分类的缺陷数据")
    @GetMapping("/getBugStatsBySystem")
    @SysLog(value = "获取主系统及关联系统缺陷统计", optType = OptLogTypeEnum.QUERY)
    public R<List<SystemBugStatsResult>> getBugStatsBySystem(@RequestParam(value = "projectId") Long projectId,
            @RequestParam(value = "reqId",required = false)Long reqId) {
        // 根据项目ID查询所有缺陷
        List<Bug> bugs = bugService.list(Wraps.<Bug>lbQ()
                .eq(Bug::getProjectId, projectId).eq(Bug::getTestreqId, reqId)
                );

        // 获取所有涉及的系统ID（主系统和关联系统）
        Set<Long> systemIds = new HashSet<>();
        bugs.forEach(bug -> {
            if (bug.getBugToSystem() != null) {
                systemIds.add(bug.getBugToSystem());
            }
        });

        // 查询系统信息
        Map<Long, ProductInfo> systemMap = new HashMap<>();
        if (!systemIds.isEmpty()) {
            List<ProductInfo> systems = productInfoApi.selectProductInfoByIds(new ArrayList<>(systemIds));
            systemMap = systems.stream().collect(Collectors.toMap(ProductInfo::getId, s -> s));
        }

        // 按系统和优先级统计缺陷
        List<SystemBugStatsResult> results = new ArrayList<>();

        for (Long systemId : systemIds) {
            ProductInfo system = systemMap.get(systemId);
            String systemName = system != null ? system.getName() : "未知系统";

            // 统计该系统的缺陷（主系统 + 关联系统）
            List<Bug> systemBugs = bugs.stream()
                    .filter(bug -> Objects.equals(bug.getBugToSystem(), systemId))
                    .collect(Collectors.toList());

            // 按优先级分组统计
            Map<String, Long> priorityStats = systemBugs.stream()
                    .collect(Collectors.groupingBy(
                            bug -> bug.getPriorityCode() != null ? bug.getPriorityCode() : "MEDIUM",
                            Collectors.counting()
                    ));

            // 构建结果
            List<SystemBugStatsResult.PriorityData> priorityDataList = new ArrayList<>();
            String[] priorities = {"HIGHEST", "H","HIGH", "MEDIUM", "LOW","L" ,"LOWEST"};
            Map<String,String> prioritiesMaps= new HashMap<>();
            prioritiesMaps.put("LOWEST","最低");
            prioritiesMaps.put("L","低");
            prioritiesMaps.put("LOW","较低");
            prioritiesMaps.put("MEDIUM","普通");
            prioritiesMaps.put("HIGH","较高");
            prioritiesMaps.put("H","高");
            prioritiesMaps.put("HIGHEST","最高");
            for (String priority : priorities) {
                priorityDataList.add(SystemBugStatsResult.PriorityData.builder()
                        .priority(prioritiesMaps.get(priority))
                        .count(priorityStats.getOrDefault(priority, 0L).intValue())
                        .build());
            }

            results.add(SystemBugStatsResult.builder()
                    .systemId(systemId)
                    .systemName(systemName)
                    .totalCount(systemBugs.size())
                    .priorityData(priorityDataList)
                    .build());
        }

        // 按总缺陷数降序排列
        results.sort((a, b) -> Integer.compare(b.getTotalCount(), a.getTotalCount()));

        return success(results);
    }
    @ApiOperation(value = "获取主系统及关联系统缺陷解决情况统计", notes = "以柱状图方式展示系统下缺陷的已解决和未解决情况")
    @GetMapping("/getBugResolveStatsBySystem")
    @SysLog(value = "获取主系统及关联系统缺陷解决情况统计", optType = OptLogTypeEnum.QUERY)
    public R<List<SystemBugResolveStatsResult>> getBugResolveStatsBySystem(@RequestParam(value = "projectId") Long projectId,
            @RequestParam(value = "reqId",required = false)Long reqId) {
        // 根据项目ID查询所有缺陷
        List<Bug> bugs = bugService.list(Wraps.<Bug>lbQ()
                .eq(Bug::getProjectId, projectId)
                        .eq(Bug::getTestreqId,reqId)
                .select(Bug::getProductId, Bug::getBugToSystem, Bug::getStateCode,Bug::getRateProgress));

        // 获取所有状态信息，判断哪些是终态
//        List<State> allStates = stateApi.findByStateName(new ArrayList<>());
//        Set<String> finalStateCodes = allStates.stream()
//                .filter(state -> state.getCode() != null)
//                .map(State::getCode)
//                .collect(Collectors.toSet());

        // 获取所有涉及的系统ID（主系统和关联系统）
        Set<Long> systemIds = new HashSet<>();
        bugs.forEach(bug -> {
            if (bug.getBugToSystem() != null) {
                systemIds.add(bug.getBugToSystem());
            }
        });

        // 查询系统信息
        Map<Long, ProductInfo> systemMap = new HashMap<>();
        if (!systemIds.isEmpty()) {
            List<ProductInfo> systems = productInfoApi.selectProductInfoByIds(new ArrayList<>(systemIds));
            systemMap = systems.stream().collect(Collectors.toMap(ProductInfo::getId, s -> s));
        }

        // 按系统统计已解决和未解决缺陷
        List<SystemBugResolveStatsResult> results = new ArrayList<>();

        for (Long systemId : systemIds) {
            ProductInfo system = systemMap.get(systemId);
            String systemName = system != null ? system.getName() : "未知系统";

            // 统计该系统的缺陷
            List<Bug> systemBugs = bugs.stream()
                    .filter(bug -> Objects.equals(bug.getBugToSystem(), systemId))
                    .collect(Collectors.toList());

            // 统计已解决和未解决缺陷
            long resolvedCount = systemBugs.stream()
                    .filter(bug -> bug.getRateProgress()==100)
                    .count();

            long unresolvedCount = systemBugs.size() - resolvedCount;

            results.add(SystemBugResolveStatsResult.builder()
                    .systemId(systemId)
                    .systemName(systemName)
                    .totalCount(systemBugs.size())
                    .resolvedCount((int) resolvedCount)
                    .unresolvedCount((int) unresolvedCount)
                    .build());
        }

        // 按总缺陷数降序排列
        results.sort((a, b) -> Integer.compare(b.getTotalCount(), a.getTotalCount()));

        return success(results);
    }

    @ApiOperation(value = "获取测试进度-里程碑计划", notes = "展示标记为里程碑的测试计划进度，按计划开始时间排序")
    @GetMapping("/getTestProgress")
    @SysLog(value = "获取测试进度-里程碑计划", optType = OptLogTypeEnum.QUERY)
    public R<List<ProjectPlan>> getTestProgress(@RequestParam(value = "projectId") Long projectId) {
        // 查询项目下标记为里程碑的测试计划
        List<ProjectPlan> milestonePlans = projectPlanService.list(Wraps.<ProjectPlan>lbQ()
                .eq(ProjectPlan::getProjectId, projectId)
                        .eq(ProjectPlan::getIsMileStone, true)
                .orderByAsc(ProjectPlan::getPlanStime));
        return success(milestonePlans);
    }

}

