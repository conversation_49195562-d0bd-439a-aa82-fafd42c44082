package com.jettech.jettong.alm.controller.issue;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.exception.code.ExceptionCode;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.model.LoadService;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.alm.issue.dto.*;
import com.jettech.jettong.alm.issue.entity.*;
import com.jettech.jettong.alm.issue.enumeration.IssueRelationType;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.issue.service.*;
import com.jettech.jettong.alm.issue.service.event.IssueUpdateEvent;
import com.jettech.jettong.alm.issue.vo.IssueTypeComponentResult;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.alm.project.entity.ProjectUserRole;
import com.jettech.jettong.alm.project.service.ProjectInfoService;
import com.jettech.jettong.alm.project.service.ProjectUserRoleService;
import com.jettech.jettong.alm.project.vo.TopologyVO;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;
import com.jettech.jettong.base.api.PersonalizedTableViewApi;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.product.api.ProductInfoApi;
import com.jettech.jettong.product.api.ProductVersionApi;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.testm.api.TestCaseApi;
import com.jettech.jettong.testm.api.TestPlanApi;
import com.jettech.jettong.testm.api.TestReqApi;
import com.jettech.jettong.testm.entity.TestProductCase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
import java.util.stream.Collectors;

import static com.jettech.jettong.common.constant.SwaggerConstants.*;

/**
 * 测试需求信息控制器
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong1
 * @package com.jettech.jettong.alm.issue.dto
 * @className aa
 * @date 2025/7/7 18:38
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Validated
@RestController
@RequestMapping("/alm/testreq")
@Api(value = "Testreq", tags = "测试需求信息")
@RequiredArgsConstructor
public class TestreqController extends
        SuperController<TestreqService, Long, Testreq, TestreqPageQuery, TestreqSaveDTO, TestreqUpdateDTO> {
    private final static String ADD_KEY = "ADD";
    private static final String TASK_LIST = "taskList";
    private static final String REQUIREMENT = "requirement";
    private static final String PARENT = "parent";
    private static final String SON = "son";
    private final EchoService echoService;
    private final Map<String, LoadService> loadServiceMap;
    private final TaskService taskService;
    private final BugService bugService;
    private final IdeaService ideaService;
    private final ProjectUserRoleService projectUserRoleService;
    private final TestPlanApi testPlanApi;
    private final UserApi userApi;
    private final ProjectInfoService projectInfoService;
    private final StateService stateService;
    private final IssueTagItemService issueTagItemService;

    private final TypeService typeService;

    private final IssueTagService issueTagService;

    private final IssueIdeaProductService issueIdeaProductService;

    private final IssueTestreqProductService issueTestreqProductService;

    private final ProductVersionApi productVersionApi;

    private final IssueFocusService issueFocusService;

    private final IssueItemRelationService issueItemRelationService;

    private final IssueRequirementTestCaseService issueRequirementTestCaseService;

    private final IssueIdeaProductRequirementService issueIdeaProductRequirementService;

    private final TestCaseApi testCaseApi;
    private final SourceService sourceService;
    private final PriorityService priorityService;
    private final PersonalizedTableViewApi tableViewApi;
    private final TestReqApi testReqApi;
    private final ProductInfoApi productInfoApi;

    @Override
    public R<Boolean> handlerDelete(List<Long> ids)
    {
        for (Long id : ids)
        {
            Testreq oldTestreq = baseService.getById(id);
            if (null == oldTestreq)
            {
                continue;
            }
            // 如果当前登录人不是处理人/负责人/提出人或角色为该项目项目经理则不能删除
            Long leadingBy = oldTestreq.getLeadingBy();
            Long putBy = oldTestreq.getPutBy();
            Long handleBy = oldTestreq.getHandleBy();
            Long createdBy = oldTestreq.getCreatedBy();

            // 查询该项目的项目经理
            List<Long> userIds =
                    projectUserRoleService.listObjs(Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getUserId)
                                    .eq(ProjectUserRole::getProjectId, oldTestreq.getProjectId())
                                    .exists("select 1 from project_role where `code` = 'PM' and id = role_id"),
                            Convert::toLong);

            // 查询具有流转权限角色的用户id
            List<Long> workFlowUserIds = baseService.getWorkFlowUserIds(oldTestreq);
            if (!isUserEx(putBy, leadingBy, handleBy, createdBy, userIds, workFlowUserIds))
            {
                return fail("当前登录人不是处理人/负责人/提出人/创建人/项目经理，不能删除");
            }
        }
        issueTestreqProductService.remove(Wraps.<IssueTestreqProduct>lbQ().in(IssueTestreqProduct::getTestreqId, ids));
        issueTagItemService.remove(Wraps.<IssueTagItem>lbQ().in(IssueTagItem::getBizId, ids));
        baseService.removeByIds(ids);
        return success(true);
    }
    /**
     * 判断当前登录用户是否是处理人/负责人/提出人
     *
     * @param createdBy 创建人
     * @param handleBy 处理人
     * @param leadingBy 负责人
     * @return {@link boolean} 是否是当前登录用户
     * <AUTHOR>
     * @date 2025/7/8 14:07
     * @update 2025/7/8 14:07
     * @since 1.0
     */
    private boolean isUserEx(Long putBy, Long handleBy, Long leadingBy, Long createdBy, List<Long> userIds, List<Long> workFlowUserIds)
    {
        if (null != putBy && putBy.equals(getUserId()))
        {
            return true;
        }

        if (null != createdBy && createdBy.equals(getUserId()))
        {
            return true;
        }

        if (null != handleBy && handleBy.equals(getUserId()))
        {
            return true;
        }

        if (null != leadingBy && leadingBy.equals(getUserId()))
        {
            return true;
        }
        if (!userIds.isEmpty() && userIds.contains(getUserId()))
        {
            return true;
        }

        return !workFlowUserIds.isEmpty() && workFlowUserIds.contains(getUserId());
    }

    @ApiOperation(value = "根据传入字段修改", notes = "根据传入字段修改")
    @PutMapping("/updateById")
    public R<Boolean> updateById(@RequestBody Testreq testreq)
    {
        Long id = testreq.getId();
        if (id == null)
        {
            throw BizException.validFail("ID不能为空");
        }

        Testreq oldtestreq = baseService.getById(id);
        if (oldtestreq == null)
        {
            return fail("要修改的测试需求不存在");
        }
        // 如果当前登录人不是处理人/负责人/提出人或角色为该项目项目经理则不能修改
        Long leadingBy = oldtestreq.getLeadingBy();
        Long putBy = oldtestreq.getPutBy();
        Long handleBy = oldtestreq.getHandleBy();
        Long createdBy = oldtestreq.getCreatedBy();
        // 查询该项目的项目经理
        List<Long> userIds =
                projectUserRoleService.listObjs(Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getUserId)
                        .eq(ProjectUserRole::getProjectId, oldtestreq.getProjectId())
                        .exists("select 1 from project_role where `code` = 'PM' and id = role_id"), Convert::toLong);

        // 查询具有流转权限角色的用户id
        List<Long> workFlowUserIds = baseService.getWorkFlowUserIds(oldtestreq);
        if (!isUserEx(putBy, leadingBy, handleBy, createdBy, userIds, workFlowUserIds))
        {
            return fail("当前登录人不是处理人/负责人/提出人/创建人/项目经理，不能修改");
        }
        boolean update = baseService.updateById(testreq);
        Testreq newTestreq = baseService.getById(id);

        // 修改事件
        IssueUpdateEvent.sendEvent(TypeClassify.TESTREQ, id, oldtestreq, newTestreq);
        return success(update);
    }

    @Override
    public R<Testreq> handlerSave(TestreqSaveDTO model)
    {
        // 清理多余数据
        model.setCreateTime(null);
        model.setUpdateTime(null);
        model.setCreatedBy(null);
        model.setUpdatedBy(null);
        model.setId(null);
        List<Long> assistProductIds = model.getAssistProductIds();
        List<String> tagId = model.getTagId();
        model.setTagId(null);
        Testreq testreq = BeanPlusUtil.toBean(model, Testreq.class);

        baseService.save(testreq);
        if (tagId != null && tagId.size() != 0)
        {
            issueTagItemService
                    .save(testreq.getId(), tagId.toArray(new String[tagId.size()]), testreq.getTypeCode());
        }
        if (assistProductIds != null && assistProductIds.size() != 0)
        {
            List<IssueTestreqProduct> issueTestreqProducts = new ArrayList();
            for(Long productId :assistProductIds){
                IssueTestreqProduct issueTestreqProduct = new IssueTestreqProduct();
                issueTestreqProduct.setTestreqId(testreq.getId());
                issueTestreqProduct.setProductId(productId);
                issueTestreqProducts.add(issueTestreqProduct);
            }
            issueTestreqProductService.batchSave(issueTestreqProducts);
        }
        return success(testreq);
    }
    @Override
    public R<Testreq> handlerUpdate(TestreqUpdateDTO model)
    {
        Testreq oldTestreq = baseService.getById(model.getId());
        if (oldTestreq == null)
        {
            return fail("要修改的测试需求不存在");
        }
        // 如果当前登录人不是处理人/负责人/提出人/创建人或角色为该项目项目经理则不能修改
        Long leadingBy = oldTestreq.getLeadingBy();
        Long putBy = oldTestreq.getPutBy();
        Long handleBy = oldTestreq.getHandleBy();
        Long createdBy = oldTestreq.getCreatedBy();
        // 查询该项目的项目经理
        List<Long> userIds =
                projectUserRoleService.listObjs(Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getUserId)
                        .eq(ProjectUserRole::getProjectId, oldTestreq.getProjectId())
                        .exists("select 1 from project_role where `code` = 'PM' and id = role_id"), Convert::toLong);

        // 查询具有流转权限角色的用户id
        List<Long> workFlowUserIds = baseService.getWorkFlowUserIds(oldTestreq);
        if (!isUserEx(putBy, leadingBy, handleBy, createdBy, userIds, workFlowUserIds))
        {
            return fail("当前登录人不是处理人/负责人/提出人/项目经理，不能修改");
        }

        List<String> tagId = model.getTagId();
        model.setTagId(null);
        Testreq testreq = BeanPlusUtil.toBean(model, Testreq.class);
        baseService.updateAllById(testreq);

        Long id = testreq.getId();

        List<Long> collect = issueTagItemService.list(Wraps.<IssueTagItem>lbQ().eq(IssueTagItem::getBizId, id)
                        .eq(IssueTagItem::getClassify, TypeClassify.TESTREQ))
                .stream().map(IssueTagItem::getTagId)
                .collect(Collectors.toList());

        List<String> stringIds = collect.stream().map(Objects::toString).collect(Collectors.toList());
        if (!CollUtil.isEqualList(tagId, stringIds))
        {
            if (tagId != null && tagId.size() != 0)
            {
                issueTagItemService.remove(Wraps.<IssueTagItem>lbQ().eq(IssueTagItem::getBizId, testreq.getId()));
                issueTagItemService.save(testreq.getId(), tagId.toArray(new String[tagId.size()]),
                        testreq.getTypeCode());
            }
            else
            {
                issueTagItemService.remove(Wraps.<IssueTagItem>lbQ().eq(IssueTagItem::getBizId, testreq.getId()));
            }
            collect = issueTagItemService.list(Wraps.<IssueTagItem>lbQ().eq(IssueTagItem::getBizId, id)
                            .eq(IssueTagItem::getClassify, TypeClassify.TESTREQ))
                    .stream().map(IssueTagItem::getTagId)
                    .collect(Collectors.toList());
        }
        if (collect.isEmpty())
        {
            testreq.getEchoMap().put("tagId", null);
        }
        else
        {
            testreq.getEchoMap().put("tagId", issueTagService.listByIds(collect));
        }
        testreq.setTagId(collect);
        //辅办系统
        List<Long> assistProductIds = model.getAssistProductIds();
        if (assistProductIds != null && assistProductIds.size() != 0) {
            //新增前需根据需求清数据
            issueTestreqProductService.remove(Wraps.<IssueTestreqProduct>lbQ().eq(IssueTestreqProduct::getTestreqId, testreq.getId()));
            List<IssueTestreqProduct> issueTestreqProducts = new ArrayList();
            for(Long productId :assistProductIds){
                IssueTestreqProduct issueTestreqProduct = new IssueTestreqProduct();
                issueTestreqProduct.setTestreqId(testreq.getId());
                issueTestreqProduct.setProductId(productId);
                issueTestreqProducts.add(issueTestreqProduct);
            }
            issueTestreqProductService.batchSave(issueTestreqProducts);
            //查询辅办产品数据
            testreq.getEchoMap().put("assistProductIdList",issueTestreqProductService.getProductList(testreq.getId()));
        }else{
            //传参无数据需清空
            issueTestreqProductService.remove(Wraps.<IssueTestreqProduct>lbQ().eq(IssueTestreqProduct::getTestreqId, testreq.getId()));
            testreq.getEchoMap().put("assistProductIdList", null);
        }
        return success(testreq);
    }

    @ApiOperation(value = "需求状态流转", notes = "需求状态流转")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "testreqId", value = "需求id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH),
            @ApiImplicitParam(name = "sourceStateCode", value = "源状态code", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_PATH),
            @ApiImplicitParam(name = "targetStateCode", value = "目标状态code", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_PATH)
    })
    @PutMapping("/transitionState/{testreqId}/{sourceStateCode}/{targetStateCode}")
    @SysLog(value = "需求状态流转", request = false, optType = OptLogTypeEnum.EDIT)
    public R<Boolean> transitionState(@PathVariable("testreqId") Long testreqId,
            @PathVariable("sourceStateCode") String sourceStateCode,
            @PathVariable("targetStateCode") String targetStateCode)
    {
        baseService.transitionState(testreqId, sourceStateCode, targetStateCode);
        return success();
    }
    @ApiOperation(value = "需求中心分页查询", notes = "需求中心分页查询")
    @PostMapping("/pageReqm")
    public IPage<Testreq> pageForTestreq(@RequestBody PageParams<TestreqPageQuery> params)
    {
        IPage<Testreq> testreqIPage = queryPage(params);

        // 保存最后一次查询的条件
        tableViewApi.saveLastSearch(getUserId(), "reqm-testreq-table", params);

        return testreqIPage;
    }
    @Override
    public IPage<Testreq> query(PageParams<TestreqPageQuery> params)
    {
        IPage<Testreq> requirementIPage = queryPage(params);

        // 保存最后一次查询的条件
        tableViewApi.saveLastSearch(getUserId(), "testreq-table", params);

        return requirementIPage;
    }
    private IPage<Testreq> queryPage(PageParams<TestreqPageQuery> params)
    {
        IPage<Testreq> page = params.buildPage(Testreq.class);
        TestreqPageQuery testreqPageQuery = params.getModel();

        LbqWrapper<Testreq> wrapper = Wraps.lbQ();
        List<Long> tagId = testreqPageQuery.getTagId();
        List<Long> ids = new ArrayList<>();
        if (tagId != null && tagId.size() != 0)
        {
            Map<Long, List<IssueTagItem>> collect =
                    issueTagItemService.list(Wraps.<IssueTagItem>lbQ().in(IssueTagItem::getTagId, tagId)).stream()
                            .collect(Collectors.groupingBy(IssueTagItem::getBizId));
            for (Long key : collect.keySet())
            {
                if (collect.get(key).size() == tagId.size())
                {
                    ids.add(key);
                }
            }
            if (collect.size() != 0)
            {
                wrapper.in(Testreq::getId, ids);
            }
            else
            {
                return page;
            }
        }

        wrapper.select(Testreq.class, re -> !"description".equals(re.getProperty()))
                .like(Testreq::getCode, testreqPageQuery.getCode())
                .like(Testreq::getName, testreqPageQuery.getName())
                .in(Testreq::getParentId, testreqPageQuery.getParentId())
                .in(Testreq::getStateCode, testreqPageQuery.getStateCode())
                .in(Testreq::getPriorityCode, testreqPageQuery.getPriorityCode())
                .in(Testreq::getTypeCode, testreqPageQuery.getTypeCode())
                .in(Testreq::getProductVersionId, testreqPageQuery.getProductVersionId())
                .in(Testreq::getProgramId, testreqPageQuery.getProgramId())
                .in(Testreq::getProjectId, testreqPageQuery.getProjectId())
                .in(Testreq::getPlanId, testreqPageQuery.getPlanId())
                .in(Testreq::getProductId, testreqPageQuery.getProductId())
                .in(Testreq::getPutBy, testreqPageQuery.getPutBy())
                .in(Testreq::getLeadingBy, testreqPageQuery.getLeadingBy())
                .in(Testreq::getHandleBy, testreqPageQuery.getHandleBy())
                .in(Testreq::getSourceCode, testreqPageQuery.getSourceCode())
                .in(Testreq::getProductModuleFunctionId, testreqPageQuery.getProductModuleFunctionId())
                .in(Testreq::getProductRepairVersionId, testreqPageQuery.getProductRepairVersionId())
                .eq(Testreq::getIsFiled, testreqPageQuery.getIsFiled());

        // 延期 筛选
        if (testreqPageQuery.getDelay() != null)
        {
            if (testreqPageQuery.getDelay())
            {
                wrapper.eq(Testreq::getDelay, true);
            }
            else
            {
                wrapper.and(w -> w.eq(Testreq::getDelay, false).or().isNull(Testreq::getDelay));
            }
        }

        PeriodQuery.appendQuery(wrapper, testreqPageQuery.getPlanEtime(), Testreq::getPlanEtime);
        PeriodQuery.appendQuery(wrapper, testreqPageQuery.getPlanStime(), Testreq::getPlanStime);
        PeriodQuery.appendQuery(wrapper, testreqPageQuery.getEstimateHour(), Testreq::getEstimateHour);
        PeriodQuery.appendQuery(wrapper, testreqPageQuery.getRateProgress(), Testreq::getRateProgress);
        PeriodQuery.appendQuery(wrapper, testreqPageQuery.getEstimatePoint(), Testreq::getEstimatePoint);
        PeriodQuery.appendQuery(wrapper, testreqPageQuery.getUsedHour(), Testreq::getUsedHour);
        PeriodQuery.appendQuery(wrapper, testreqPageQuery.getStartTime(), Testreq::getStartTime);
        PeriodQuery.appendQuery(wrapper, testreqPageQuery.getEndTime(), Testreq::getEndTime);
        PeriodQuery.appendQuery(wrapper, testreqPageQuery.getCreateTime(), Testreq::getCreateTime);

        // 添加自定义字段的条件筛选
        testreqPageQuery.appendQuery(wrapper);

        if (null != testreqPageQuery.getParentId() && 1 == testreqPageQuery.getParentId().size() &&
                Convert.toLong(-1).equals(testreqPageQuery.getParentId().get(0)))
        {
            wrapper.isNull(Testreq::getParentId);
        }
        else
        {
            wrapper.in(Testreq::getParentId, testreqPageQuery.getParentId());
        }

        baseService.page(page, wrapper);

        page.getRecords().forEach(item -> item.setDescription(null));

        echoService.action(page);

//        List<Long> testreqList = page.getRecords().stream().map(Testreq::getId).collect(Collectors.toList());
//        List<TestPlan> testPlanListByRequList =
//                testPlanApi.getTestPlanListByRequList(
//                        testreqList.stream().map(a -> a + "").collect(Collectors.toList()));
//        page.getRecords().forEach(testreq ->
//        {
//            testreq.getEchoMap().put("testPlan", null);
//            for (TestPlan testPlan : testPlanListByRequList)
//            {
//                if (Objects.equals(testPlan.getQueryId(), testreq.getId().toString()))
//                {
//                    testreq.getEchoMap().put("testPlan", testPlan);
//                }
//            }
//
//        });


        // 根据工作项ID获取标签数据
        Set<Long> bizIds = page.getRecords().stream().map(SuperEntity::getId).collect(Collectors.toSet());
        Map<Long, List<IssueTag>> issueTagMap = issueTagService.issueTagMap(bizIds);

        // 将标签数据放入工作项中
        page.getRecords().forEach(item ->
        {
            item.setDescription(null);

            Long bizId = item.getId();
            List<IssueTag> issueTags = issueTagMap.getOrDefault(bizId, new ArrayList<>());

            List<Long> tagIds = CollUtil.map(issueTags, SuperEntity::getId, true);
            item.setTagId(tagIds);

            item.getEchoMap().put("tagId", issueTags);

            //查询辅办产品数据
           List<ProductInfo> products = issueTestreqProductService.getProductList(bizId);
            item.getEchoMap().put("assistProductIdList", products);
        });

        return page;
    }

    @Override
    @ApiOperation(value = "批量查询,不会查询关联数据", notes = "批量查询,不会查询关联数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码，模糊查询", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "name", value = "名称，模糊查询", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "parentId", value = "父需求id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "typeCode", value = "事项分类", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "stateCode", value = "状态", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "productId", value = "关联产品id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "priorityCode", value = "优先级", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "projectId", value = "归属项目id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "planId", value = "关联计划id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "productVersionId", value = "产品版本id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "delay", value = "是否延期", dataType = DATA_TYPE_BOOLEAN,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "putBy", value = "提出人", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "leadingBy", value = "负责人", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "handleBy", value = "处理人", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "sourceCode", value = "来源code", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "ideaId", value = "用户需求id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY)
    })
    public R<List<Testreq>> query(@ApiIgnore @RequestBody Testreq testreq)
    {
        LbqWrapper<Testreq> wrapper = Wraps.lbQ();

        wrapper.select(Testreq.class, task -> !"description".equals(task.getColumn()))
                .like(Testreq::getName, testreq.getName())
                .like(Testreq::getCode, testreq.getCode())
                .eq(Testreq::getParentId, testreq.getParentId())
                .eq(Testreq::getTypeCode, testreq.getTypeCode())
                .eq(Testreq::getStateCode, testreq.getStateCode())
                .eq(Testreq::getProductId, testreq.getProductId())
                .eq(Testreq::getPriorityCode, testreq.getPriorityCode())
                .eq(Testreq::getProjectId, testreq.getProjectId())
                .eq(Testreq::getPlanId, testreq.getPlanId())
                .eq(Testreq::getProductVersionId, testreq.getProductVersionId())
                .eq(Testreq::getDelay, testreq.getDelay())
                .eq(Testreq::getPutBy, testreq.getPutBy())
                .eq(Testreq::getLeadingBy, testreq.getLeadingBy())
                .eq(Testreq::getHandleBy, testreq.getHandleBy())
                .eq(Testreq::getSourceCode, testreq.getSourceCode());

        List<Testreq> testreqs = baseService.list(wrapper);
        testreqs.forEach(item -> item.setDescription(null));

        return success(testreqs);
    }

    @Override
    public R<Testreq> get(Long id)
    {

        Testreq testreq = baseService.getById(id);
        if (testreq == null)
        {
            throw BizException.wrap(ExceptionCode.NOT_FOUND);
        }

        String typeCode = testreq.getTypeCode();
        TypeClassify classify = typeService.findByCode(typeCode).getClassify();
        List<Long> collect = issueTagItemService.list(Wraps.<IssueTagItem>lbQ().eq(IssueTagItem::getBizId, id)
                        .eq(IssueTagItem::getClassify, classify)).stream().map(IssueTagItem::getTagId)
                .collect(Collectors.toList());
        if (collect != null && collect.size() != 0)
        {
            testreq.getEchoMap().put("tagId", issueTagService.listByIds(collect));
        }
        else
        {
            testreq.getEchoMap().put("tagId", null);
        }
        if (CollUtil.isNotEmpty(testreq.getAssistProductIds())){
            //查询辅办产品数据
            List<ProductInfo> products = productInfoApi.selectProductInfoByIds(testreq.getAssistProductIds());
            testreq.getEchoMap().put("assistProductIds",products);
        }
        testreq.setTagId(collect);

        echoService.action(testreq);
        IssueCustomFieldService.echo(testreq, testreq.getEchoMap());
        return success(testreq);
    }

    @ApiOperation(value = "查询测试需求可流转的状态节点", notes = "查询测试需求可流转的状态节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "testreqId", value = "测试需求id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @GetMapping("/findNextNode/{testreqId}")
    public R<List<WorkflowNode>> findNextNode(@ApiIgnore @PathVariable Long testreqId)
    {
        List<WorkflowNode> nextNodes = baseService.findNextNode(testreqId);
        echoService.action(nextNodes);
        return success(nextNodes);
    }

    @ApiOperation(value = "批量查询", notes = "批量查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码，模糊查询", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "name", value = "名称，模糊查询", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "parentId", value = "父需求id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "typeCode", value = "事项分类", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "stateCode", value = "状态", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "productId", value = "关联产品id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "priorityCode", value = "优先级", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "projectId", value = "归属项目id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "planId", value = "关联计划id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "productVersionId", value = "产品版本id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "delay", value = "是否延期", dataType = DATA_TYPE_BOOLEAN,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "putBy", value = "提出人", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "leadingBy", value = "负责人", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "handleBy", value = "处理人", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "sourceCode", value = "来源code", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "ideaId", value = "用户需求id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY)
    })
    @PostMapping("/queryList")
    public R<List<Testreq>> queryList(@ApiIgnore @RequestBody Testreq testreq)
    {
        LbqWrapper<Testreq> wrapper = Wraps.lbQ();

        wrapper.like(Testreq::getName, testreq.getName())
                .like(Testreq::getCode, testreq.getCode())
                .eq(Testreq::getParentId, testreq.getParentId())
                .eq(Testreq::getTypeCode, testreq.getTypeCode())
                .eq(Testreq::getStateCode, testreq.getStateCode())
                .eq(Testreq::getProductId, testreq.getProductId())
                .eq(Testreq::getPriorityCode, testreq.getPriorityCode())
                .eq(Testreq::getProjectId, testreq.getProjectId())
                .eq(Testreq::getPlanId, testreq.getPlanId())
                .eq(Testreq::getProductVersionId, testreq.getProductVersionId())
                .eq(Testreq::getDelay, testreq.getDelay())
                .eq(Testreq::getPutBy, testreq.getPutBy())
                .eq(Testreq::getLeadingBy, testreq.getLeadingBy())
                .eq(Testreq::getHandleBy, testreq.getHandleBy())
                .eq(Testreq::getSourceCode, testreq.getSourceCode());

        List<Testreq> testreqs = baseService.list(wrapper);
        for (Testreq item : testreqs)
        {
            item.setDescription(null);
            Map<String, Object> echoMap = item.getEchoMap();
            Long handleBy = item.getHandleBy();
            if (handleBy != null)
            {
                User userById = userApi.findUserById(handleBy);
                echoMap.put("handleBy", userById);
            }
            else
            {
                echoMap.put("handleBy", null);
            }

            if (item.getProjectId() != null)
            {
                ProjectInfo projectInfo = projectInfoService.findById(item.getProjectId());
                echoMap.put("projectInfo", projectInfo);
            }
            else
            {
                echoMap.put("projectInfo", null);
            }

            if (item.getStateCode() != null && !item.getStateCode().isEmpty())
            {
                State state = stateService.getOne(Wraps.<State>lbQ().eq(State::getCode, item.getStateCode()));
                echoMap.put("state", state);
            }
            else
            {
                echoMap.put("state", null);
            }

            Type type = Optional.ofNullable(item.getTypeCode())
                    .filter(typeCode -> !typeCode.isEmpty())
                    .map(typeService::findByCode)
                    .orElse(null);
            echoMap.put("type", type);

        }

        return success(testreqs);
    }

    @ApiOperation(value = "下拉框查询（只展示五条）", notes = "下拉框查询（只展示五条）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码，模糊查询", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "name", value = "名称，模糊查询", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "parentId", value = "父需求id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "typeCode", value = "事项分类", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "stateCode", value = "状态", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "productId", value = "关联产品id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "priorityCode", value = "优先级", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "projectId", value = "归属项目id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "planId", value = "关联计划id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "productVersionId", value = "产品版本id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "delay", value = "是否延期", dataType = DATA_TYPE_BOOLEAN,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "putBy", value = "提出人", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "leadingBy", value = "负责人", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "handleBy", value = "处理人", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "sourceCode", value = "来源code", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "productId", value = "产品id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY)
    })
    @PostMapping("/getFiveRequirement")
    public R<List<Testreq>> getFiveRequirement(@ApiIgnore @RequestBody TestreqPageQuery params)
    {
        LbqWrapper<Testreq> wrapper = Wraps.lbQ();
        if (Objects.equals(params.getProductId(), null) && Objects.equals(params.getProjectId(), null))
        {
            return fail("缺少项目或产品信息！");
        }
        wrapper.select(Testreq.class, task -> !"description".equals(task.getColumn()))
                .like(Testreq::getName, params.getName())
                .like(Testreq::getCode, params.getCode())
                .eq(Testreq::getParentId, params.getParentId())
                .eq(Testreq::getTypeCode, params.getTypeCode())
                .eq(Testreq::getStateCode, params.getStateCode())
                .eq(Testreq::getPriorityCode, params.getPriorityCode())
                .eq(Testreq::getPlanId, params.getPlanId())
                .eq(Testreq::getProductVersionId, params.getProductVersionId())
                .eq(Testreq::getDelay, params.getDelay())
                .eq(Testreq::getPutBy, params.getPutBy())
                .eq(Testreq::getLeadingBy, params.getLeadingBy())
                .eq(Testreq::getHandleBy, params.getHandleBy())
                .eq(Testreq::getSourceCode, params.getSourceCode());
        if (!Objects.equals(params.getProductId(), null))
        {

            wrapper.in(Testreq::getProductId, params.getProductId());
        }
        if (!Objects.equals(params.getProjectId(), null))
        {

            wrapper.in(Testreq::getProjectId, params.getProjectId());
        }

        List<Testreq> testreqs = baseService.list(wrapper.last("limit 5"));
        testreqs.forEach(item -> item.setDescription(null));
        return success(testreqs);
    }

    @ApiOperation(value = "需求树形看板数据", notes = "需求树形看板数据")
    @PostMapping("/testreqTree")
    @SysLog(value = "需求树形看板数据")
    public IPage<Testreq> testreqTree(@RequestBody PageParams<TestreqPageQuery> params)
    {
        IPage<Testreq> page = params.buildPage(Testreq.class);
        TestreqPageQuery testreqPage = params.getModel();

        LbqWrapper<Testreq> wrapper = Wraps.lbQ();

        wrapper.select(Testreq.class, re -> !"description".equals(re.getProperty()))
                .like(Testreq::getCode, testreqPage.getCode())
                .like(Testreq::getName, testreqPage.getName())
                .in(Testreq::getParentId, testreqPage.getParentId())
                .in(Testreq::getStateCode, testreqPage.getStateCode())
                .in(Testreq::getPriorityCode, testreqPage.getPriorityCode())
                .in(Testreq::getTypeCode, testreqPage.getTypeCode())
                .eq(Testreq::getDelay, testreqPage.getDelay())
                .in(Testreq::getProductVersionId, testreqPage.getProductVersionId())
                .in(Testreq::getProgramId, testreqPage.getProgramId())
                .in(Testreq::getProjectId, testreqPage.getProjectId())
                .in(Testreq::getPlanId, testreqPage.getPlanId())
                .in(Testreq::getProductId, testreqPage.getProductId())
                .in(Testreq::getPutBy, testreqPage.getPutBy())
                .in(Testreq::getLeadingBy, testreqPage.getLeadingBy())
                .in(Testreq::getHandleBy, testreqPage.getHandleBy())
                .in(Testreq::getSourceCode, testreqPage.getSourceCode())
                .eq(Testreq::getIsFiled, testreqPage.getIsFiled());

        PeriodQuery.appendQuery(wrapper, testreqPage.getPlanEtime(), Testreq::getPlanEtime);
        PeriodQuery.appendQuery(wrapper, testreqPage.getPlanStime(), Testreq::getPlanStime);
        PeriodQuery.appendQuery(wrapper, testreqPage.getEstimateHour(), Testreq::getEstimateHour);
        PeriodQuery.appendQuery(wrapper, testreqPage.getRateProgress(), Testreq::getRateProgress);
        PeriodQuery.appendQuery(wrapper, testreqPage.getEstimatePoint(), Testreq::getEstimatePoint);
        PeriodQuery.appendQuery(wrapper, testreqPage.getUsedHour(), Testreq::getUsedHour);
        PeriodQuery.appendQuery(wrapper, testreqPage.getStartTime(), Testreq::getStartTime);
        PeriodQuery.appendQuery(wrapper, testreqPage.getEndTime(), Testreq::getEndTime);
        PeriodQuery.appendQuery(wrapper, testreqPage.getCreateTime(), Testreq::getCreateTime);

        if (null != testreqPage.getParentId() && 1 == testreqPage.getParentId().size() &&
                Convert.toLong(-1).equals(testreqPage.getParentId().get(0)))
        {
            wrapper.isNull(Testreq::getParentId);
        }
        else
        {
            wrapper.in(Testreq::getParentId, testreqPage.getParentId());
        }
        baseService.page(page, wrapper);

        echoService.action(page);

        return page;
    }

    @ApiOperation(value = "查询用户需求或者需求关联的父子需求", notes = "查询用户需求或者需求关联的父子需求")
    @PostMapping("/selectTestreqByIdeaIdOrTestreqId")
    @SysLog(value = "查询用户需求或者需求关联的父子需求")
    public R<List<Testreq>> selectTestreqByIdeaIdOrTestreqId(
            @RequestBody Testreq testreq)
    {

        List<Testreq> list = baseService.selectTestreqByIdeaIdOrTestreqId(testreq);
        //手动注入
        echoService.action(list);
        return success(list);
    }

    @GetMapping("/topology/{id}")
    @ApiOperation(value = "新根据需求id获取拓扑图信息", notes = "新根据需求id获取拓扑图信息")
    @SysLog(value = "新根据需求id获取拓扑图信息", request = false)
    public R<TopologyVO<TestProductCase, IssueRequirementCode>> getTopology(@PathVariable("id") Long id)
    {
        TopologyVO<TestProductCase, IssueRequirementCode> topology = new TopologyVO();
        Testreq testreq = baseService.getById(id);
        if (testreq == null)
        {
            return R.fail("需求不存在");
        }
        topology.setRequirement(BeanPlusUtil.toBean(testreq, WorkItemDTO.class));
        echoService.action(topology.getRequirement());
        //获取需求相关的需求 父子关系 关联关系
        Map<IssueRelationType, List<WorkItemDTO>> data =
                issueItemRelationService.getRelation(TypeClassify.TESTREQ, id);
        data.forEach((relationType, itemList) -> itemList.forEach(echoService::action));
        topology.setParentRequirement(CollUtil.isEmpty(data.get(IssueRelationType.PARENT)) ? null :
                data.get(IssueRelationType.PARENT).get(0));
        topology.setChildRequirements(data.get(IssueRelationType.CHILD));
        TopologyVO.Relation relation = topology.new Relation();
        relation.setRelationRequirements(data.get(IssueRelationType.RELATION));
        relation.setAffectRequirements(data.get(IssueRelationType.AFFECT));
        relation.setDependRequirements(data.get(IssueRelationType.DEPEND));
        topology.setRelation(relation);

        //获取需求相关的测试用例
        List<String> caseIds = issueRequirementTestCaseService.list(
                        Wrappers.<IssueRequirementTestCase>lambdaQuery().eq(IssueRequirementTestCase::getRequirementId, id))
                .stream()
                .map(IssueRequirementTestCase::getProductCaseId)
                .map(Object::toString)
                .distinct()
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(caseIds))
        {
            List<TestProductCase> caseList = testCaseApi.findCasesByIds(caseIds);
            echoService.action(caseList);
            topology.setTestCases(caseList);
        }
        //获取需求相关的用户需求
        topology.setIdeas(issueIdeaProductRequirementService.getIdeaList(id));
        echoService.action(topology.getIdeas());

        // 获取需求相关的任务
        List<Task> taskList = taskService.list(Wrappers.<Task>lambdaQuery().eq(Task::getRequirementId, id));
        List<WorkItemDTO> taskItemList = BeanPlusUtil.toBeanList(taskList, WorkItemDTO.class);
        echoService.action(taskItemList);
        topology.setTasks(taskItemList);

        return R.success(topology);
    }
    @ApiOperation(value = "需求关联或解除关联父需求", notes = "需求关联或解除关联父需求")
    @PutMapping("/correlatedRequirement")
    @SysLog(value = "需求关联或解除关联父需求", optType = OptLogTypeEnum.EDIT)
    public R<Boolean> correlatedBug(@Validated @RequestBody RequirementCorrelatedRequirementDTO model)
    {
        List<Long> requirementIds = model.getRequirementIds();
        String correlatedType = model.getCorrelatedType();
        Long requirementId = model.getRequirementId();
        Testreq testreq = baseService.getById(requirementId);
        if (testreq == null)
        {
            return validFail("关联失败,原因:未查询到需求信息");
        }
        if (ADD_KEY.equals(correlatedType))
        {
            baseService.update(Wraps.<Testreq>lbU().set(Testreq::getParentId, requirementId)
                    .set(Testreq::getPlanId, testreq.getPlanId()).in(SuperEntity::getId, requirementIds));
        }
        else
        {
            baseService.update(Wraps.<Testreq>lbU().set(Testreq::getParentId, null)
                    .in(SuperEntity::getId, requirementIds));
        }
        return success();
    }

    @ApiOperation(value = "需求关联或解除关联任务", notes = "需求关联或解除关联任务")
    @PutMapping("/correlatedTask")
    @SysLog(value = "需求关联或解除关联任务", optType = OptLogTypeEnum.EDIT)
    public R<Boolean> correlatedTask(@Validated @RequestBody RequirementCorrelatedTaskDTO model)
    {
        List<Long> taskIds = model.getTaskIds();
        String correlatedType = model.getCorrelatedType();
        Long testreqId = model.getRequirementId();
        Testreq testreq = baseService.getById(testreqId);
        if (testreq==null)
        {
            return validFail("关联失败,原因:未查询到需求信息");
        }
        Long planId = testreq.getPlanId();

        taskService.update(Wraps.<Task>lbU()
                .set(ADD_KEY.equals(correlatedType), Task::getTestreqId, testreqId)
                .set(ADD_KEY.equals(correlatedType) && planId != null, Task::getPlanId, planId)
                .set(!ADD_KEY.equals(correlatedType), Task::getTestreqId, null)
                .in(SuperEntity::getId, taskIds)
        );
        return success();
    }

    @ApiOperation(value = "需求关联或解除关联缺陷", notes = "需求关联或解除关联缺陷")
    @PutMapping("/correlatedBug")
    @SysLog(value = "需求关联或解除关联缺陷", optType = OptLogTypeEnum.EDIT)
    public R<Boolean> correlatedBug(@Validated @RequestBody RequirementCorrelatedBugDTO model)
    {
        List<Long> bugIds = model.getBugIds();
        String correlatedType = model.getCorrelatedType();
        Long requirementId = model.getRequirementId();
        Testreq byId = baseService.getById(requirementId);
        if (byId == null)
        {
            return validFail("关联失败,原因:未查询到需求信息");
        }
        Long planId = byId.getPlanId();
//        if(byId != null){
//            planId = byId.getPlanId();
//        }else {
//            planId = requirement.getPlanId();
//        }
        if (ADD_KEY.equals(correlatedType))
        {
            bugService.update(Wraps.<Bug>lbU().set(Bug::getTestreqId, requirementId)
                    .set(Bug::getPlanId, planId).in(SuperEntity::getId, bugIds));
        }
        else
        {
            bugService.update(Wraps.<Bug>lbU().set(Bug::getTestreqId, null).in(SuperEntity::getId, bugIds));
        }
        return success();
    }
    @ApiOperation(value = "查询项目需求概览组件", notes = "查询项目需求概览组件")
    @GetMapping("/findTypeByProjectId/{projectId}")
    @SysLog("查询项目需求概览组件")
    public R<IssueTypeComponentResult> findTypeByProjectId(@PathVariable("projectId") Long projectId)
    {
        return R.success(baseService.findTypeByProjectId(projectId));
    }
}
