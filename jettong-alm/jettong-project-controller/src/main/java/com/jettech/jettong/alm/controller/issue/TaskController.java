package com.jettech.jettong.alm.controller.issue;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.exception.code.ExceptionCode;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.StringUtil;
import com.jettech.jettong.alm.issue.dto.*;
import com.jettech.jettong.alm.issue.entity.*;
import com.jettech.jettong.alm.issue.enumeration.ItemUserType;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.issue.service.*;
import com.jettech.jettong.alm.issue.service.event.IssueUpdateEvent;
import com.jettech.jettong.alm.issue.vo.IssueTypeComponentResult;
import com.jettech.jettong.alm.issue.vo.TaskStateComponentResult;
import com.jettech.jettong.alm.project.entity.ProjectWorkflow;
import com.jettech.jettong.alm.project.service.ProjectRoleService;
import com.jettech.jettong.alm.project.service.ProjectUserRoleService;
import com.jettech.jettong.alm.project.service.ProjectWorkflowService;
import com.jettech.jettong.alm.project.service.WorkflowLogic;
import com.jettech.jettong.alm.workflow.dto.WorkflowItemDTO;
import com.jettech.jettong.alm.workflow.entity.WorkflowCountersignRecord;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;
import com.jettech.jettong.alm.workflow.enumeration.WorkflowNodeType;
import com.jettech.jettong.alm.workflow.service.WorkflowCountersignRecordService;
import com.jettech.jettong.alm.workflow.service.WorkflowInfoService;
import com.jettech.jettong.alm.workflow.service.WorkflowNodeService;
import com.jettech.jettong.base.api.PersonalizedTableViewApi;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.common.constant.TaskTypeCodeConstants;
import com.jettech.jettong.common.enumeration.CountersignStatus;
import com.jettech.jettong.product.api.ProductModuleFunctionApi;
import com.jettech.jettong.testm.api.*;
import com.jettech.jettong.testm.entity.TestProductCase;
import com.jettech.jettong.testm.entity.TestRequirementFunctionPoints;
import com.jettech.jettong.testm.entity.TestTaskCase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
import java.util.stream.Collectors;

import static com.jettech.jettong.common.constant.SwaggerConstants.*;


/**
 * 任务信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 任务信息控制器
 * @projectName jettong
 * @package com.jettech.jettong.alm.controller.issue
 * @className TaskController
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/alm/task")
@Api(value = "Task", tags = "任务信息")
@RequiredArgsConstructor
public class TaskController extends SuperController<TaskService, Long, Task, TaskPageQuery, TaskSaveDTO, TaskUpdateDTO>
{

    private final EchoService echoService;
    private final ProjectUserRoleService projectUserRoleService;
    private final TaskCommentService taskCommentService;
    private final TaskRegistrationTimeService taskRegistrationTimeService;
    private final UserApi userApi;
    private final TypeService typeService;
    private final IssueTagItemService issueTagItemService;
    private final IssueTagService issueTagService;
    private final IssueFocusService issueFocusService;
    private final PersonalizedTableViewApi tableViewApi;
    private final TestCaseApi testCaseApi;
    private final TestTaskCaseApi testTaskCaseApi;
    private final TestTaskCaseResultApi testTaskCaseResultApi;
    private final TestProductCaseApi testProductCaseApi;
    private final TestRequirementFunctionPointsApi testRequirementFunctionPointsApi;
    private final ProductModuleFunctionApi productModuleFunctionApi;
    private final BugService bugService;
    private final ProjectWorkflowService projectWorkflowService;
    private final WorkflowInfoService workflowInfoService;
    private final WorkflowNodeService workflowNodeService;
    private final WorkflowCountersignRecordService workflowCountersignRecordService;
    private final WorkflowLogic workflowLogic;
    private final TaskUserService taskUserService;
    private final ProjectRoleService projectRoleService;

    public final static String TASK_HANDLE_EXISTS = "select 1 from issue_task_user stu where stu.task_id = issue_task.id and type='HANDLE' and stu.user_id in ( %s )";

    @Override
    public R<Boolean> handlerDelete(List<Long> ids)
    {
        List<TestTaskCase> listAll = new ArrayList<>();
        for (Long id : ids)
        {
            Task oldTask = baseService.getById(id);
            if (null == oldTask)
            {
                continue;
            }


            List<TestRequirementFunctionPoints> testRequirementFunctionPoints =
                    testRequirementFunctionPointsApi.queryPointsByTaskId(id);
            if(!testRequirementFunctionPoints.isEmpty()){
                // 拼接所有测试分析的name
//                StringBuilder names = new StringBuilder();
//                for (TestRequirementFunctionPoints point : testRequirementFunctionPoints) {
//                    if (names.length() > 0) {
//                        names.append("、"); // 用顿号分隔多个name
//                    }
//                    names.append(point.getFunctionPoint()); // 假设存在getName()方法获取name
//                }
                return fail ("当前存在【" + testRequirementFunctionPoints.size () + "】项测试分析点，请先删除后再执行此操作！");
            }
            List<TestProductCase> testProductCases =
                    testProductCaseApi.queryProductCaseByTaskId(Collections.singletonList(id));
            if(!testProductCases.isEmpty()){
                // 拼接所有测试用例的的name
//                StringBuilder names = new StringBuilder();
//                for (TestProductCase testProductCase : testProductCases) {
//                    if (names.length() > 0) {
//                        names.append("、"); // 用顿号分隔多个name
//                    }
//                    names.append(testProductCase.getName()); // 假设存在getName()方法获取name
//                }
                return fail ("当前存在【" + testProductCases.size () + "】项测试用例，请先删除后再执行此操作！");

            }
            List<TestTaskCase> testTaskCaseList =
                    testTaskCaseApi.queryByTaskId(id);
            if(!testTaskCaseList.isEmpty()){
//                // 拼接所有测试用例的的name
//                StringBuilder names = new StringBuilder();
//                for (TestTaskCase testTaskCase : testTaskCaseList) {
//                    if (names.length() > 0) {
//                        names.append("、"); // 用顿号分隔多个name
//                    }
//                    names.append(testTaskCase.getName()); // 假设存在getName()方法获取name
//                }
                //return fail("存在测试执行：" + names.toString() + "，请先删除后再操作！");
                return fail ("当前存在【" + testTaskCaseList.size () + "】项测试执行，请先删除后再执行此操作！");
            }

            if (!isUserEx(oldTask))
            {
                return fail("当前登录人不是处理人/负责人/提出人/创建人/项目经理，不能删除");
            }
            if(TaskTypeCodeConstants.TASK_MANUAL.equals(oldTask.getTypeCode())){
                List<TestTaskCase> list = testTaskCaseApi.queryByTaskId( oldTask.getId());
                listAll.addAll(list);
            }
            if (TaskTypeCodeConstants.TASK_TEST_ANAL.equals(oldTask.getTypeCode())){
                List<TestRequirementFunctionPoints> points = testRequirementFunctionPointsApi.queryPointsByTaskId(oldTask.getId());
                if (CollUtil.isNotEmpty(points)){
                    throw new BizException("删除失败，"+oldTask.getName()+"包含测试点");
                }
            }
        }
        taskCommentService.remove(Wraps.<TaskComment>lbQ().in(TaskComment::getTaskId, ids));
        taskRegistrationTimeService.remove(Wraps.<TaskRegistrationTime>lbQ().in(TaskRegistrationTime::getTaskId, ids));
        issueTagItemService.remove(Wraps.<IssueTagItem>lbQ().in(IssueTagItem::getBizId, ids));
        baseService.removeByIds(ids);
        issueFocusService.remove(Wraps.<IssueFocus>lbQ().eq(IssueFocus::getClassify, "TASK")
                .in(IssueFocus::getItemId, ids));
        //删除交易关联内容
        productModuleFunctionApi.deleteProductModuleFunctionItemByBizId(ids);

        taskUserService.remove(Wraps.<TaskUser>lbQ().in(TaskUser::getTaskId, ids));

        return success();
    }

    private boolean isUserEx(Task task)
    {
        List<Long> userIds = new ArrayList<>();
        // 如果当前登录人不是处理人/负责人/提出人或角色为该项目项目经理则不能操作
        userIds.add(task.getLeadingBy());
        userIds.add(task.getHandleBy());
        userIds.add(task.getPutBy());
        userIds.add(task.getCreatedBy());
        if (userIds.stream().filter(Objects::nonNull).filter(id -> id.equals(getUserId())).findFirst().isPresent()){
            return Boolean.TRUE;
        }
        //处理人
        List<Long> handleList = taskUserService.list(
                        Wraps.<TaskUser>lbQ().select(TaskUser::getUserId).eq(TaskUser::getTaskId, task.getId())
                                .eq(TaskUser::getType, ItemUserType.HANDLE))
                .stream().map(TaskUser::getUserId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(handleList)&&handleList.contains(getUserId())){
            return Boolean.TRUE;
        }
        // 查询该项目的项目经理
        List<Long> pmUserIds = projectRoleService.getPMUserIds(task.getProjectId());
        if (CollUtil.isNotEmpty(pmUserIds)&&handleList.contains(getUserId())){
            return Boolean.TRUE;
        }
        // 查询具有流转权限角色的用户id
        List<Long> workflowUserIds = baseService.getWorkFlowUserIds(task);
        if (CollUtil.isNotEmpty(workflowUserIds)&&workflowUserIds.contains(getUserId())){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @ApiOperation(value = "根据传入字段修改", notes = "根据传入字段修改")
    @PutMapping("/updateById")
    public R<Boolean> updateById(@RequestBody Task task)
    {
        Long id = task.getId();
        if (id == null)
        {
            throw BizException.validFail("ID不能为空");
        }

        Task oldTask = baseService.getById(id);

        if (oldTask == null)
        {
            return fail("要修改的任务不存在");
        }
        // 如果当前登录人不是处理人/负责人/提出人/创建人或角色为该项目项目经理则不能修改
        if (!isUserEx(oldTask))
        {
            return fail("当前登录人不是处理人/负责人/提出人/创建人/项目经理，不能修改");
        }
        baseService.dealHandleBy(task);
        boolean update = baseService.updateById(task);
        Task newTask = baseService.getById(id);

        // 修改事件
        IssueUpdateEvent.sendEvent(TypeClassify.TASK, id, oldTask, newTask);
        return success(update);
    }

    @Override
    public R<Task> handlerUpdate(TaskUpdateDTO model)
    {
        Task oldTask = baseService.getById(model.getId());
        if (oldTask == null)
        {
            return fail("要修改的任务不存在");
        }
        // 如果当前登录人不是处理人/负责人/提出人/创建人或角色为该项目项目经理则不能修改
        if (!isUserEx(oldTask))
        {
            return fail("当前登录人不是处理人/负责人/提出人/创建人/项目经理，不能修改");
        }

        List<String> tagId = model.getTagId();
        model.setTagId(null);
        Task task = BeanPlusUtil.toBean(model, Task.class);
        baseService.updateAllById(task);
        Long id = task.getId();
        List<Long> collect = issueTagItemService.list(Wraps.<IssueTagItem>lbQ().eq(IssueTagItem::getBizId, id)
                        .eq(IssueTagItem::getClassify, TypeClassify.TASK))
                .stream().map(IssueTagItem::getTagId)
                .collect(Collectors.toList());

        List<String> stringIds = collect.stream().map(Objects::toString).collect(Collectors.toList());
        // 与原来的标签数据不一致时，更新标签
        if (!CollUtil.isEqualList(tagId, stringIds))
        {
            if (CollUtil.isNotEmpty(tagId))
            {
                issueTagItemService.remove(Wraps.<IssueTagItem>lbQ().eq(IssueTagItem::getBizId, id));
                issueTagItemService.save(id, tagId.toArray(new String[0]),
                        task.getTypeCode());
            }
            else
            {
                issueTagItemService.remove(Wraps.<IssueTagItem>lbQ().eq(IssueTagItem::getBizId, id));
            }
            // 重新查询修改后的标签信息
            collect = issueTagItemService.list(Wraps.<IssueTagItem>lbQ().eq(IssueTagItem::getBizId, id)
                            .eq(IssueTagItem::getClassify, TypeClassify.TASK))
                    .stream().map(IssueTagItem::getTagId)
                    .collect(Collectors.toList());
        }
        if (collect.isEmpty())
        {
            task.getEchoMap().put("tagId", null);
        }
        else
        {
            task.getEchoMap().put("tagId", issueTagService.listByIds(collect));
        }

        task.setTagId(collect);
        return success(task);
    }

    @ApiOperation(value = "任务状态流转", notes = "任务状态流转")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskId", value = "任务id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH),
            @ApiImplicitParam(name = "sourceStateCode", value = "源状态code", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH),
            @ApiImplicitParam(name = "targetStateCode", value = "目标状态code", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @PutMapping("/transitionState/{taskId}/{sourceStateCode}/{targetStateCode}")
    @SysLog(value = "任务状态流转", request = false, optType = OptLogTypeEnum.EDIT)
    public R<Boolean> transitionState(@PathVariable("taskId") Long taskId,
            @PathVariable("sourceStateCode") String sourceStateCode,
            @PathVariable("targetStateCode") String targetStateCode)
    {
        baseService.transitionState(taskId, sourceStateCode, targetStateCode);
        return success();
    }

    @Override
    public IPage<Task> query(PageParams<TaskPageQuery> params)
    {
        IPage<Task> page = params.buildPage(Task.class);
        TaskPageQuery taskPage = params.getModel();

        LbqWrapper<Task> wrapper = Wraps.lbQ();
        List<Long> tagId = taskPage.getTagId();
        List<Long> ids = new ArrayList<>();
        if (tagId != null && tagId.size() != 0)
        {
            Map<Long, List<IssueTagItem>> collect =
                    issueTagItemService.list(Wraps.<IssueTagItem>lbQ().in(IssueTagItem::getTagId, tagId)).stream()
                            .collect(Collectors.groupingBy(IssueTagItem::getBizId));
            for (Long key : collect.keySet())
            {
                if (collect.get(key).size() == tagId.size())
                {
                    ids.add(key);
                }
            }
            if (collect.size() != 0)
            {
                wrapper.in(Task::getId, ids);
            }
            else
            {
                return page;
            }
        }

        wrapper.select(Task.class, t -> !"description".equals(t.getColumn()))
                .like(Task::getCode, taskPage.getCode())
                .like(Task::getName, taskPage.getName())
                .in(Task::getParentId, taskPage.getParentId())
                .in(Task::getTypeCode, taskPage.getTypeCode())
                .in(Task::getStateCode, taskPage.getStateCode())
                .in(Task::getSourceCode, taskPage.getSourceCode())
                .in(Task::getPriorityCode, taskPage.getPriorityCode())
                .in(Task::getProgramId, taskPage.getProgramId())
                .in(Task::getProductId, taskPage.getProductId())
                .in(Task::getProjectId, taskPage.getProjectId())
                .in(Task::getPlanId, taskPage.getPlanId())
                .in(Task::getRequirementId, taskPage.getRequirementId())
                .in(Task::getBugId, taskPage.getBugId())
                .in(Task::getLeadingBy, taskPage.getLeadingBy())
                .eq(Task::getIsFiled, taskPage.getIsFiled())
                .in(Task::getProductModuleFunctionId, taskPage.getProductModuleFunctionId())
                .in(Task::getProductRepairVersionId, taskPage.getProductRepairVersionId())
                .in(Task::getProductVersionId, taskPage.getProductVersionId());

        if (CollUtil.isNotEmpty(taskPage.getHandleBy())){
            wrapper.exists(String.format(TASK_HANDLE_EXISTS, StrUtil.join( StrUtil.COMMA,taskPage.getHandleBy())));
        }

        // 延期 筛选
        if (taskPage.getDelay() != null)
        {
            if (taskPage.getDelay())
            {
                wrapper.eq(Task::getDelay, true);
            }
            else
            {
                wrapper.and(w -> w.eq(Task::getDelay, false).or().isNull(Task::getDelay));
            }
        }

        if(taskPage.getStateMark() != null && "1".equals(taskPage.getStateMark())){
            String typeCode = taskPage.getTypeCode().get(0);
            WorkflowItemDTO workItem = new WorkflowItemDTO();
            workItem.setBizType(TypeClassify.TASK);
            workItem.setTypeCode(typeCode);
            String endStateCode = workflowLogic.findEndStateCode(workItem);
            wrapper.ne(Task::getStateCode,endStateCode);
            wrapper.exists(String.format(TASK_HANDLE_EXISTS, StrUtil.join( StrUtil.COMMA,getUserId())));
        }

        PeriodQuery.appendQuery(wrapper, taskPage.getRateProgress(), Task::getRateProgress);
        PeriodQuery.appendQuery(wrapper, taskPage.getEstimateHour(), Task::getEstimateHour);
        PeriodQuery.appendQuery(wrapper, taskPage.getUsedHour(), Task::getUsedHour);
        PeriodQuery.appendQuery(wrapper, taskPage.getPlanStime(), Task::getPlanStime);
        PeriodQuery.appendQuery(wrapper, taskPage.getPlanEtime(), Task::getPlanEtime);
        PeriodQuery.appendQuery(wrapper, taskPage.getStartTime(), Task::getStartTime);
        PeriodQuery.appendQuery(wrapper, taskPage.getEndTime(), Task::getEndTime);
        PeriodQuery.appendQuery(wrapper, taskPage.getCreateTime(), Task::getCreateTime);

        // 添加自定义字段的条件筛选
        taskPage.appendQuery(wrapper);

        // 只查询顶级树节点
        if (taskPage.getTreeView() != null && taskPage.getTreeView())
        {
            wrapper.isNull(Task::getParentId);
        }

        baseService.page(page, wrapper);
        page.getRecords().forEach(item ->
                item.setPutBy(item.getCreatedBy()));
        echoService.action(page);
        taskUserService.actionHandleBy(page.getRecords());
        page.getRecords().forEach(item ->
        {
            Long id = item.getId();
            long count = baseService.count(Wraps.<Task>lbQ().eq(Task::getParentId, id));
            item.getEchoMap().put("childrenCount", count);
        });


        // 根据工作项ID获取标签数据
        Set<Long> bizIds = page.getRecords().stream().map(SuperEntity::getId).collect(Collectors.toSet());
        Map<Long, List<IssueTag>> issueTagMap = issueTagService.issueTagMap(bizIds);

        // 将标签数据放入工作项中
        page.getRecords().forEach(item ->
        {
            item.setDescription(null);

            Long bizId = item.getId();
            List<IssueTag> issueTags = issueTagMap.getOrDefault(bizId, new ArrayList<>());

            List<Long> tagIds = CollUtil.map(issueTags, SuperEntity::getId, true);
            item.setTagId(tagIds);

            item.getEchoMap().put("tagId", issueTags);
        });

        // 保存最后一次查询的条件
        tableViewApi.saveLastSearch(getUserId(), "task-table", params);

        return page;
    }

    @Override
    @ApiOperation(value = "批量查询,不会查询关联数据", notes = "批量查询,不会查询关联数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "编码，模糊查询", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "name", value = "名称，模糊查询", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "parentId", value = "父id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "typeCode", value = "事项类型", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "stateCode", value = "状态", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "priorityCode", value = "优先级", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "programId", value = "归属项目集id", dataType = DATA_TYPE_STRING,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "projectId", value = "归属项目id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "requirementId", value = "关联需求id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "planId", value = "关联计划id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "delay", value = "是否延期", dataType = DATA_TYPE_BOOLEAN,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "leadingBy", value = "责任人", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY),
            @ApiImplicitParam(name = "handleBy", value = "处理人", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_BODY)
    })
    public R<List<Task>> query(@ApiIgnore @RequestBody Task task)
    {
        LbqWrapper<Task> wrapper = Wraps.lbQ();

        wrapper.select(Task.class, t -> !"description".equals(t.getColumn())).like(Task::getName, task.getName())
                .like(Task::getCode, task.getCode())
                .eq(Task::getParentId, task.getParentId())
                .eq(Task::getTypeCode, task.getTypeCode())
                .eq(Task::getStateCode, task.getStateCode())
                .eq(Task::getPriorityCode, task.getPriorityCode())
                .eq(Task::getProgramId, task.getProgramId())
                .eq(Task::getProjectId, task.getProjectId())
                .eq(Task::getPlanId, task.getPlanId())
                .eq(Task::getBugId, task.getBugId())
                .eq(Task::getDelay, task.getDelay())
                .eq(Task::getCreatedBy, task.getCreatedBy())
                .eq(Task::getRequirementId, task.getRequirementId())
                .eq(Task::getTestreqId, task.getTestreqId())
                .eq(Task::getCreatedBy, task.getCreatedBy())
                .eq(Task::getLeadingBy, task.getLeadingBy())
                .eq(Task::getHandleBy, task.getHandleBy());

        if (task.getHandleBy()!=null){
            wrapper.exists(String.format(TASK_HANDLE_EXISTS, StrUtil.join( StrUtil.COMMA,getUserId())));
        }
        if(task.getStateMark() != null && "1".equals(task.getStateMark())){
            WorkflowItemDTO workItem = new WorkflowItemDTO();
            workItem.setBizType(TypeClassify.TASK);
            workItem.setTypeCode(task.getTypeCode());
            String endStateCode = workflowLogic.findEndStateCode(workItem);
            wrapper.ne(Task::getStateCode,endStateCode);
            wrapper.exists(String.format(TASK_HANDLE_EXISTS, StrUtil.join( StrUtil.COMMA,getUserId())));
        }

//        if(task.getIsOwnTask()){
//            //todo 后续需要可以新增当前人员的所有任务
//        }
        List<Task> tasks = baseService.list(wrapper);

        // 为每个任务添加caseCoverage字段
        tasks.forEach(item -> {
            item.setPutBy(item.getCreatedBy());
        });
        echoService.action(tasks);
        taskUserService.actionHandleBy(tasks);
        return success(tasks);
    }

    @Override
    public R<Task> get(Long id)
    {
        Task task = baseService.getById(id);
        if (task == null)
        {
            throw BizException.wrap(ExceptionCode.NOT_FOUND);
        }

        String typeCode = task.getTypeCode();
        TypeClassify classify = typeService.findByCode(typeCode).getClassify();
        List<Long> collect = issueTagItemService.list(Wraps.<IssueTagItem>lbQ().eq(IssueTagItem::getBizId, id)
                        .eq(IssueTagItem::getClassify, classify)).stream().map(IssueTagItem::getTagId)
                .collect(Collectors.toList());
        if (collect != null && collect.size() != 0)
        {
            task.getEchoMap().put("tagId", issueTagService.listByIds(collect));
        }
        else
        {
            task.getEchoMap().put("tagId", null);
        }
        task.setTagId(collect);
        //根据交易id查询交易信息
//        Long functionId = task.getFunctionId();
//        if(functionId != null){
//            ProductModuleFunction productModuleFunction = productModuleFunctionService.getById(functionId);
//            if(productModuleFunction != null){
//                task.setFunctionCode(productModuleFunction.getCode());
//                task.setFunctionName(productModuleFunction.getName());
//            }
//        }
        echoService.action(task);
        IssueCustomFieldService.echo(task, task.getEchoMap());
        return success(task);
    }

    @ApiOperation(value = "查询任务可流转的状态节点", notes = "查询任务可流转的状态节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "taskId", value = "任务id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @GetMapping("/findNextNode/{taskId}")
    public R<List<WorkflowNode>> findNextNode(@PathVariable Long taskId)
    {
        List<WorkflowNode> nextNodes = baseService.findNextNode(taskId);
        echoService.action(nextNodes);
        return success(nextNodes);
    }

    @ApiOperation(value = "查询任务概览组件", notes = "查询任务概览组件")
    @GetMapping("/findTypeByProjectId/{projectId}")
    @SysLog("查询任务概览组件")
    public R<IssueTypeComponentResult> findTypeByProjectId(@PathVariable("projectId") Long projectId)
    {
        return R.success(baseService.findTypeByProjectId(projectId));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @ApiOperation(value = "查询项目任务状态按人员分布组件", notes = "查询项目任务状态按人员分布组件")
    @GetMapping("/findStateByProjectId/{projectId}")
    @SysLog("查询项目任务状态按人员分布组件")
    public R<TaskStateComponentResult> findStateByProjectId(@PathVariable("projectId") Long projectId)
    {
        return R.success(baseService.findStateByProjectId(projectId));
    }

    @ApiOperation(value = "根据产品id查询任务概览数据", notes = "根据产品id查询任务概览数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "productId", value = "产品id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @GetMapping("/findTaskComponentByProductId/{productId}")
    @SysLog("根据产品id查询任务概览数据")
    public R<IssueTypeComponentResult> findTaskComponentByProductId(@PathVariable("productId") Long productId)
    {
        return R.success(baseService.findTaskComponentByProductId(productId));
    }

    @ApiOperation(value = "根据计划id查询任务概览数据", notes = "根据计划id查询任务概览数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "planId", value = "计划id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @GetMapping("/findTaskComponentByPlanId/{planId}")
    @SysLog("根据计划id查询任务概览数据")
    public R<IssueTypeComponentResult> findTaskComponentByPlanId(@PathVariable("planId") Long planId)
    {
        return R.success(baseService.findTaskComponentByPlanId(planId));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "planId", value = "计划id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @ApiOperation(value = "查询计划任务状态按人员分布组件", notes = "查询计划任务状态按人员分布组件")
    @GetMapping("/findStateByPlanId/{planId}")
    @SysLog("查询计划任务状态按人员分布组件")
    public R<TaskStateComponentResult> findStateByPlanId(@PathVariable("planId") Long planId)
    {
        return R.success(baseService.findStateByPlanId(planId));
    }

    @ApiOperation(value = "根据项目id查询未完成且未关联需求的任务", notes = "根据项目id查询未完成且未关联需求的任务")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @GetMapping("/findTodoByProjectId/{projectId}")
    @SysLog("根据项目id查询未完成且未关联需求的任务")
    public R<List<Task>> findTodoByProjectId(@PathVariable Long projectId)
    {
        return success(baseService.list(
                Wraps.<Task>lbQ().eq(Task::getProjectId, projectId).isNull(Task::getRequirementId)
                        .isNull(Task::getEndTime)));
    }


    @Override
    public R<Task> handlerSave(TaskSaveDTO model)
    {
        // 清理多余数据
        model.setCreateTime(null);
        model.setUpdateTime(null);
        model.setCreatedBy(null);
        model.setUpdatedBy(null);
        model.setId(null);

        List<String> tagId = model.getTagId();
        model.setTagId(null);
        Task task = BeanPlusUtil.toBean(model, Task.class);
        baseService.save(task);

        if (tagId != null && tagId.size() != 0)
        {
            issueTagItemService.save(task.getId(), tagId.toArray(new String[tagId.size()]), task.getTypeCode());
        }
        echoService.action(task);
        return success(task);
    }


    @ApiOperation(value = "研发度量任务统计下钻", notes = "研发度量任务统计下钻")
    @PostMapping("/findPageCountByQuery")
    @SysLog("研发度量任务统计下钻")
    public R<Page<Task>> findPageByQuery(@RequestBody PageParams<TaskCountQuery> query)
    {
        Page<Task> page = baseService.findPageByQuery(query.buildPage(Task.class), query.getModel());
        if (page.getTotal() > 0)
        {
            page.getRecords().forEach(view -> view.setPutBy(view.getCreatedBy()));
            echoService.action(page.getRecords());
        }
        return R.success(page);
    }

    @ApiOperation(value = "研发度量工时预估下钻分页", notes = "研发度量工时预估下钻分页")
    @PostMapping("/findHourPageByQuery")
    @SysLog("研发度量工时预估下钻分页")
    public R<Page<WorkItemDTO>> findHourPageByQuery(@RequestBody PageParams<TaskCountQuery> query)
    {
        Page<WorkItemDTO> page = baseService.findHourPageByQuery(query.buildPage(), query.getModel());
        if (page.getTotal() > 0)
        {
            echoService.action(page.getRecords());
        }
        return R.success(page);
    }

    @GetMapping("/getTaskByProjectId/{projectId}/{typeCode}")
    @SysLog("根据项目id查询手工执行任务")
    public R<List<Task>> getTaskByProjectId(@PathVariable Long projectId, @PathVariable String typeCode, @RequestParam(value = "testreqId") String testreqId)
    {

        WorkflowItemDTO workItem = new WorkflowItemDTO();
        workItem.setBizType(TypeClassify.TASK);
        workItem.setTypeCode(typeCode);
        String endStateCode = workflowLogic.findEndStateCode(workItem);

        LbqWrapper<Task> queryWrapLbuWrapper = Wraps.lbQ();
        queryWrapLbuWrapper.eq(Task::getProjectId, projectId)
                .eq(Task::getTypeCode, typeCode)
                .ne(Task::getStateCode,endStateCode)
                .exists(String.format(TASK_HANDLE_EXISTS,getUserId()));
        if(StringUtil.isNotBlank(testreqId)){
            queryWrapLbuWrapper.eq(Task::getTestreqId,testreqId);
        }
        List<Task> taskList = baseService.list(queryWrapLbuWrapper);
        echoService.action(taskList);
        return R.success(taskList, "查询测试任务成功！");
    }

//    @ApiOperation(value = "回显任务信息", notes = "回显任务信息")
//    @GetMapping("/queryTaskById/{id}")
//    @SysLog(value = "回显计划信息", request = false)
//    public R<Task> queryTaskById(@PathVariable("id") Long id)
//    {
//        Task task = baseService.getById(id);
//        List<TestTaskCase> list = testTaskCaseApi.queryByTaskId(id);
//        task.getEchoMap().put("testProductCase",list);
//        return R.success(task);
//    }

    @ApiOperation(value = "展开任务详情", notes = "展开任务详情")
    @GetMapping("/queryTaskById/{id}")
    @SysLog(value = "展开任务详情", request = false)
    public R<Task> queryTaskById(@PathVariable("id") Long id)
    {
        Task task = baseService.getById(id);

        switch (task.getTypeCode()){
            case TaskTypeCodeConstants.TASK_MANUAL:
                getCaseExecution(task);
                break;
            case TaskTypeCodeConstants.TASK_CASE_DESIGN:
                getTaskCaseCoverage(task);
                break;
            case TaskTypeCodeConstants.TASK_TEST_ANAL:
                getTestPoints(task);
                break;
            default:
//                return fail("任务类型不存在");
                break;
        }


        echoService.action(task);
        taskUserService.actionHandleBy(task);
        return R.success(task);
    }

    /**
     *
     * @param task
     * @return
     * @throws
     * <AUTHOR>
     * @date 2025/9/2 15:03
     * @update sxh 2025/9/2 15:03
     * @since 1.0
     */
    public void getCaseExecution(Task task){
        List<TestTaskCase> list = testTaskCaseApi.queryByTaskId(task.getId());
        int caseNum = list.size();

        List<TestTaskCase> systemList = testTaskCaseApi.queryByTaskIdAndStatus(task.getId(),"system");
        int systemNum = systemList.size();

        List<TestTaskCase> skipList = testTaskCaseApi.queryByTaskIdAndStatus(task.getId(),"skip");
        int skipNum = skipList.size();

        Map<String,Object> map = new HashMap<>();
        //执行用例数量
        map.put("execCaseNum",caseNum);
        //分母
        map.put("denominator",caseNum-skipNum);
        //分子
        map.put("numerator",systemNum);

        task.setGeneralMap(map);

    }

    /**
     *
     * @param task
     * @return
     * @throws
     * <AUTHOR>
     * @date 2025/9/2 15:03
     * @update tmm 2025/9/2 15:03
     * @since 1.0
     */
    public void getTaskCaseCoverage(Task task)
    {
        Long taskId = task.getId();
        Map<String, Object> caseCoverage = new HashMap<>();
                // 查询当前任务的所有测试点
        List<TestRequirementFunctionPoints> allPoints = new ArrayList<>();if (task.getProjectId() != null) {if (task.getTestreqId() == null) {
            allPoints = testRequirementFunctionPointsApi.listByProjectIdAndTestReqId(task.getProjectId(), 0L);
        } else {
            allPoints = testRequirementFunctionPointsApi.listByProjectIdAndTestReqId(task.getProjectId(), task.getTestreqId());
        }}
        int totalPointsCount = allPoints != null ? allPoints.size() : 0;
        // 查询被引用的测试点数量
        List<TestProductCase> referencedCases = testProductCaseApi.queryProductCaseByTaskId(Collections.singletonList(taskId));

        Set<Long> referencedPointIds = referencedCases.stream()
                .map(TestProductCase::getFunctionPointsId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        int referencedPointsCount = referencedPointIds.size();


        //执行用例数量
        caseCoverage.put("caseNum",referencedCases.size());
        caseCoverage.put("totalPoints", totalPointsCount);
        caseCoverage.put("referencedPoints", referencedPointsCount);
        task.setGeneralMap(caseCoverage);
    }

    /**
     *
     * @param task
     * @return
     * @throws
     * <AUTHOR>
     * @date 2025/9/2 15:03
     * @update sxh 2025/9/2 15:03
     * @since 1.0
     */
    public void getTestPoints(Task task){
        List<TestRequirementFunctionPoints> list = testRequirementFunctionPointsApi.queryPointsByTaskId(task.getId());

        Map<String,Object> map = new HashMap<>();
        //测试点数量
        map.put("testPointsNum",list.size());

        task.setGeneralMap(map);

    }

    @ApiOperation(value = "查询各类型任务数", notes = "查询各类型任务数")
    @GetMapping("/queryTaskCountOwner")
    @SysLog(value = "查询各类型任务数", request = false)
    public R<List<Map<String,Object>>> queryTaskCountOwner()
    {

        List<Long> workflowIds = projectWorkflowService.list(Wraps.<ProjectWorkflow>lbQ().select(ProjectWorkflow::getWorkflowId).eq(ProjectWorkflow::getTypeClassify,TypeClassify.TASK))
                .stream().map(ProjectWorkflow::getWorkflowId).collect(Collectors.toList());
        //查询结束节点状态
        List<String> stateCodes = workflowNodeService.list(Wraps.<WorkflowNode>lbQ().in(WorkflowNode::getWorkflowId, workflowIds).select(WorkflowNode::getStateCode)
                        .eq(WorkflowNode::getNodeType, WorkflowNodeType.END_NODE))
                .stream().map(WorkflowNode::getStateCode).collect(Collectors.toList());

        List<Task> taskList = baseService.list(Wraps.<Task>lbQ().select(Task::getTypeCode).eq(Task::getHandleBy,getUserId()).notIn(Task::getStateCode,stateCodes));

        Map<String,Long> map = taskList.stream().collect(Collectors.groupingBy(Task::getTypeCode,Collectors.counting()));


        List<Long> bugWorkflowIds = projectWorkflowService.list(Wraps.<ProjectWorkflow>lbQ().select(ProjectWorkflow::getWorkflowId).eq(ProjectWorkflow::getTypeClassify,TypeClassify.BUG))
                .stream().map(ProjectWorkflow::getWorkflowId).collect(Collectors.toList());
        //查询结束节点状态
        List<String> bugStateCodes = workflowNodeService.list(Wraps.<WorkflowNode>lbQ().select(WorkflowNode::getStateCode).in(WorkflowNode::getWorkflowId, bugWorkflowIds)
                                .eq(WorkflowNode::getNodeType, WorkflowNodeType.END_NODE))
                .stream().map(WorkflowNode::getStateCode).collect(Collectors.toList());

        long bugCount = bugService.count(Wraps.<Bug>lbQ().eq(Bug::getHandleBy,getUserId()).notIn(Bug::getStateCode,bugStateCodes));

        map.put(TypeClassify.BUG.getCode(),Long.valueOf(bugCount));
        long count = workflowCountersignRecordService.count(
                Wraps.<WorkflowCountersignRecord>lbQ().eq(WorkflowCountersignRecord::getUserId, getUserId())
                        .eq(WorkflowCountersignRecord::getBizType,TypeClassify.TEST_REVIEW.getCode())
                        .eq(WorkflowCountersignRecord::getStatus, CountersignStatus.PENDING)
                        .exists("select 1 from test_review tr where tr.id=workflow_countersign_record.biz_id "));
        map.put(TypeClassify.TEST_REVIEW.getCode(),count);
        List<Map<String,Object>> list = new ArrayList<>();
        for (Map.Entry<String, Long> entry : map.entrySet()) {
            Map<String,Object> newMap = new HashMap<>();
            newMap.put("type",entry.getKey());
            newMap.put("value",entry.getValue());
            list.add(newMap);
        }



        return R.success(list);
    }


}
