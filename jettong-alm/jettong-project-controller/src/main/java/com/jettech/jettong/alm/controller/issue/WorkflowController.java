package com.jettech.jettong.alm.controller.issue;


import com.jettech.basic.base.R;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.jettong.alm.issue.dto.StateTransitionDTO;
import com.jettech.jettong.alm.project.service.WorkflowLogic;
import com.jettech.jettong.alm.workflow.dto.CountersignResultDTO;
import com.jettech.jettong.alm.workflow.dto.WorkflowCountersignRecordDTO;
import com.jettech.jettong.alm.workflow.dto.WorkflowItemDTO;
import com.jettech.jettong.alm.workflow.dto.WorkflowTransitionHistoryDTO;
import com.jettech.jettong.alm.workflow.entity.WorkflowCountersignRecord;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;
import com.jettech.jettong.alm.workflow.service.WorkflowCountersignInstanceService;
import com.jettech.jettong.alm.workflow.service.WorkflowNodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 工作流相关
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.alm.controller.issue
 * @className WorkflowController
 * @date 2025/9/10 9:27
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/alm/workflow")
@Api(value = "workflow", tags = "工作流相关")
@RequiredArgsConstructor
public class WorkflowController
{

    private final WorkflowLogic workflowLogic;

    private final EchoService echoService;

    private final WorkflowCountersignInstanceService workflowCountersignInstanceService;

    private final WorkflowNodeService workflowNodeService;

    @PostMapping("/transitionState")
    public R<Boolean> transitionState(@RequestBody StateTransitionDTO stateTransitionDTO){
        return R.success(workflowLogic.transitionState(stateTransitionDTO));
    }


    @PutMapping("/transitionStateByWorkflowIdAndBizId")
    public R<Boolean> transitionStateByWorkflowIdAndBizId(@RequestBody StateTransitionDTO stateTransitionDTO){
        return R.success(workflowLogic.transitionState(stateTransitionDTO));
    }

    @PostMapping("/submitCountersign")
    public R<CountersignResultDTO> submitCountersign(@RequestBody WorkflowCountersignRecordDTO dto){
        return R.success(workflowLogic.submitCountersign(dto));
    }

    @GetMapping("/findNextNode/{workFlowId}/{leadingBy}/{sourceStateCode}")
    public R<List<WorkflowNode>> findNextNodeByWorkflowIdAndLeadingBy(
            @PathVariable("workFlowId")Long workFlowId,
            @PathVariable("leadingBy")Long leadingBy,
            @PathVariable("sourceStateCode")String sourceStateCode){
        return R.success(workflowLogic.findNextNodeByWorkflowIdAndLeadingBy(workFlowId, leadingBy, sourceStateCode));
    }

    @PostMapping("/findNextNode")
    @ApiOperation(value = "查询下一步节点")
    public R<List<WorkflowNode>> findNextNode(@RequestBody WorkflowItemDTO workItem){
        return R.success(workflowLogic.findNextNode(workItem));
    }

    @PostMapping("/countersignHistory")
    @ApiOperation(value = "查询流转记录")
    public R<List<WorkflowTransitionHistoryDTO>> countersignHistory(@RequestBody WorkflowItemDTO dto){
        List<WorkflowTransitionHistoryDTO> historyList = workflowLogic.countersignHistory(dto);
        echoService.action(historyList);
        return R.success(historyList);
    }

    @GetMapping("/findLastCountersignRecord")
    @ApiOperation(value = "获取最近的会签记录 成功和失败的各一条")
    public R<List<WorkflowCountersignRecord>> findLastCountersignRecord(@RequestParam Long instanceId){
        return R.success(workflowLogic.findLastCountersignRecord(instanceId));
    }

    @GetMapping("checkCountersign")
    @ApiOperation(value = "检查会签")
    public R<CountersignResultDTO> checkCountersign(@RequestParam Long instanceId,@RequestParam Long nodeId){
        WorkflowNode node = workflowNodeService.getById(nodeId);
        return R.success(workflowCountersignInstanceService.checkCountersignCompletion(instanceId,node,null));
    }
}
