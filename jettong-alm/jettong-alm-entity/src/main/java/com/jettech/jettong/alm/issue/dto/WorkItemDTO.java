package com.jettech.jettong.alm.issue.dto;

import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.alm.issue.entity.Priority;
import com.jettech.jettong.alm.issue.entity.State;
import com.jettech.jettong.alm.issue.entity.Testreq;
import com.jettech.jettong.alm.issue.entity.Type;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.*;

/**
 * 查询工作项列表（任务、需求、缺陷、风险、用户需求）
 *
 * <AUTHOR>
 * @version 1.0
 * @description 查询工作项列表（任务、需求、缺陷、风险、用户需求）
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.issue.dto
 * @className WorkItemDTO
 * @date 2022/4/21 12:00
 * @copyright 2022 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "WorkItemDTO", description = "工作项信息")
public class WorkItemDTO implements EchoVO {

    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 资源类型
     */
    @ApiModelProperty(value = "资源类型")
    private TypeClassify sourceType;

    /**
     * 资源类型
     */
    @ApiModelProperty(value = "资源ID")
    private Long id;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String code;
    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    private String name;
    /**
     * 状态code
     */
    @ApiModelProperty(value = "状态code")
    @Echo(api = STATE_CODE_CLASS, beanClass = State.class)
    private String stateCode;

    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    private LocalDateTime planEtime;

    @ApiModelProperty(value = "计划开始时间")
    private LocalDateTime planStime;

    /**
     * 用户需求分类
     */
    @ApiModelProperty(value = "用户需求分类")
    @NotEmpty(message = "请填写用户需求分类")
    @Size(max = 20, message = "用户需求分类长度不能超过20")
    @Echo(api = TYPE_CODE_CLASS, beanClass = Type.class)
    private String typeCode;

    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联项目")
    @Echo(api = PROJECT_INFO_FEIGN_CLASS, beanClass = ProjectInfo.class)
    private Long projectId;

    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    private Long productId;
    /**
     * 测试需求
     */
    @ApiModelProperty(value = "测试需求")
    @Echo(api = TESTREQ_ID_FEIGN_CLASS, beanClass = Testreq.class)
    private Long testreqId;
    /**
     * 优先级code
     */
    @ApiModelProperty(value = "优先级code")
    @Echo(api = PRIORITY_CODE_CLASS, beanClass = Priority.class)
    private String priorityCode;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "提出人")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long putBy;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long leadingBy;

    /**
     * 处理人
     */
    @ApiModelProperty(value = "处理人")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long handleBy;

    /**
     * 依赖关系的ID，在查询工作项关系时使用
     */
    @ApiModelProperty(value = "依赖关系的ID")
    private Long relationId;


    /**
     * 依赖关系的链接原因，在查询工作项关系时使用
     */
    @ApiModelProperty(value = "依赖关系的链接原因")
    private String relationLinked;

    @ApiModelProperty(value = "估算工时")
    private Double estimateHour;

    @ApiModelProperty(value = "可填报工时")
    private Double isFillInHour;

    @ApiModelProperty(value = "创建时间")
    protected LocalDateTime createTime;
}
