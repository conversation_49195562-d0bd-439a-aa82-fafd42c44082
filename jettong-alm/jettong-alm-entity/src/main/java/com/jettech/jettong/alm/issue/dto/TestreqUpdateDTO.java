package com.jettech.jettong.alm.issue.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jettech.jettong.alm.issue.entity.CustomFormFieldExtend;
import com.jettech.jettong.base.entity.file.File;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.commons.math3.stat.descriptive.summary.Product;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong1
 * @package com.jettech.jettong.alm.issue.dto
 * @className aa
 * @date 2025/7/7 18:38
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestreqUpdateDTO", description = "测试需求信息")
public class TestreqUpdateDTO extends CustomFormFieldExtend implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "父测试需求ID")
    private Long parentId;
    @ApiModelProperty(value = "名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    private String name;
    @ApiModelProperty(value = "描述")
    private String description;
    @ApiModelProperty(value = "状态")
    private String stateCode;
    @ApiModelProperty(value = "优先级")
    private String priorityCode;
    @ApiModelProperty(value = "事项类型code")
    private String typeCode;
    @ApiModelProperty(value = "进度0-100")
    private Integer rateProgress;
    @ApiModelProperty(value = "产品版本id")
    private Long productVersionId;
    @ApiModelProperty(value = "产品模块id")
    private Long productModuleFunctionId;
    @ApiModelProperty(value = "规模")
    private Double estimatePoint;
    @ApiModelProperty(value = "估算工时")
    private Double estimateHour;
    @ApiModelProperty(value = "归属项目集")
    private Long programId;
    @ApiModelProperty(value = "归属项目")
    private Long projectId;
    @ApiModelProperty(value = "归属产品")
    private Long productId;
    @ApiModelProperty(value = "归属计划")
    private Long planId;
    @ApiModelProperty(value = "提出人")
    private Long putBy;
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;
    @ApiModelProperty(value = "处理人")
    private Long handleBy;
    @ApiModelProperty(value = "计划开始时间")
    private LocalDateTime planStime;
    @ApiModelProperty(value = "计划完成时间")
    private LocalDateTime planEtime;
    @ApiModelProperty(value = "测试需求来源")
    private String sourceCode;
    @ApiModelProperty(value = "附件信息")
    private List<File> files;
    @ApiModelProperty(value = "标签id")
    private List<String> tagId;
    @ApiModelProperty(value = "产品修复版本id")
    private Long productRepairVersionId;

    @ApiModelProperty(value = "项目经理")
    private Long projectManagerBy;

    /**
     * 测试经理
     */
    @ApiModelProperty(value = "测试经理")
    private Long testManagerBy;

    @ApiModelProperty(value = "辅办产品Id")
    private List<Long> assistProductIds;

}
