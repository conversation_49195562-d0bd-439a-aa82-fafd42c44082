package com.jettech.jettong.alm.issue.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.alm.project.entity.ProjectPlan;
import com.jettech.jettong.alm.project.entity.ProjectProgram;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.product.entity.ProductVersion;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jettech.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;
import static com.jettech.jettong.common.constant.EchoConstants.*;


/**
 * 任务信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 任务信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.issue.entity
 * @className Task
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("issue_task")
@ApiModel(value = "Task", description = "任务信息")
@AllArgsConstructor
public class Task extends CustomFormFieldExtend implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 父任务ID
     */
    @ApiModelProperty(value = "父任务ID")
    @TableField(value = "`parent_id`")
    @Excel(name = "父任务ID")
    private Long parentId;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @NotEmpty(message = "请填写编号")
    @Size(max = 20, message = "编号长度不能超过20")
    @TableField(value = "`code`")
    @Excel(name = "编号")
    private String code;

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    @TableField(value = "`name`")
    @Excel(name = "名称/标题")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "`description`")
    @Excel(name = "描述")
    private String description;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    @TableField(value = "`state_code`")
    @Echo(api = STATE_CODE_CLASS, beanClass = State.class)
    @Excel(name = "状态")
    private String stateCode;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @TableField(value = "`priority_code`")
    @Echo(api = PRIORITY_CODE_CLASS, beanClass = Priority.class)
    @Excel(name = "优先级")
    private String priorityCode;

    /**
     * 归属项目集
     */
    @ApiModelProperty(value = "归属项目集")
    @TableField(value = "`program_id`")
    @Echo(api = PROJECT_PROGRAM_ID_CLASS, beanClass = ProjectProgram.class)
    @Excel(name = "归属项目集")
    private Long programId;

    /**
     * 归属项目
     */
    @ApiModelProperty(value = "归属项目")
    @TableField(value = "`project_id`")
    @Echo(api = PROJECT_INFO_ID_CLASS, beanClass = ProjectInfo.class)
    @Excel(name = "归属项目")
    private Long projectId;

    /**
     * 产品id
     */
    @ApiModelProperty(value = "产品id")
    @TableField(value = "`product_id`")
    @Echo(api = PRODUCT_ID_FEIGN_CLASS, beanClass = ProductInfo.class)
    @Excel(name = "产品id")
    private Long productId;

    /**
     * 关联计划
     */
    @ApiModelProperty(value = "关联计划")
    @TableField(value = "`plan_id`")
    @Excel(name = "关联计划")
    @Echo(api = PROJECT_PLAN_ID_CLASS, beanClass = ProjectPlan.class)
    private Long planId;

    /**
     * 关联软件需求
     */
    @ApiModelProperty(value = "关联软件需求")
    @TableField(value = "`requirement_id`")
    @Echo(api = REQUIREMENT_ID_CLASS, beanClass = Requirement.class)
    @Excel(name = "关联软件需求")
    private Long requirementId;

    /**
     * 关联缺陷
     */
    @ApiModelProperty(value = "关联缺陷")
    @TableField(value = "`bug_id`")
    @Echo(api = BUG_ID_CLASS, beanClass = Bug.class)
    @Excel(name = "关联缺陷")
    private Long bugId;

    /**
     * 事项类型code
     */
    @ApiModelProperty(value = "事项类型code")
    @NotEmpty(message = "请填写事项类型code")
    @Size(max = 20, message = "事项类型code长度不能超过20")
    @TableField(value = "`type_code`")
    @Echo(api = TYPE_CODE_CLASS, beanClass = Type.class)
    @Excel(name = "事项类型code")
    private String typeCode;

    /**
     * 任务来源code
     */
    @ApiModelProperty(value = "任务来源code")
    @TableField(value = "`source_code`")
    @Echo(api = SOURCE_CODE_CLASS, beanClass = Source.class)
    private String sourceCode;

    /**
     * 是否延期
     */
    @ApiModelProperty(value = "是否延期")
    @TableField(value = "`delay`")
    @Excel(name = "是否延期", replace = {"是_true", "否_false", "_null"})
    private Boolean delay;

    /**
     * 进度0-100
     */
    @ApiModelProperty(value = "进度0-100")
    @TableField(value = "`rate_progress`")
    @Excel(name = "进度0-100")
    private Integer rateProgress;

    /**
     * 估算工时
     */
    @ApiModelProperty(value = "估算工时")
    @TableField(value = "`estimate_hour`")
    @Excel(name = "估算工时")
    private Double estimateHour;

    /**
     * 实际工时
     */
    @ApiModelProperty(value = "实际工时")
    @TableField(value = "`used_hour`")
    @Excel(name = "实际工时")
    private Double usedHour;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "`leading_by`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    @Excel(name = "负责人")
    private Long leadingBy;

    /**
     * 处理人
     */
    @ApiModelProperty(value = "处理人")
    @TableField(value = "`handle_by`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    @Excel(name = "处理人")
    private Long handleBy;
    /**
     * 提出人实际是创建人
     */
    @ApiModelProperty(value = "提出人实际是创建人")
    @TableField(exist = false)
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    @Excel(name = "提出人实际是创建人")
    private Long putBy;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "`plan_stime`")
    @Excel(name = "计划开始时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime planStime;

    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    @TableField(value = "`plan_etime`")
    @Excel(name = "计划完成时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime planEtime;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @TableField(value = "`start_time`")
    @Excel(name = "开始时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime startTime;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    @TableField(value = "`end_time`")
    @Excel(name = "完成时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime endTime;

    /**
     * 是否归档
     */
    @ApiModelProperty(value = "是否归档")
    @TableField(value = "`is_filed`")
    private Boolean isFiled;

    /**
     * 附件信息
     */
    @ApiModelProperty(value = "附件信息")
    @TableField(exist = false)
    private List<File> files;


    @ApiModelProperty(value = "标签Id")
    @TableField(exist = false)
    private List<Long> tagId;

    /**
     * 产品版本id
     */
    @ApiModelProperty(value = "产品版本id")
    @TableField(value = "`product_version_id`")
    @Echo(api = PRODUCT_VERSION_FEIGN_CLASS, beanClass = ProductVersion.class)
    private Long productVersionId;
    /**
     * 产品修复版本id
     */
    @ApiModelProperty(value = "产品修复版本id")
    @TableField(value = "`product_repair_version_id`")
    @Echo(api = PRODUCT_VERSION_FEIGN_CLASS, beanClass = ProductVersion.class)
    private Long productRepairVersionId;
    /**
     * 产品功能模块id
     */
    @ApiModelProperty(value = "产品功能模块id")
    @TableField(value = "`product_module_function_id`")
    @Echo(api = PRODUCT_MODULE_FUNCTION_FEIGN_CLASS, beanClass = ProductModuleFunction.class)
    private Long productModuleFunctionId;

    @ApiModelProperty(value = "交易id")
    @TableField(value = "`function_id`")
    @Echo(api = PRODUCT_MODULE_FUNCTION_FEIGN_CLASS, beanClass = ProductModuleFunction.class)
    private Long functionId;

    @ApiModelProperty(value = "系统id")
    @TableField(value = "`system_id`")
    private Long systemId;

    @ApiModelProperty(value = "用例执行失败必须新增缺陷 0否 1是")
    @TableField(value = "`fail_add_bug`")
    private String failAddBug;

    /**
     * 产品用例库id
     */
    @ApiModelProperty(value = "产品用例库id")
    @TableField(value = "`library_id`")
    @Excel(name = "产品用例库id")
    private Long libraryId;

    /**
     * 关联测试需求
     */
    @ApiModelProperty(value = "关联测试需求")
    @TableField(value = "`testreq_id`")
    @Echo(api = TESTREQ_ID_CLASS, beanClass = Testreq.class)
    @Excel(name = "关联测试需求")
    private Long testreqId;

    @ApiModelProperty(value = "是否自己的")
    @TableField(exist = false)
    private Boolean isOwnTask;
//
//    @ApiModelProperty(value = "交易编码")
//    @TableField(exist = false)
//    private String functionCode;

    /**
     * 状态标识 过滤不为已完成状态
     */
    @ApiModelProperty(value = "状态标识")
    @TableField(exist = false)
    private String stateMark;

    /**
     * 处理人
     */
    @ApiModelProperty(value = "处理人多选")
    @TableField(exist = false)
    private List<Long> handleByList = new ArrayList<>();

    @ApiModelProperty(value = "通用返回Map")
    @TableField(exist = false)
    private Map<String,Object> generalMap  = new HashMap<>();

    @Builder
    public Task(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
            Long parentId, String code, String name, String description, String stateCode,
            String priorityCode, Long programId, Long projectId, Long planId, Long requirementId, Long bugId,
            String typeCode, Boolean isFiled,
            Boolean delay, Integer rateProgress, Double estimateHour, Double usedHour, Long handleBy,
            LocalDateTime planStime,Long functionId,Long systemId,
            LocalDateTime planEtime, LocalDateTime startTime, LocalDateTime endTime,
            List<File> files, List<Long> tagId,
            Long productVersionId, Long productModuleFunctionId, Long productRepairVersionId, Long testreqId)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.parentId = parentId;
        this.code = code;
        this.name = name;
        this.description = description;
        this.stateCode = stateCode;
        this.priorityCode = priorityCode;
        this.programId = programId;
        this.projectId = projectId;
        this.planId = planId;
        this.requirementId = requirementId;
        this.bugId = bugId;
        this.typeCode = typeCode;
        this.delay = delay;
        this.rateProgress = rateProgress;
        this.estimateHour = estimateHour;
        this.usedHour = usedHour;
        this.handleBy = handleBy;
        this.planStime = planStime;
        this.planEtime = planEtime;
        this.startTime = startTime;
        this.endTime = endTime;
        this.isFiled = isFiled;
        this.files = files;
        this.tagId = tagId;
        this.productVersionId = productVersionId;
        this.productModuleFunctionId = productModuleFunctionId;
        this.productRepairVersionId = productRepairVersionId;
        this.functionId = functionId;
        this.systemId = systemId;
        this.testreqId = testreqId;
    }

}
