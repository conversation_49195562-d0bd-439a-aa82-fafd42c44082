package com.jettech.jettong.alm.project.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.alm.issue.entity.Priority;
import com.jettech.jettong.alm.project.enumeration.ProjectStageEnum;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jettech.jettong.common.constant.BaseEchoConstants.ORG_ID_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.PRIORITY_CODE_CLASS;
import static com.jettech.jettong.common.constant.EchoConstants.PROJECT_PROGRAM_ID_CLASS;


/**
 * 项目信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 项目信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.project.entity
 * @className ProjectInfo
 * @date 2021-11-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("project_info")
@ApiModel(value = "ProjectInfo", description = "项目信息")
@AllArgsConstructor
public class ProjectInfo extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @Size(max = 50, message = "编号长度不能超过50")
    @TableField(value = "`code`")
    @Excel(name = "编号")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 128, message = "名称长度不能超过128")
    @TableField(value = "`name`")
    @Excel(name = "名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 255, message = "描述长度不能超过255")
    @TableField(value = "`description`")
    @Excel(name = "描述")
    private String description;

    /**
     * 优先级code
     */
    @ApiModelProperty(value = "优先级code")
    @Size(max = 20, message = "优先级code长度不能超过20")
    @TableField(value = "`priority_code`")
    @Echo(api = PRIORITY_CODE_CLASS, beanClass = Priority.class)
    private String priorityCode;

    /**
     * 项目类型code
     */
    @ApiModelProperty(value = "项目模式code，WELL_瀑布类型、AGILE_敏捷类型、TEST_测试类型")
    @NotNull(message = "请填写项目模式code")
    @TableField(value = "`type_code`")
    @Echo(api = "projectTypeServiceImpl", beanClass = ProjectType.class)
    @Excel(name = "项目模式code")
    private String typeCode;
    /**
     * 附件信息
     */
    @ApiModelProperty(value = "附件信息")
    @TableField(exist = false)
    private List<File> files;
    /**
     * 项目类型  交付类/研发类  原项目类型改为项目模式
     */
    @Excel(name = "项目类型")
    @TableField(value = "`classify`")
    @ApiModelProperty(value = "项目类型")
    @NotNull(message = "请填写项目类型")
    private String classify;

    /**
     * 机构ID
     */
    @ApiModelProperty(value = "机构ID")
    @TableField(value = "`org_id`")
    @Echo(api = ORG_ID_FEIGN_CLASS, beanClass = Org.class)
    @Excel(name = "机构ID")
    private Long orgId;
    /**
     * 主板产品
     */
    @ApiModelProperty(value = "主办系统ID")
    @TableField(value = "`product_id`")
    @Excel(name = "主办系统ID")
    private Long productId;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    @TableField(value = "`leading_by`")
    @Excel(name = "项目经理")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long leadingBy;

    /**
     * 项目集
     */
    @ApiModelProperty(value = "项目集")
    @TableField(value = "`program_id`")
    @Excel(name = "项目集")
    @Echo(api = PROJECT_PROGRAM_ID_CLASS, beanClass = ProjectProgram.class)
    private Long programId;

    @ApiModelProperty(value = "用户id")
    @TableField(exist = false)
    private Long userId;

    /**
     * 是否归档
     */
    @ApiModelProperty(value = "是否归档")
    @TableField(value = "`is_filed`")
    private Boolean isFiled;

    /**
     * 是否结项
     */
    @ApiModelProperty(value = "是否结项")
    @TableField(value = "`is_closure`")
    private Boolean isClosure;

    /**
     * 申请部门机构Id
     */
    @ApiModelProperty(value = "申请部门机构Id")
    @TableField(value = "`apply_org_id`")
    @Excel(name = "申请部门机构Id")
    private Long applyOrgId;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "`plan_start_time`")
    @Excel(name = "计划开始时间")
    private LocalDateTime planStartTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @TableField(value = "`plan_end_time`")
    @Excel(name = "计划结束时间")
    private LocalDateTime planEndTine;

    /**
     * 项目阶段
     */
    @ApiModelProperty(value = "项目阶段")
    @TableField(value = "`project_stage`")
    @Excel(name = "项目阶段")
    private ProjectStageEnum projectStage;

    /**
     * 关联主办产品
     */
//    @ApiModelProperty(value = "关联主办产品")
////    @TableField(exist = false)
//    @TableField(value = "`host_product_id`")
//    private Long hostProductId;


    /**
     * 关联辅办产品
     */
    @ApiModelProperty(value = "关联辅办产品")
    @TableField(exist = false)
    private List<Long> assistProductIds = new ArrayList<>();

    /**
     * 关联项目
     */
    @ApiModelProperty(value = "关联项目")
    @TableField(exist = false)
    private List<ProjectInfo> projects = new ArrayList<>();


    /**
     * 是否置顶
     */
    @ApiModelProperty(value = "是否置顶")
    @TableField(exist = false)
    private Boolean top;

    /**
     * 是否收藏
     */
    @ApiModelProperty(value = "是否收藏")
    @TableField(exist = false)
    private Boolean collection;

    /**
     * 关联产品集
     */
    @ApiModelProperty(value = "关联产品集")
    @TableField(exist = false)
    private List<ProjectProductset> projectProductsets;

    /**
     * 父项目ID
     */
    @ApiModelProperty(value = "父项目ID")
    @TableField(value = "`parent_id`")
    private Long parentId;

    /**
     * 是否有子项目
     */
    @ApiModelProperty(value = "是否有子项目")
    @TableField(exist = false)
    private Boolean hasChildren;

    /**
     * 子项目数量
     */
    @ApiModelProperty(value = "子项目数量")
    @TableField(exist = false)
    private Long childrenCount;

    @Excel(name = "项目状态")
    @TableField(value = "`project_status`")
    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    @ApiModelProperty(value = "项目预算")
    @TableField(value = "budget")
    @Excel(name = "项目预算")
    private BigDecimal budget;


    /**
     * 测试方式
     */
    @ApiModelProperty(value = "测试方式")
    @TableField(exist = false)
    private List<String> testModes;
    /**
     * 测试方式
     */
    @ApiModelProperty(value = "测试方式")
    @TableField(value = "test_mode")
    private String testMode;

    @ApiModelProperty(value = "实施方案是否可裁剪 1是-项目模板可变动；0否-项目模板不可变动")
    @TableField(value = "`is_cropping`")
    private Boolean isCropping;
    @Builder
    public ProjectInfo(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
            String code, String name, String description, String typeCode,
            Long orgId, Long leadingBy, Long programId, Boolean isFiled, Boolean isClosure, //Long hostProductId,
            List<Long> assistProductIds, Long applyOrgId, LocalDateTime planStartTime, LocalDateTime planEndTine,
            ProjectStageEnum projectStage, List<ProjectProductset> projectProductsets, Long parentId,
            String projectStatus,Boolean isCropping
    )
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.code = code;
        this.name = name;
        this.description = description;
        this.typeCode = typeCode;
        this.orgId = orgId;
        this.leadingBy = leadingBy;
        this.programId = programId;
        this.isFiled = isFiled;
        this.isClosure = isClosure;
        this.assistProductIds = assistProductIds;
        this.applyOrgId = applyOrgId;
        this.planStartTime = planStartTime;
        this.planEndTine = planEndTine;
        this.projectStage = projectStage;
        this.projectProductsets = projectProductsets;
        this.parentId = parentId;
        this.projectStatus = projectStatus;
        this.isCropping = isCropping;
    }

}
