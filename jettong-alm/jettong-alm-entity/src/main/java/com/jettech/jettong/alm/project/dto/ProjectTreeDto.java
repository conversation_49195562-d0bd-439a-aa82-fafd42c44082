package com.jettech.jettong.alm.project.dto;


import com.jettech.basic.base.entity.TreeDtoEntity;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.alm.project.entity.ProjectProductset;
import com.jettech.jettong.alm.project.enumeration.ProjectStageEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong1
 * @package com.jettech.jettong.alm.project.dto
 * @className ProjectTreeDto
 * @date 2025/7/29 10:54
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ProjectTreeDto", description = "项目树形结构数据")
public class ProjectTreeDto extends TreeDtoEntity<ProjectTreeDto, Long>
{

    private static final long serialVersionUID = 1L;
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String code;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 项目类型code
     */
    @ApiModelProperty(value = "项目模式code，WELL_瀑布类型、AGILE_敏捷类型、TEST_测试类型")
    private String typeCode;

    /**
     * 项目类型  交付类/研发类
     */
    @ApiModelProperty(value = "项目类型")
    private String classify;

    /**
     * 机构ID
     */
    @ApiModelProperty(value = "机构ID")
    private Long orgId;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    private Long leadingBy;

    /**
     * 项目集
     */
    @ApiModelProperty(value = "项目集")
    private Long programId;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    /**
     * 是否归档
     */
    @ApiModelProperty(value = "是否归档")
    private Boolean isFiled;

    /**
     * 是否结项
     */
    @ApiModelProperty(value = "是否结项")
    private Boolean isClosure;

    /**
     * 申请部门机构Id
     */
    @ApiModelProperty(value = "申请部门机构Id")
    private Long applyOrgId;

    /**
     * 项目阶段
     */
    @ApiModelProperty(value = "项目阶段")
    private ProjectStageEnum projectStage;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private LocalDateTime planStartTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private LocalDateTime planEndTine;

    /**
     * 关联主办产品
     */
    @ApiModelProperty(value = "关联主办产品")
    private Long productId;

    /**
     * 关联辅办产品
     */
    @ApiModelProperty(value = "关联辅办产品")
    private List<Long> assistProductIds = new ArrayList<>();

    /**
     * 是否置顶
     */
    @ApiModelProperty(value = "是否置顶")
    private Boolean top;

    /**
     * 关联项目
     */
    @ApiModelProperty(value = "关联项目")
    private List<ProjectInfo> projects = new ArrayList<>();
    /**
     * 是否收藏
     */
    @ApiModelProperty(value = "是否收藏")
    private Boolean collection;

    /**
     * 关联产品集
     */
    @ApiModelProperty(value = "关联产品集")
    private List<ProjectProductset> projectProductsets;

    /**
     * 从ProjectInfo转换为ProjectTreeDto
     *
     * @param projectInfo 项目信息实体
     * @return ProjectTreeDto实例
     */
    public static ProjectTreeDto fromProjectInfo(ProjectInfo projectInfo) {
        if (projectInfo == null) {
            return null;
        }

        ProjectTreeDto dto = new ProjectTreeDto();
        // 设置基本属性
        dto.setId(projectInfo.getId());
        dto.setName(projectInfo.getName());  // TreeDtoEntity已包含name字段
        dto.setCode(projectInfo.getCode());
        dto.setDescription(projectInfo.getDescription());
        dto.setTypeCode(projectInfo.getTypeCode());
        dto.setClassify(projectInfo.getClassify());
        dto.setOrgId(projectInfo.getOrgId());
        dto.setLeadingBy(projectInfo.getLeadingBy());
        dto.setProgramId(projectInfo.getProgramId());
        dto.setIsFiled(projectInfo.getIsFiled());
        dto.setIsClosure(projectInfo.getIsClosure());
        dto.setProjectStage(projectInfo.getProjectStage());
        dto.setPlanStartTime(projectInfo.getPlanStartTime());
        dto.setPlanEndTine(projectInfo.getPlanEndTine());
        dto.setApplyOrgId(projectInfo.getApplyOrgId());
        dto.setProductId(projectInfo.getProductId());
        dto.setAssistProductIds(projectInfo.getAssistProductIds());
        dto.setProjects(projectInfo.getProjects());
        dto.setTop(projectInfo.getTop());
        dto.setCollection(projectInfo.getCollection());
        dto.setProjectProductsets(projectInfo.getProjectProductsets());
        dto.setParentId(projectInfo.getParentId());
        dto.setUserId(projectInfo.getUserId());
        dto.setEchoMap(projectInfo.getEchoMap());
        dto.setLeadingBy(projectInfo.getLeadingBy());


        // 设置创建和更新信息
        dto.setCreateTime(projectInfo.getCreateTime());
        dto.setUpdateTime(projectInfo.getUpdateTime());
        dto.setCreatedBy(projectInfo.getCreatedBy());
        dto.setUpdatedBy(projectInfo.getUpdatedBy());

        // 初始化子节点列表
        dto.initChildren();

        // 设置排序值，可以根据实际需求调整
//        dto.setSort(0);

        return dto;
    }
}
