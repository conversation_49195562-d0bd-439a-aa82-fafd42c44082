package com.jettech.jettong.alm.issue.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 事项批量操作实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 事项批量操作实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.dto
 * @className IdeaUpdateDTO
 * @date 2021-11-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "IssueBatchOperationVO", description = "事项批量操作实体类")
public class IssueBatchOperationVO implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 事项ids
     */
    @ApiModelProperty(value = "事项ids")
    private List<Long> ids;

    /**
     * 提出人
     */
    @ApiModelProperty(value = "提出人")
    private Long putBy;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;

    /**
     * 处理人
     */
    @ApiModelProperty(value = "处理人")
    private Long handleBy;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private LocalDateTime planStime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private LocalDateTime planEtime;

    /**
     * 优先级code
     */
    @ApiModelProperty(value = "优先级code")
    private String priorityCode;

    /**
     * 处理人
     */
    @ApiModelProperty(value = "处理人")
    private List<Long> handleByList = new ArrayList<>();

}
