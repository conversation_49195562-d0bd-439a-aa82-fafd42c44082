package com.jettech.jettong.alm.issue.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import java.io.Serializable;
import java.util.List;
/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong1
 * @package com.jettech.jettong.alm.issue.dto
 * @className aa
 * @date 2025/7/7 18:38
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "TestreqViewResultVo", description = "测试需求视图项返回值")
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TestreqViewResultVo implements Serializable {
    @ApiModelProperty(value = "是否分组")
    private Boolean isgroup;
    @ApiModelProperty(value = "分组类型")
    private String groupType;
    @ApiModelProperty(value = "分组名称")
    private String groupName;
    @ApiModelProperty(value = "每组数量")
    private Integer groupCount;
    @ApiModelProperty(value = "分组数据")
    private List<TestreqViewResultVo> testreqMapVoList;
}
