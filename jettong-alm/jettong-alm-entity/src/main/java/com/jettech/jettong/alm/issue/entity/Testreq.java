package com.jettech.jettong.alm.issue.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.alm.project.entity.ProjectPlan;
import com.jettech.jettong.alm.project.entity.ProjectProgram;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.product.entity.ProductVersion;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.apache.commons.math3.stat.descriptive.summary.Product;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.*;

/**
 * 测试需求信息
 *
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong1
 * @package com.jettech.jettong.alm.issue.dto
 * @className aa
 * @date 2025/7/7 18:38
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("issue_testreq")
@ApiModel(value = "Testreq", description = "测试需求信息")
@AllArgsConstructor
public class Testreq extends CustomFormFieldExtend implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 父需求ID
     */
    @ApiModelProperty(value = "父需求ID")
    @TableField(value = "`parent_id`")
    @Echo(api = REQUIREMENT_ID_CLASS, beanClass = Testreq.class)
    private Long parentId;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @NotEmpty(message = "请填写编号")
    @Size(max = 20, message = "编号长度不能超过20")
    @TableField(value = "`code`")
    private String code;

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    @TableField(value = "`name`")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "`description`")
    private String description;

    /**
     * 状态code
     */
    @ApiModelProperty(value = "状态code")
    @TableField(value = "`state_code`")
    @Echo(api = STATE_CODE_CLASS, beanClass = State.class)
    private String stateCode;

    /**
     * 优先级code
     */
    @ApiModelProperty(value = "优先级code")
    @TableField(value = "`priority_code`")
    @Echo(api = PRIORITY_CODE_CLASS, beanClass = Priority.class)
    private String priorityCode;

    /**
     * 事项类型code
     */
    @ApiModelProperty(value = "事项类型code")
    @NotNull(message = "请填写事项类型code")
    @TableField(value = "`type_code`")
    @Echo(api = TYPE_CODE_CLASS, beanClass = Type.class)
    private String typeCode;

    /**
     * 是否延期
     */
    @ApiModelProperty(value = "是否延期")
    @TableField(value = "`delay`")
    private Boolean delay;

    /**
     * 进度0-100
     */
    @ApiModelProperty(value = "进度0-100")
    @TableField(value = "`rate_progress`")
    private Integer rateProgress;

    /**
     * 产品版本id
     */
    @ApiModelProperty(value = "产品版本id")
    @TableField(value = "`product_version_id`")
    @Echo(api = PRODUCT_VERSION_FEIGN_CLASS, beanClass = ProductVersion.class)
    private Long productVersionId;

    /**
     * 产品功能模块id
     */
    @ApiModelProperty(value = "产品功能模块id")
    @TableField(value = "`product_module_function_id`")
    @Echo(api = PRODUCT_MODULE_FUNCTION_FEIGN_CLASS, beanClass = ProductModuleFunction.class)
    private Long productModuleFunctionId;

    /**
     * 规模
     */
    @ApiModelProperty(value = "规模")
    @TableField(value = "`estimate_point`")
    private Double estimatePoint;

    /**
     * 估算工时
     */
    @ApiModelProperty(value = "估算工时")
    @TableField(value = "`estimate_hour`")
    private Double estimateHour;

    /**
     * 实际工时
     */
    @ApiModelProperty(value = "实际工时")
    @TableField(value = "`used_hour`")
    private Double usedHour;

    /**
     * 归属项目集
     */
    @ApiModelProperty(value = "归属项目集")
    @TableField(value = "`program_id`")
    @Echo(api = PROJECT_PROGRAM_ID_CLASS, beanClass = ProjectProgram.class)
    private Long programId;

    /**
     * 归属项目
     */
    @ApiModelProperty(value = "归属项目")
    @TableField(value = "`project_id`")
    @Echo(api = PROJECT_INFO_ID_CLASS, beanClass = ProjectInfo.class)
    private Long projectId;

    /**
     * 归属计划
     */
    @ApiModelProperty(value = "归属计划")
    @TableField(value = "`plan_id`")
    @Echo(api = PROJECT_PLAN_ID_CLASS, beanClass = ProjectPlan.class)
    private Long planId;

    /**
     * 归属产品
     */
    @ApiModelProperty(value = "主办系统")
    @TableField(value = "`product_id`")
    @Echo(api = PRODUCT_ID_FEIGN_CLASS, beanClass = ProductInfo.class)
    private Long productId;

    /**
     * 提出人
     */
    @ApiModelProperty(value = "提出人")
    @TableField(value = "`put_by`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long putBy;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "`leading_by`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long leadingBy;

    /**
     * 处理人
     */
    @ApiModelProperty(value = "处理人")
    @TableField(value = "`handle_by`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long handleBy;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "`plan_stime`")
    private LocalDateTime planStime;

    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    @TableField(value = "`plan_etime`")
    private LocalDateTime planEtime;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @TableField(value = "`start_time`")
    private LocalDateTime startTime;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    @TableField(value = "`end_time`")
    private LocalDateTime endTime;

    /**
     * 是否归档
     */
    @ApiModelProperty(value = "是否归档")
    @TableField(value = "`is_filed`")
    private Boolean isFiled;

    /**
     * 需求来源code
     */
    @ApiModelProperty(value = "需求来源code")
    @TableField(value = "`source_code`")
    @Echo(api = SOURCE_CODE_CLASS, beanClass = Source.class)
    private String sourceCode;

    /**
     * 附件信息
     */
    @ApiModelProperty(value = "附件信息")
    @TableField(exist = false)
    private List<File> files;

    @ApiModelProperty(value = "标签Id")
    @TableField(exist = false)
    private List<Long> tagId;

    /**
     * 产品修复版本id
     */
    @ApiModelProperty(value = "产品修复版本id")
    @TableField(value = "`product_repair_version_id`")
    @Echo(api = PRODUCT_VERSION_FEIGN_CLASS, beanClass = ProductVersion.class)
    private Long productRepairVersionId;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    @TableField(value = "`project_manager_by`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long projectManagerBy;

    /**
     * 测试经理
     */
    @ApiModelProperty(value = "测试经理")
    @TableField(value = "`test_manager_by`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long testManagerBy;

    @ApiModelProperty(value = "辅办产品Id")
    @TableField(exist = false)
    private List<Long> assistProductIds;

    @Builder
    public Testreq(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
            Long parentId, String code, String name, String description, String stateCode,
            String priorityCode, String typeCode, Boolean delay, Integer rateProgress, Long productVersionId,
            Double estimatePoint,
            Double estimateHour, Double usedHour, Long programId, Long projectId, Long productId, Long planId,
            Long putBy, Long leadingBy, Boolean isFiled,
            Long handleBy, LocalDateTime planStime, LocalDateTime planEtime, LocalDateTime startTime,
            LocalDateTime endTime, String sourceCode,Long projectManagerBy,Long testManagerBy,
            List<File> files, Long productModuleFunctionId, List<Long> tagId, Long productRepairVersionId)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.parentId = parentId;
        this.code = code;
        this.name = name;
        this.description = description;
        this.stateCode = stateCode;
        this.priorityCode = priorityCode;
        this.typeCode = typeCode;
        this.delay = delay;
        this.rateProgress = rateProgress;
        this.productVersionId = productVersionId;
        this.estimatePoint = estimatePoint;
        this.estimateHour = estimateHour;
        this.usedHour = usedHour;
        this.programId = programId;
        this.projectId = projectId;
        this.productId = productId;
        this.planId = planId;
        this.putBy = putBy;
        this.leadingBy = leadingBy;
        this.handleBy = handleBy;
        this.planStime = planStime;
        this.planEtime = planEtime;
        this.startTime = startTime;
        this.endTime = endTime;
        this.sourceCode = sourceCode;
        this.isFiled = isFiled;
        this.files = files;
        this.productModuleFunctionId = productModuleFunctionId;
        this.tagId = tagId;
        this.productRepairVersionId = productRepairVersionId;
        this.projectManagerBy = projectManagerBy;
        this.testManagerBy = testManagerBy;
    }

}
