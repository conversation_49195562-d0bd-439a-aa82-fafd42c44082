package com.jettech.jettong.alm.project.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.jettong.alm.issue.entity.Priority;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.io.Serializable;

import static com.jettech.jettong.common.constant.EchoConstants.PRIORITY_CODE_CLASS;

/**
 * 项目信息分页实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 项目信息分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.project.dto
 * @className ProjectInfo
 * @date 2021-11-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ProjectInfoPageQuery", description = "项目信息")
public class ProjectInfoPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "优先级code")
    private String priorityCode;
    /**
     * 项目类型Code
     */
    @ApiModelProperty(value = "项目类型Code")
    private String typeCode;

    @ApiModelProperty(value = "项目类型")
    private String classify;

    /**
     * 项目集
     */
    @ApiModelProperty(value = "项目集")
    private Long programId;

    /**
     * 是否归档
     */
    @ApiModelProperty(value = "是否归档")
    private Boolean isFiled;

    /**
     * 是否置顶
     */
    @ApiModelProperty(value = "是否置顶")
    private Boolean top;

    /**
     * 是否收藏
     */
    @ApiModelProperty(value = "是否收藏")
    private Boolean collection;

    /**
     * 产品集id
     */
    @ApiModelProperty(value = "产品集id")
    private Long productsetId;

    /**
     * 产品集id
     */
    @ApiModelProperty(value = "产品id")
    private Long productId;

    /**
     * 父项目ID，用于查询子项目
     */
    @ApiModelProperty(value = "父项目ID，用于查询子项目")
    private Long parentProjectId;

    /**
     * 是否只查询父项目
     */
    @ApiModelProperty(value = "是否只查询父项目")
    private Boolean parentOnly;

    /**
     * 所属部门机构ID
     */
    @ApiModelProperty(value = "所属部门机构ID")
    private Long orgId;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long userId;
}


