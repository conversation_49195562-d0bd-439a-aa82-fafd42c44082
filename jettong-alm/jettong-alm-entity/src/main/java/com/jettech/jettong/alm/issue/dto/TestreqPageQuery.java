package com.jettech.jettong.alm.issue.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong1
 * @package com.jettech.jettong.alm.issue.dto
 * @className aa
 * @date 2025/7/7 18:38
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestreqPageQuery", description = "测试需求信息")
public class TestreqPageQuery extends CustomFormFieldExtendQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "计划完成时间 筛选时间段")
    private PeriodQuery<LocalDateTime> planEtime;
    @ApiModelProperty(value = "计划开始时间 筛选时间段")
    private PeriodQuery<LocalDateTime> planStime;
    @ApiModelProperty(value = "编号")
    private String code;
    @ApiModelProperty(value = "名称/标题")
    private String name;
    @ApiModelProperty(value = "父测试需求ID")
    private List<Long> parentId;
    @ApiModelProperty(value = "状态")
    private List<String> stateCode;
    @ApiModelProperty(value = "优先级")
    private List<String> priorityCode;
    @ApiModelProperty(value = "事项类型code")
    private List<String> typeCode;
    @ApiModelProperty(value = "估算工时")
    private PeriodQuery<Double> estimateHour;
    @ApiModelProperty(value = "是否延期")
    private Boolean delay;
    @ApiModelProperty(value = "是否归档")
    private Boolean isFiled;
    @ApiModelProperty(value = "产品版本id")
    private List<Long> productVersionId;
    @ApiModelProperty(value = "产品模块id")
    private List<Long> productModuleFunctionId;
    @ApiModelProperty(value = "归属项目集")
    private List<Long> programId;
    @ApiModelProperty(value = "归属产品")
    private List<Long> productId;
    @ApiModelProperty(value = "归属项目")
    private List<Long> projectId;
    @ApiModelProperty(value = "归属计划")
    private List<Long> planId;
    @ApiModelProperty(value = "提出人")
    private List<Long> putBy;
    @ApiModelProperty(value = "负责人")
    private List<Long> leadingBy;
    @ApiModelProperty(value = "处理人")
    private List<Long> handleBy;
    @ApiModelProperty(value = "测试需求来源")
    private List<String> sourceCode;
    @ApiModelProperty(value = "标签id")
    private List<Long> tagId;
    @ApiModelProperty(value = "进度0-100")
    private PeriodQuery<Integer> rateProgress;
    @ApiModelProperty(value = "规模")
    private PeriodQuery<Double> estimatePoint;
    @ApiModelProperty(value = "实际工时")
    private PeriodQuery<Double> usedHour;
    @ApiModelProperty(value = "开始时间")
    private PeriodQuery<LocalDateTime> startTime;
    @ApiModelProperty(value = "完成时间")
    private PeriodQuery<LocalDateTime> endTime;
    @ApiModelProperty(value = "产品修复版本id")
    private List<Long> productRepairVersionId;
    @ApiModelProperty(value = "创建时间")
    private PeriodQuery<LocalDateTime> createTime;
    @ApiModelProperty(value = "辅办产品Id")
    private List<Long> assistProductIds;
}
