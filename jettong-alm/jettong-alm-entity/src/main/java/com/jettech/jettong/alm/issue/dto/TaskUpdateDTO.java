package com.jettech.jettong.alm.issue.dto;

import com.jettech.jettong.alm.issue.entity.CustomFormFieldExtend;
import com.jettech.jettong.base.entity.file.File;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务信息修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 任务信息修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.issue.dto
 * @className TaskUpdateDTO
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TaskUpdateDTO", description = "任务信息")
public class TaskUpdateDTO extends CustomFormFieldExtend implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * 父任务ID
     */
    @ApiModelProperty(value = "父任务ID")
    private Long parentId;

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    private String name;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String stateCode;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private String priorityCode;
    /**
     * 归属项目集
     */
    @ApiModelProperty(value = "归属项目集")
    private Long programId;
    /**
     * 归属项目
     */
    @ApiModelProperty(value = "归属项目")
    private Long projectId;

    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    private Long productId;
    /**
     * 关联计划
     */
    @ApiModelProperty(value = "关联计划")
    private Long planId;
    /**
     * 关联测试需求
     */
    @ApiModelProperty(value = "关联软件需求")
    private Long requirementId;
    /**
     * 关联缺陷
     */
    @ApiModelProperty(value = "关联缺陷")
    private Long bugId;
    /**
     * 估算工时
     */
    @ApiModelProperty(value = "估算工时")
    private Double estimateHour;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;
    /**
     * 执行者
     */
    @ApiModelProperty(value = "处理人")
    private Long handleBy;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private LocalDateTime planStime;
    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    private LocalDateTime planEtime;

    /**
     * 附件信息
     */
    @ApiModelProperty(value = "附件信息")
    private List<File> files;

    /**
     * 任务来源
     */
    @ApiModelProperty(value = "任务来源")
    private String sourceCode;

    @ApiModelProperty(value = "标签id")
    private List<String> tagId;

    /**
     * 产品版本id
     */
    @ApiModelProperty(value = "产品版本id")
    private Long productVersionId;
    /**
     * 产品模块id
     */
    @ApiModelProperty(value = "产品模块id")
    private Long productModuleFunctionId;
    /**
     * 产品修复版本id
     */
    @ApiModelProperty(value = "产品修复版本id")
    private Long productRepairVersionId;

    @ApiModelProperty(value = "用例执行失败必须新增缺陷")
    private String failAddBug;

    @ApiModelProperty(value = "交易id")
    private Long functionId;

    @ApiModelProperty(value = "系统id")
    private Long systemId;

    /**
     * 关联测试需求
     */
    @ApiModelProperty(value = "关联测试需求")
    private Long testreqId;

    /**
     * 处理人
     */
    @ApiModelProperty(value = "处理人多选")
    private List<Long> handleByList;
}
