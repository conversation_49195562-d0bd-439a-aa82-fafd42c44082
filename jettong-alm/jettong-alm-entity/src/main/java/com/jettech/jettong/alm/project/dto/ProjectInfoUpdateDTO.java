package com.jettech.jettong.alm.project.dto;

import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.jettong.alm.project.entity.ProjectProductset;
import com.jettech.jettong.base.entity.file.File;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目信息修改实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 项目信息修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.project.dto
 * @className ProjectInfoUpdateDTO
 * @date 2021-11-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ProjectInfoUpdateDTO", description = "项目信息")
public class ProjectInfoUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Size(max = 128, message = "名称长度不能超过128")
    private String name;

    @ApiModelProperty(value = "优先级code")
    private String priorityCode;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Size(max = 200, message = "描述长度不能超过200")
    private String description;

    @ApiModelProperty(value = "项目类型")
    private String classify;
    /**
     * 附件信息
     */
    @ApiModelProperty(value = "附件信息")
    private List<File> files;
    /**
     * 主板产品
     */
    @ApiModelProperty(value = "关联主办产品")
    private Long productId;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    private Long leadingBy;
    /**
     * 项目集
     */
    @ApiModelProperty(value = "项目集")
    private Long programId;
    /**
     * 关联主办产品
     */
//    @ApiModelProperty(value = "关联主办产品")
//    private Long hostProductId;

    /**
     * 关联辅办产品
     */
    @ApiModelProperty(value = "关联辅办产品")
    private List<Long> assistProductIds;

    /**
     * 机构ID
     */
    @ApiModelProperty(value = "机构ID")
    private Long orgId;

    /**
     * 申请部门机构Id
     */
    @ApiModelProperty(value = "申请部门机构Id")
    private Long applyOrgId;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private LocalDateTime planStartTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private LocalDateTime planEndTine;

    /**
     * 项目阶段
     */
    @ApiModelProperty(value = "项目阶段")
    private String projectStage;

    /**
     * 关联项目
     */
    @ApiModelProperty(value = "关联项目")
    private List<Long> projectIds;

    /**
     * 关联产品集
     */
    @ApiModelProperty(value = "关联产品集")
    private List<ProjectProductset> projectProductsets;

    @ApiModelProperty(value = "父项目ID")
    private Long parentId;

    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    @ApiModelProperty(value = "测试方式")
    private List<String> testModes;
    @ApiModelProperty(value = "测试方式")
    private String testMode;

//    @ApiModelProperty(value = "辅办系统")
//    private String officeSystem;

    @ApiModelProperty(value = "实施方案是否可裁剪 1是-项目模板可变动；0否-项目模板不可变动")
    private Boolean isCropping;

    @ApiModelProperty(value = "项目预算")
    private BigDecimal budget;
}
