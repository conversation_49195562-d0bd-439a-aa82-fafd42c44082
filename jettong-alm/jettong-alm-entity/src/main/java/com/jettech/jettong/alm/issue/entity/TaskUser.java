package com.jettech.jettong.alm.issue.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.alm.issue.enumeration.ItemUserType;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_FEIGN_CLASS;


/**
 * 任务和人员关联实体类
 * <AUTHOR>
 * @version 1.0
 * @description 任务和人员关联实体类
 * @projectName jettong
 * @package com.jettech.jettong.workflow.entity
 * @className TaskUser
 * @date 2025-09-15
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("issue_task_user")
@ApiModel(value = "TaskUser", description = "任务和人员关联")
@AllArgsConstructor
public class TaskUser extends SuperEntity<Long> implements EchoVO
{

    public static final String HANDLE_BY_LIST = "handleByList";

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键")
    @TableField(value = "task_id")
    private Long taskId;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @NotNull(message = "请填写主键")
    @TableField(value = "user_id")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long userId;

    /**
     * 类型 #ItemUserType{HANDLE:处理人}
     */
    @ApiModelProperty(value = "类型 #ItemUserType{HANDLE:处理人}")
    @Size(max = 100, message = "类型 #ItemUserType{HANDLE:处理人}长度不能超过100")
    @TableField(value = "type", condition = LIKE)
    private ItemUserType type;


    @Builder
    public TaskUser(Long id, LocalDateTime createTime, Long createdBy, 
                    Long taskId, Long userId, ItemUserType type)
    {
        this.id = id;
        this.createTime = createTime;
        this.createdBy = createdBy;
        this.taskId = taskId;
        this.userId = userId;
        this.type = type;
    }


    public static List<Long> getUserIds(List<TaskUser> taskUsers){
        return taskUsers.stream().map(TaskUser::getUserId).distinct().collect(Collectors.toList());
    }

    public static List<User> getUserInfos(List<TaskUser> taskUsers){
        return taskUsers.stream().map(n->(User)n.getEchoMap().get("userId")).distinct().collect(Collectors.toList());
    }

}
