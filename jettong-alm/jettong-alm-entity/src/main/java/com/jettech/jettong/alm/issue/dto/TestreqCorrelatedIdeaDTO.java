package com.jettech.jettong.alm.issue.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong1
 * @package com.jettech.jettong.alm.issue.dto
 * @className aa
 * @date 2025/7/7 18:38
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestreqCorrelatedIdeaDTO", description = "测试需求信息关联用户需求实体")
public class TestreqCorrelatedIdeaDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "测试需求id")
    @NotNull(message = "请填写测试需求id")
    private Long testreqId;
    @ApiModelProperty(value = "用户需求id")
    private Long ideaId;
    @ApiModelProperty(value = "关联类型,ADD-关联，DELETE-解除关联")
    @NotNull(message = "请填写关联类型")
    @NotEmpty(message = "请填写关联类型")
    private String correlatedType;
}
