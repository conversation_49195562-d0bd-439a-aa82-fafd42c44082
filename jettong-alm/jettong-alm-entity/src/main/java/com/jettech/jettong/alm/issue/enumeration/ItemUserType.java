package com.jettech.jettong.alm.issue.enumeration;


import com.jettech.basic.base.BaseEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 关联人员类型
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.alm.issue.enumeration
 * @className ItemUserType
 * @date 2025/9/15 11:43
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ItemUserType implements BaseEnum
{
    HANDLE("处理人"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;

    /**
     * 根据当前枚举的name匹配
     */
    public static ItemUserType match(String val, ItemUserType def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static ItemUserType get(String val)
    {
        return match(val, null);
    }

    public boolean eq(ItemUserType val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码")
    public String getCode()
    {
        return this.name();
    }
}
