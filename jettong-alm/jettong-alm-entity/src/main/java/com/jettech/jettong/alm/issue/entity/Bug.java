package com.jettech.jettong.alm.issue.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.alm.project.entity.ProjectPlan;
import com.jettech.jettong.alm.project.entity.ProjectProgram;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.constant.DictionaryType;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.product.entity.ProductVersion;
import com.jettech.jettong.testm.entity.TestPlan;
import com.jettech.jettong.testm.entity.TestProductCase;
import com.jettech.jettong.testm.entity.TestTaskCase;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.*;


/**
 * 缺陷实例表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 缺陷实例表实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.bug.entity
 * @className Bug
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("issue_bug")
@ApiModel(value = "Bug", description = "缺陷实例表")
@AllArgsConstructor
public class Bug extends CustomFormFieldExtend implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 父级缺陷ID
     */
    @ApiModelProperty(value = "父级缺陷ID")
    @TableField(value = "`parent_id`")
    @Echo(api = BUG_ID_CLASS, beanClass = Bug.class)
    private Long parentId;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @NotEmpty(message = "请填写编号")
    @Size(max = 20, message = "编号长度不能超过20")
    @TableField(value = "`code`")
    private String code;

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    @TableField(value = "`name`")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "`description`")
    private String description;

    /**
     * 状态code
     */
    @ApiModelProperty(value = "状态code")
    @TableField(value = "`state_code`")
    @Echo(api = STATE_CODE_CLASS, beanClass = State.class)
    private String stateCode;

    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    @TableField(value = "`product_id`")
    @Echo(api = PRODUCT_ID_FEIGN_CLASS, beanClass = ProductInfo.class)
    private Long productId;

    /**
     * 测试环境
     */
    @ApiModelProperty(value = "环境code")
    @TableField(value = "`env_code`")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.ENVIRONMENT)
    private String envCode;

    /**
     * 优先级code
     */
    @ApiModelProperty(value = "优先级code")
    @TableField(value = "`priority_code`")
    @Echo(api = PRIORITY_CODE_CLASS, beanClass = Priority.class)
    private String priorityCode;

    /**
     * 归属项目集
     */
    @ApiModelProperty(value = "归属项目集")
    @TableField(value = "`program_id`")
    @Echo(api = PROJECT_PROGRAM_ID_CLASS, beanClass = ProjectProgram.class)
    private Long programId;

    /**
     * 归属项目
     */
    @ApiModelProperty(value = "归属项目")
    @TableField(value = "`project_id`")
    @Echo(api = PROJECT_INFO_ID_CLASS, beanClass = ProjectInfo.class)
    private Long projectId;

    /**
     * 关联计划
     */
    @ApiModelProperty(value = "关联计划")
    @TableField(value = "`plan_id`")
    @Echo(api = PROJECT_PLAN_ID_CLASS, beanClass = ProjectPlan.class)
    private Long planId;

    /**
     * 关联软件需求
     */
    @ApiModelProperty(value = "关联软件需求")
    @TableField(value = "`requirement_id`")
    @Echo(api = REQUIREMENT_ID_CLASS, beanClass = Requirement.class)
    private Long requirementId;

    /**
     * 关联案例
     */
    @ApiModelProperty(value = "关联案例")
    @TableField(value = "`testcase_id`")
    @Echo(api = TEST_PRODUCT_CASE_FEIGN_CLASS, beanClass = TestProductCase.class)
    private Long testcaseId;

    /**
     * 事项类型code
     */
    @ApiModelProperty(value = "事项类型code")
    @NotEmpty(message = "请填写事项类型code")
    @Size(max = 20, message = "事项类型code长度不能超过20")
    @TableField(value = "`type_code`")
    @Echo(api = TYPE_CODE_CLASS, beanClass = Type.class)
    private String typeCode;

    /**
     * 复现概览code
     */
    @ApiModelProperty(value = "复现概览code")
    @Size(max = 255, message = "复现概览code长度不能超过255")
    @TableField(value = "`probability_code`")
    @Echo(api = BUG_PROBABILITY_CODE_CLASS, beanClass = BugProbability.class)
    private String probabilityCode;

    /**
     * 复现概览code
     */
    @ApiModelProperty(value = "严重程度code")
    @Size(max = 255, message = "严重程度code长度不能超过255")
    @TableField(value = "`severity_code`")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.SEVERITY_CODE)
    private String severityCode;

    /**
     * 是否延期
     */
    @ApiModelProperty(value = "是否延期")
    @NotNull(message = "请填写是否延期")
    @TableField(value = "`delay`")
    private Boolean delay;

    /**
     * 进度0-100
     */
    @ApiModelProperty(value = "进度0-100")
    @TableField(value = "`rate_progress`")
    private Integer rateProgress;

    /**
     * 估算工时
     */
    @ApiModelProperty(value = "估算工时")
    @TableField(value = "`estimate_hour`")
    private Double estimateHour;

    /**
     * 实际工时
     */
    @ApiModelProperty(value = "实际工时")
    @TableField(value = "`used_hour`")
    private Double usedHour;

    /**
     * 提出人
     */
    @ApiModelProperty(value = "提出人")
    @TableField(value = "`put_by`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long putBy;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "`leading_by`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long leadingBy;

    /**
     * 处理人
     */
    @ApiModelProperty(value = "处理人")
    @TableField(value = "`handle_by`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long handleBy;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @TableField(value = "`plan_stime`")
    private LocalDateTime planStime;

    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    @TableField(value = "`plan_etime`")
    private LocalDateTime planEtime;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @TableField(value = "`start_time`")
    private LocalDateTime startTime;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    @TableField(value = "`end_time`")
    private LocalDateTime endTime;

    /**
     * 是否归档
     */
    @ApiModelProperty(value = "是否归档")
    @TableField(value = "`is_filed`")
    private Boolean isFiled;

    /**
     * 测试计划
     */
    @ApiModelProperty(value = "测试执行任务")
    @TableField(value = "`test_task_id`")
    @Echo(api = TEST_TASK_CASE_ID_FEIGN_CLASS, beanClass = TestTaskCase.class)
    private Long testTaskId;

    /**
     * 附件信息
     */
    @ApiModelProperty(value = "附件信息")
    @TableField(exist = false)
    private List<File> files;

    /**
     * 缺陷来源code
     */
    @ApiModelProperty(value = "缺陷来源code")
    @TableField(value = "`source_code`")
    @Echo(api = SOURCE_CODE_CLASS, beanClass = Source.class)
    private String sourceCode;

    @ApiModelProperty(value = "标签Id")
    @TableField(exist = false)
    private List<Long> tagId;

    /**
     * 产品版本id
     */
    @ApiModelProperty(value = "产品版本id")
    @TableField(value = "`product_version_id`")
    @Echo(api = PRODUCT_VERSION_FEIGN_CLASS, beanClass = ProductVersion.class)
    private Long productVersionId;

    /**
     * 产品修复版本id
     */
    @ApiModelProperty(value = "产品修复版本id")
    @TableField(value = "`product_repair_version_id`")
    @Echo(api = PRODUCT_VERSION_FEIGN_CLASS, beanClass = ProductVersion.class)
    private Long productRepairVersionId;

    /**
     * 产品功能模块id
     */
    @ApiModelProperty(value = "产品功能模块id")
    @TableField(value = "`product_module_function_id`")
    @Echo(api = PRODUCT_MODULE_FUNCTION_FEIGN_CLASS, beanClass = ProductModuleFunction.class)
    private Long productModuleFunctionId;

    @ApiModelProperty(value = "系统id")
    @TableField(value = "`system_id`")
    @Echo(api = PRODUCT_ID_FEIGN_CLASS, beanClass = ProductInfo.class)
    private Long systemId;

    @ApiModelProperty(value = "缺陷归属系统")
    @TableField(value = "`bug_to_system`")
    @Echo(api = PRODUCT_ID_FEIGN_CLASS, beanClass = ProductInfo.class)
    private Long bugToSystem;

    @ApiModelProperty(value = "缺陷分类")
    @TableField(value = "`bug_class`")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.BUG_CLASS)
    private String bugClass;

    @ApiModelProperty(value = "测试计划")
    @TableField(value = "`test_plan`")
    @Echo(api = PLAN_ID_CLASS, beanClass = TestPlan.class)
    private Long testPlan;

    @ApiModelProperty(value = "归属计划(里程碑计划)")
    @TableField(value = "`milestione_plan`")
    @Echo(api = PROJECT_PLAN_ID_CLASS, beanClass = ProjectPlan.class)
    private Long milestionePlan;

    @ApiModelProperty(value = "归属开发人员")
    @TableField(value = "`developer_by`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long developerBy;

    @ApiModelProperty(value = "重新打开次数")
    @TableField(value = "`numbers_open`")
    private String numbersOpen;

    @ApiModelProperty(value = "闭环时间")
    @TableField(value = "`closed_loop_time`")
    private LocalDateTime closedLoopTime;

//    @ApiModelProperty(value = "所属模块")
//    @TableField(value = "`module_id`")
//    @Echo(api = PRODUCT_MODULE_FUNCTION_FEIGN_CLASS, beanClass = ProductModuleFunction.class)
//    private Long moduleId;

    @ApiModelProperty(value = "所属交易")
    @TableField(value = "`function_id`")
    @Echo(api = PRODUCT_MODULE_FUNCTION_FEIGN_CLASS, beanClass = ProductModuleFunction.class)
    private Long functionId;

    /**
     * 关联测试需求
     */
    @ApiModelProperty(value = "关联测试需求")
    @TableField(value = "`testreq_id`")
    @Echo(api = TESTREQ_ID_CLASS, beanClass = Testreq.class)
    private Long testreqId;


    @Builder
    public Bug(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
            String code, String name, String description, String stateCode, Long productId,
            String envCode, String priorityCode, Long programId, Long projectId, Long planId, Long requirementId,
            Long testcaseId,
            String typeCode, Boolean delay, Integer rateProgress, Double estimateHour, Double usedHour,
            Long leadingBy, Long productRepairVersionId,
            Long handleBy, LocalDateTime planStime, LocalDateTime planEtime, LocalDateTime startTime,
            LocalDateTime endTime, Long testTaskId, List<File> files,
            String sourceCode, List<Long> tagId, Long parentId, Long productVersionId, Long productModuleFunctionId,
            Long systemId,Long bugToSystem,String bugClass,Long testPlan,Long milestionePlan,Long developerBy,String numbersOpen,LocalDateTime closedLoopTime,Long functionId,
               Long testreqId)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.code = code;
        this.name = name;
        this.description = description;
        this.stateCode = stateCode;
        this.productId = productId;
        this.envCode = envCode;
        this.priorityCode = priorityCode;
        this.programId = programId;
        this.projectId = projectId;
        this.planId = planId;
        this.requirementId = requirementId;
        this.testcaseId = testcaseId;
        this.typeCode = typeCode;
        this.delay = delay;
        this.rateProgress = rateProgress;
        this.estimateHour = estimateHour;
        this.usedHour = usedHour;
        this.leadingBy = leadingBy;
        this.handleBy = handleBy;
        this.planStime = planStime;
        this.planEtime = planEtime;
        this.startTime = startTime;
        this.endTime = endTime;
        this.testTaskId = testTaskId;
        this.files = files;
        this.sourceCode = sourceCode;
        this.tagId = tagId;
        this.parentId = parentId;
        this.productVersionId = productVersionId;
        this.productModuleFunctionId = productModuleFunctionId;
        this.productRepairVersionId = productRepairVersionId;
        this.systemId = systemId;
        this.bugToSystem = bugToSystem;
        this.bugClass = bugClass;
        this.testPlan = testPlan;
        this.milestionePlan = milestionePlan;
        this.developerBy = developerBy;
        this.numbersOpen = numbersOpen;
        this.closedLoopTime = closedLoopTime;
        this.functionId = functionId;
        this.testreqId = testreqId;
    }

}
