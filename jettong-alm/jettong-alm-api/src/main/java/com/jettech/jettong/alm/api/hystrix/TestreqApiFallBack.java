package com.jettech.jettong.alm.api.hystrix;


import com.jettech.jettong.alm.api.TestreqApi;
import com.jettech.jettong.alm.issue.entity.Testreq;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.Collections;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong1
 * @package com.jettech.jettong.alm.api.hystrix
 * @className TestreqApiFallBack
 * @date 2025/7/14 16:33
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
public class TestreqApiFallBack implements TestreqApi
{
    @Override
    public Testreq getTestreq(Long id)
    {
        return null;
    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return Collections.emptyMap();
    }
}
