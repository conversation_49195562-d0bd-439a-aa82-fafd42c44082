package com.jettech.jettong.alm.api;


import com.jettech.basic.model.LoadService;
import com.jettech.jettong.alm.api.hystrix.TestreqApiFallBack;
import com.jettech.jettong.alm.issue.entity.Testreq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong1
 * @package com.jettech.jettong.alm.api
 * @className TestreqApi
 * @date 2025/7/14 16:32
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@FeignClient(name = "${jettong.feign.alm-server:jettong-alm-server}", fallback = TestreqApiFallBack.class
        , path = "/alm/testreq")
public interface TestreqApi extends LoadService
{
    @Override
    @GetMapping("/echo/findByIds")
    Map<Serializable, Object> findByIds(@RequestParam(value = "ids") Set<Serializable> ids);

    @GetMapping("/echo/getTestreq")
    Testreq getTestreq(@RequestParam(value = "id") Long id);
}
