package com.jettech.jettong.alm.project.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.exception.BizException;
import com.jettech.jettong.alm.project.dao.*;
import com.jettech.jettong.alm.project.entity.*;
import com.jettech.jettong.alm.project.service.ProjectMenuService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目菜单资源信息
 * 注意：根据查询项目下菜单列表时，由于项目有多种类型{@link ProjectType}
 * 需要增加项目类型的筛选条件。使用方法{@link ProjectMenuServiceImpl#}
 *
 * <AUTHOR>
 * @version 1.0
 * @description 项目菜单资源信息
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.project.service.impl
 * @className ProjectMenuServiceImpl
 * @date 2022/5/11 10:44
 * @copyright 2022 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class ProjectMenuServiceImpl extends SuperServiceImpl<ProjectMenuMapper, ProjectMenu>
        implements ProjectMenuService
{

    private final ProjectInfoMapper projectInfoMapper;
    private final ProjectTypeMenuConfMapper projectTypeMenuConfMapper;
    private final ProjectRoleMapper projectRoleMapper;
    private final ProjectTypeMapper projectTypeMapper;
    private final ProjectMenuDicMapper projectMenuDicMapper;
    /**
     * 项目经理默认角色code
     */
    public static final String PM_CODE = "_PM";
    private final ProjectUserRoleMapper projectUserRoleMapper;
    private final ProjectRoleAuthorityMapper projectRoleAuthorityMapper;

    @Override
    public Boolean existMenuCode(Long projectId, String menuCode)
    {
        // 获取项目类型
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        if (null == projectInfo || StrUtil.isEmpty(projectInfo.getTypeCode()))
        {
            throw BizException.validFail("获取项目信息失败");
        }
        ProjectMenuDic menu = projectMenuDicMapper.selectOne(
                Wraps.<ProjectMenuDic>lbQ().eq(ProjectMenuDic::getCode, menuCode).last(" limit 1"));
        if (null == menu)
        {
            return false;
        }
        ProjectType projectType = projectTypeMapper.selectOne(
                Wraps.<ProjectType>lbQ().eq(ProjectType::getCode, projectInfo.getTypeCode()));
        if(projectType==null){
            return false;
        }
        Long count = projectTypeMenuConfMapper.selectCount(
                Wraps.<ProjectTypeMenuConf>lbQ().eq(ProjectTypeMenuConf::getProjectTypeId, projectType.getId())
                        .eq(ProjectTypeMenuConf::getMenuId, menu.getId()));
        return count > 0;
    }

    @Override
    public List<ProjectMenu> getMenuByRoleId(Long roleId)
    {
        return baseMapper.getMenuByRoleId(roleId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateRoleMenu(Long roleId, List<Long> menuIds)
    {
        // 判断角色是否存在
        ProjectRole projectRole = projectRoleMapper.selectById(roleId);
        if (null == projectRole)
        {
            throw BizException.validFail("要修改的角色不存在");
        }

            // 删除角色原来的权限信息
            projectRoleAuthorityMapper.delete(Wraps.<ProjectRoleAuthority>lbQ()
                    .eq(ProjectRoleAuthority::getRoleId, roleId));
            // 添加角色权限信息
            if (!menuIds.isEmpty())
            {
                List<ProjectRoleAuthority> list = menuIds.stream()
                        .map(mId -> ProjectRoleAuthority.builder().roleId(roleId).menuId(mId).build())
                        .collect(Collectors.toList());

                projectRoleAuthorityMapper.insertBatchSomeColumn(list);
            }

    }

    @Override
    public List<ProjectMenu> getAllMenu(Long projectId)
    {
        // 获取项目类型
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        if (null == projectInfo || StrUtil.isEmpty(projectInfo.getTypeCode()))
        {
            throw BizException.validFail("获取项目信息失败");
        }
        List<ProjectType> projectTypes = projectTypeMapper.selectList(
                Wraps.<ProjectType>lbQ().eq(ProjectType::getCode, projectInfo.getTypeCode()));
        if(projectTypes.isEmpty()){
            throw BizException.validFail("获取项目模板信息失败");
        }
        List<ProjectTypeMenuConf> projectTypeMenuConfs = projectTypeMenuConfMapper.selectList(
                Wraps.<ProjectTypeMenuConf>lbQ()
                        .eq(ProjectTypeMenuConf::getProjectTypeId, projectTypes.get(0).getId()));
        List<Long> collect =
                projectTypeMenuConfs.stream().map(ProjectTypeMenuConf::getMenuId).collect(Collectors.toList());
        // 1. 一次性查询所有相关菜单（根据业务范围添加WHERE条件）
        List<ProjectMenuDic> allMenus = projectMenuDicMapper.selectList(
                Wrappers.<ProjectMenuDic>lambdaQuery()
        );

        // 2. 构建 parentId → 子菜单列表 的映射
        Map<Long, List<ProjectMenuDic>> menuMap = allMenus.stream( ).filter(projectMenudis ->
                        projectMenudis.getParentId() != null
                )
                .collect(Collectors.groupingBy(ProjectMenuDic::getParentId));

        // 3. 递归收集所有子孙菜单ID（内存操作，无DB查询）
        Set<Long> allMenuIds = new HashSet<>();
        // 遍历一级菜单ID
        for (Long topMenuId : collect) {
            collectChildIds(topMenuId, menuMap, allMenuIds);
        }
        //最终结果
        List<Long> result = new ArrayList<>(allMenuIds);
        result.addAll(collect);
        return baseMapper.selectList(
                Wraps.<ProjectMenu>lbQ().in(ProjectMenu::getId, result).orderByAsc(ProjectMenu::getSort));
    }

    @Override
    public List<ProjectMenu> getAllMenuByType(String type)
    {
        List<ProjectType> projectTypes = projectTypeMapper.selectList(
                Wraps.<ProjectType>lbQ().eq(ProjectType::getCode, type));
        if(projectTypes.isEmpty()){
            throw BizException.validFail("获取项目模板信息失败");
        }
        List<ProjectTypeMenuConf> projectTypeMenuConfs = projectTypeMenuConfMapper.selectList(
                Wraps.<ProjectTypeMenuConf>lbQ()
                        .eq(ProjectTypeMenuConf::getProjectTypeId, projectTypes.get(0).getId()));
        List<Long> collect =
                projectTypeMenuConfs.stream().map(ProjectTypeMenuConf::getMenuId).collect(Collectors.toList());
                    // 1. 一次性查询所有相关菜单（根据业务范围添加WHERE条件）
            List<ProjectMenuDic> allMenus = projectMenuDicMapper.selectList(
                    Wrappers.<ProjectMenuDic>lambdaQuery()
            );

            // 2. 构建 parentId → 子菜单列表 的映射
            Map<Long, List<ProjectMenuDic>> menuMap = allMenus.stream( ).filter(projectMenudis ->
                            projectMenudis.getParentId() != null
                    )
                    .collect(Collectors.groupingBy(ProjectMenuDic::getParentId));

            // 3. 递归收集所有子孙菜单ID（内存操作，无DB查询）
            Set<Long> allMenuIds = new HashSet<>();
            // 遍历一级菜单ID
            for (Long topMenuId : collect) {
                collectChildIds(topMenuId, menuMap, allMenuIds);
            }
         //最终结果
            List<Long> result = new ArrayList<>(allMenuIds);
            result.addAll(collect);
        return baseMapper.selectList(
                Wraps.<ProjectMenu>lbQ().in(ProjectMenu::getId, result).orderByAsc(ProjectMenu::getSort));
    }

    @Override
    public List<ProjectMenu> getMenuByUserIdAndProjectId(Long userId, Long projectId, String projectType)
    {

        List<ProjectMenu> projectMenus = baseMapper.getMenuByUserIdAndProjectId(userId, projectId, projectType);
        if (CollUtil.isEmpty(projectMenus)){
            //是否有项目权限
            ProjectInfo projectInfo = projectInfoMapper.checkHaveProject(projectId);
            if (projectInfo != null){
                String pmRoleCode = projectType + PM_CODE;
                // 查询项目经理角色id
                ProjectRole projectRole = projectRoleMapper.selectOne(
                        Wraps.<ProjectRole>lbQ().eq(ProjectRole::getCode, pmRoleCode).eq(ProjectRole::getReadonly, true)
                                .eq(ProjectRole::getType, true).eq(ProjectRole::getTypeCode, projectInfo.getTypeCode())
                                .last(" limit 1"));
                if (projectRole != null){
                    return baseMapper.getMenuByRoleId(projectRole.getId());
                }
            }
        }
        return projectMenus;

    }
    private void collectChildIds(Long parentId, Map<Long, List<ProjectMenuDic>> menuMap, Set<Long> idCollector) {
        if (!menuMap.containsKey(parentId))
        {
            // 终止条件：无子节点
            return;
        }

        for (ProjectMenuDic child : menuMap.get(parentId)) {
            // 添加当前子菜单ID
            idCollector.add(child.getId());
            // 递归子孙
            collectChildIds(child.getId(), menuMap, idCollector);
        }
    }
}
