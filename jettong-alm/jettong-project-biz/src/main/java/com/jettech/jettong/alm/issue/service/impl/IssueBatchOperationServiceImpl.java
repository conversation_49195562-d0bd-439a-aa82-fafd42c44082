package com.jettech.jettong.alm.issue.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.exception.BizException;
import com.jettech.jettong.alm.issue.dao.*;
import com.jettech.jettong.alm.issue.entity.*;
import com.jettech.jettong.alm.issue.enumeration.ItemUserType;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.issue.service.IssueBatchOperationService;
import com.jettech.jettong.alm.issue.vo.IssueBatchOperationVO;
import com.jettech.jettong.alm.project.entity.ProjectUserRole;
import com.jettech.jettong.alm.project.service.ProjectUserRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 事项批量操作业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 事项批量操作业务层
 * @projectName jettong
 * @package com.jettech.jettong.alm.service.impl
 * @className IdeaServiceImpl
 * @date 2022-01-10
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class IssueBatchOperationServiceImpl implements IssueBatchOperationService
{
    private final BugMapper bugMapper;
    private final IdeaMapper ideaMapper;
    private final TaskMapper taskMapper;
    private final RiskMapper riskMapper;
    private final RequirementMapper requirementMapper;
    private final TestreqMapper testreqMapper;
    private final ProjectUserRoleService projectUserRoleService;
    private final TaskUserMapper taskUserMapper;

    @Override
    public Boolean updateByIds(TypeClassify typeClassify,IssueBatchOperationVO model)
    {
        if(model.getIds()== null || model.getIds().size()<1){
            throw BizException.validFail("当前没有需要批量修改的事项");
        }
        switch (typeClassify)
        {
            case IDEA:
                updateIdeaByIds(model);
                break;
            case ISSUE:
                updateRequirementByIds(model);
                break;
            case TESTREQ:
                updateTestreqByIds(model);
                break;
            case TASK:
                updateTaskByIds(model);
                break;
            case BUG:
                updateBugByIds(model);
                break;
            case RISK:
                updateRiskByIds(model);
                break;
            default:
                throw BizException.validFail("事项类型错误");
        }
        return true;
    }

    private  void updateIdeaByIds(IssueBatchOperationVO model){
        LambdaUpdateWrapper<Idea> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(Idea::getId, model.getIds())
                .set(model.getHandleBy()!=null,Idea::getHandleBy, model.getHandleBy())
                .set(model.getLeadingBy()!=null,Idea::getLeadingBy, model.getLeadingBy())
                .set(model.getPutBy()!=null,Idea::getPutBy, model.getPutBy())
                .set(model.getPlanEtime()!=null,Idea::getExpectedTime, model.getPlanEtime())
                .set(StringUtils.isNotEmpty(model.getPriorityCode()),Idea::getPriorityCode, model.getPriorityCode())
                .set(Idea::getUpdateTime, LocalDateTime.now())
                .set(Idea::getUpdatedBy, ContextUtil.getUserId());
        ideaMapper.update(null, lambdaUpdateWrapper);
    }

    private  void updateBugByIds(IssueBatchOperationVO model)
    {
        for (Long id : model.getIds())
        {
            Bug bug = bugMapper.selectById(id);
            if (null == bug)
            {
                continue;
            }
            // 如果当前登录人不是处理人/负责人/提出人或角色为该项目项目经理则不能删除
            Long leadingBy = bug.getLeadingBy();
            Long putBy = bug.getPutBy();
            Long handleBy = bug.getHandleBy();
            Long createdBy = bug.getCreatedBy();
            // 查询该项目的项目经理
            List<Long> userIds =
                    projectUserRoleService.listObjs(Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getUserId)
                            .eq(ProjectUserRole::getProjectId, bug.getProjectId())
                            .exists("select 1 from project_role where `code` = 'PM' and id = role_id"), Convert::toLong);

            if (!isUserEx(putBy, leadingBy, handleBy, createdBy, userIds))
            {
                throw BizException.validFail("批量修改失败，缺陷【{}】当前登录人不是处理人/负责人/提出人/创建人/项目经理;", bug.getName());
            }
        }

        LambdaUpdateWrapper<Bug> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(Bug::getId, model.getIds())
                .set(model.getHandleBy()!=null,Bug::getHandleBy, model.getHandleBy())
                .set(model.getLeadingBy()!=null,Bug::getLeadingBy, model.getLeadingBy())
                .set(model.getPutBy()!=null,Bug::getPutBy, model.getPutBy())
                .set(model.getPlanEtime()!=null,Bug::getPlanEtime, model.getPlanEtime())
                .set(model.getPlanStime()!=null,Bug::getPlanStime, model.getPlanStime())
                .set(StringUtils.isNotEmpty(model.getPriorityCode()),Bug::getPriorityCode, model.getPriorityCode())
                .set(Bug::getUpdateTime, LocalDateTime.now())
                .set(Bug::getUpdatedBy, ContextUtil.getUserId());
        bugMapper.update(null, lambdaUpdateWrapper);
    }

    /**
     * 判断当前登录用户是否是处理人/负责人/提出人
     * @param putBy 提出人
     * @param createdBy 创建人
     * @param handleBy 处理人
     * @param leadingBy 负责人
     * @param userIds 当前项目项目经理用户id
     * @return {@link boolean} 是否是当前登录用户
     * <AUTHOR>
     * @date 2023/7/10 10:08
     * @update 2023/7/10 10:08
     * @since 1.0
     */
    private boolean isUserEx(Long putBy, Long handleBy, Long leadingBy, Long createdBy, List<Long> userIds)
    {
        Long userId = ContextUtil.getUserId();
        if (null != putBy && putBy.equals(userId))
        {
            return true;
        }

        if (null != createdBy && createdBy.equals(userId))
        {
            return true;
        }

        if (null != handleBy && handleBy.equals(userId))
        {
            return true;
        }

        if (null != leadingBy && leadingBy.equals(userId))
        {
            return true;
        }

        return !userIds.isEmpty() && userIds.contains(userId);
    }


    private  void updateRequirementByIds(IssueBatchOperationVO model)
    {
        LambdaUpdateWrapper<Requirement> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(Requirement::getId, model.getIds())
                .set(model.getHandleBy()!=null,Requirement::getHandleBy, model.getHandleBy())
                .set(model.getLeadingBy()!=null,Requirement::getLeadingBy, model.getLeadingBy())
                .set(model.getPutBy()!=null,Requirement::getPutBy, model.getPutBy())
                .set(model.getPlanEtime()!=null,Requirement::getPlanEtime, model.getPlanEtime())
                .set(model.getPlanStime()!=null,Requirement::getPlanStime, model.getPlanStime())
                .set(StringUtils.isNotEmpty(model.getPriorityCode()),Requirement::getPriorityCode, model.getPriorityCode())
                .set(Requirement::getUpdateTime, LocalDateTime.now())
                .set(Requirement::getUpdatedBy, ContextUtil.getUserId());
        requirementMapper.update(null, lambdaUpdateWrapper);
    }
    //测试需求
    private  void updateTestreqByIds(IssueBatchOperationVO model)
    {
        LambdaUpdateWrapper<Testreq> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(Testreq::getId, model.getIds())
                .set(model.getHandleBy()!=null,Testreq::getHandleBy, model.getHandleBy())
                .set(model.getLeadingBy()!=null,Testreq::getLeadingBy, model.getLeadingBy())
                .set(model.getPutBy()!=null,Testreq::getPutBy, model.getPutBy())
                .set(model.getPlanEtime()!=null,Testreq::getPlanEtime, model.getPlanEtime())
                .set(model.getPlanStime()!=null,Testreq::getPlanStime, model.getPlanStime())
                .set(StringUtils.isNotEmpty(model.getPriorityCode()),Testreq::getPriorityCode, model.getPriorityCode())
                .set(Testreq::getUpdateTime, LocalDateTime.now())
                .set(Testreq::getUpdatedBy, ContextUtil.getUserId());
        testreqMapper.update(null, lambdaUpdateWrapper);
    }

    private  void updateTaskByIds(IssueBatchOperationVO model)
    {
        if (CollUtil.isNotEmpty(model.getHandleByList())){
            model.setHandleBy(model.getHandleByList().get(0));
        }
        LambdaUpdateWrapper<Task> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(Task::getId, model.getIds())
                .set(model.getHandleBy()!=null,Task::getHandleBy, model.getHandleBy())
                .set(model.getLeadingBy()!=null,Task::getLeadingBy, model.getLeadingBy())
                .set(model.getPlanEtime()!=null,Task::getPlanEtime, model.getPlanEtime())
                .set(model.getPlanStime()!=null,Task::getPlanStime, model.getPlanStime())
                .set(StringUtils.isNotEmpty(model.getPriorityCode()),Task::getPriorityCode, model.getPriorityCode())
                .set(Task::getUpdateTime, LocalDateTime.now())
                .set(Task::getUpdatedBy, ContextUtil.getUserId());
        taskMapper.update(null, lambdaUpdateWrapper);
        if (CollUtil.isNotEmpty(model.getHandleByList())){
            List<TaskUser> taskUsers = new ArrayList<>();
            model.getIds().forEach(taskId -> {
                model.getHandleByList().forEach(
                     userId->  taskUsers.add(TaskUser.builder().taskId(taskId).userId(userId).type(ItemUserType.HANDLE).build())
                );
            });
            taskUserMapper.insertBatchSomeColumn(taskUsers);
        }

    }

    private  void updateRiskByIds(IssueBatchOperationVO model)
    {
        LambdaUpdateWrapper<Risk> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(Risk::getId, model.getIds())
                .set(model.getHandleBy()!=null,Risk::getHandleBy, model.getHandleBy())
                .set(model.getLeadingBy()!=null,Risk::getLeadingBy, model.getLeadingBy())
                .set(model.getPutBy()!=null,Risk::getPutBy, model.getPutBy())
                .set(model.getPlanEtime()!=null,Risk::getEndTime, model.getPlanEtime())
                .set(model.getPlanStime()!=null,Risk::getStartTime, model.getPlanStime())
                .set(StringUtils.isNotEmpty(model.getPriorityCode()),Risk::getPriorityCode, model.getPriorityCode())
                .set(Risk::getUpdateTime, LocalDateTime.now())
                .set(Risk::getUpdatedBy, ContextUtil.getUserId());
        riskMapper.update(null, lambdaUpdateWrapper);
    }
}
