package com.jettech.jettong.alm.issue.dao;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.alm.issue.entity.TaskUser;
import org.springframework.stereotype.Repository;

/**
 * 任务和人员关联Mapper 接口
 * <AUTHOR>
 * @version 1.0
 * @description 任务和人员关联Mapper 接口
 * @projectName jettong
 * @package com.jettech.jettong.workflow.dao
 * @className TaskUserMapper
 * @date 2025-09-15
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface TaskUserMapper extends SuperMapper<TaskUser>
{

}
