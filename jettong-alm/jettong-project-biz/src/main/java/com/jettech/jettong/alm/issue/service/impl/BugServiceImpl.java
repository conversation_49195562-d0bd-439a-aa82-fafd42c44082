package com.jettech.jettong.alm.issue.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.repository.CacheOps;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.CollHelper;
import com.jettech.basic.utils.DateUtils;
import com.jettech.basic.utils.StringUtil;
import com.jettech.jettong.alm.api.TaskApi;
import com.jettech.jettong.alm.issue.dao.BugMapper;
import com.jettech.jettong.alm.issue.dao.StateMapper;
import com.jettech.jettong.alm.issue.dto.PeriodQuery;
import com.jettech.jettong.alm.issue.entity.Bug;
import com.jettech.jettong.alm.issue.entity.State;
import com.jettech.jettong.alm.issue.entity.Task;
import com.jettech.jettong.alm.issue.entity.Type;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.issue.service.BugService;
import com.jettech.jettong.alm.issue.service.TestTaskCaseBugService;
import com.jettech.jettong.alm.issue.service.TypeService;
import com.jettech.jettong.alm.issue.service.event.IssueUpdateEvent;
import com.jettech.jettong.alm.issue.vo.*;
import com.jettech.jettong.alm.project.dao.ProjectInfoMapper;
import com.jettech.jettong.alm.project.dao.ProjectUserRoleMapper;
import com.jettech.jettong.alm.project.dao.ProjectWorkflowAuthorityMapper;
import com.jettech.jettong.alm.project.dao.ProjectWorkflowTransitionHistoryMapper;
import com.jettech.jettong.alm.project.entity.*;
import com.jettech.jettong.alm.project.service.ProjectRoleService;
import com.jettech.jettong.alm.project.service.ProjectWorkflowService;
import com.jettech.jettong.alm.project.service.ProjectWorkflowTransitionCheckService;
import com.jettech.jettong.alm.workflow.dao.WorkflowAuthorityMapper;
import com.jettech.jettong.alm.workflow.dao.WorkflowNodeMapper;
import com.jettech.jettong.alm.workflow.dao.WorkflowTransitionMapper;
import com.jettech.jettong.alm.workflow.entity.WorkflowAuthority;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;
import com.jettech.jettong.alm.workflow.entity.WorkflowTransition;
import com.jettech.jettong.alm.workflow.enumeration.WorkflowAuthorityType;
import com.jettech.jettong.alm.workflow.enumeration.WorkflowNodeType;
import com.jettech.jettong.base.api.DictionaryApi;
import com.jettech.jettong.base.api.FileApi;
import com.jettech.jettong.base.api.MsgApi;
import com.jettech.jettong.base.dto.msg.MsgDTO;
import com.jettech.jettong.base.dto.msg.MsgSaveDTO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.common.cache.alm.TypeCodePrefixCacheKeyBuilder;
import com.jettech.jettong.common.constant.MsgBizType;
import com.jettech.jettong.common.enumeration.DateGranularityEnum;
import com.jettech.jettong.common.enumeration.DateTypeEnum;
import com.jettech.jettong.testm.api.TestPlanApi;
import com.jettech.jettong.testm.entity.TestOverview;
import com.jettech.jettong.testm.vo.TestSummaryComponent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static java.time.format.DateTimeFormatter.ISO_LOCAL_DATE;

/**
 * 缺陷实例表业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 缺陷实例表业务层
 * @projectName jettong
 * @package com.jettech.jettong.alm.issue.service.impl
 * @className BugServiceImpl
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class BugServiceImpl extends SuperServiceImpl<BugMapper, Bug> implements BugService
{
    private final CacheOps cacheOps;
    private final TypeService typeService;
    private final ProjectWorkflowService projectWorkflowService;
    private final ProjectWorkflowAuthorityMapper projectWorkflowAuthorityMapper;
    private final WorkflowAuthorityMapper workflowAuthorityMapper;
    private final WorkflowTransitionMapper workflowTransitionMapper;
    private final WorkflowNodeMapper workflowNodeMapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final ProjectUserRoleMapper projectUserRoleMapper;
    private final ProjectWorkflowTransitionHistoryMapper projectWorkflowTransitionHistoryMapper;
    private final StateMapper stateMapper;
    private final FileApi fileApi;
    private final TestPlanApi testPlanApi;
    private final TaskApi taskApi;
    private final BugMapper bugMapper;
    private final DictionaryApi dictionaryApi;

    private final ProjectWorkflowTransitionCheckService projectWorkflowTransitionCheckService;
    private final ProjectRoleService projectRoleService;
    private final MsgApi msgApi;

    @Override
    public IPage<IssueGroupVO> groupSelect(IPage<IssueGroupVO> page, String groupBy, LbqWrapper<Bug> wrapper) {
        return baseMapper.groupSelect(page, groupBy, wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(Bug bug)
    {
        if (bug.getProjectId() == 0){
            saveOverView(bug);
        }
        Type type = typeService.findByCode(bug.getTypeCode());

        if (type == null)
        {
            throw BizException.validFail("获取事项类型失败,请联系管理员");
        }

        // 自动生成一个code
        bug.setCode(typeService.getCode(type));

        // 查询bug的初始化状态
        WorkflowNode workflowNode =
                projectWorkflowService.findProjectFirstNode(bug.getProjectId(), TypeClassify.BUG, bug.getTypeCode());
        if (null == workflowNode)
        {
            throw BizException.validFail("获取该缺陷类型工作流失败，请联系管理员");
        }
        bug.setStateCode(workflowNode.getStateCode());
        bug.setRateProgress(workflowNode.getProgress());
        //如果想目Id为0以为此缺陷为组织级测试生成的缺陷，不用去查询项目的信息
        if (bug.getProjectId() != 0L) {
            // 查询项目信息
            ProjectInfo projectInfo = projectInfoMapper.selectById(bug.getProjectId());
            if (projectInfo == null) {
                throw BizException.validFail("创建失败,原因:获取项目信息失败");
            }
            bug.setProgramId(projectInfo.getProgramId());
        }
        boolean add = super.save(bug);

        //添加缺陷和用例关联信息
//        if (bug.getTestcaseId() != null) {
//            Long testcaseId = bug.getTestcaseId();
//            TestTaskCaseBug build = TestTaskCaseBug.builder()
//                    .bugId(bug.getId())
//                    .caseId(testcaseId)
//                    .taskId(bug.getTestTaskId())
//                    .build();
//            testTaskCaseBugService.save(build);
//        }

        if (!add) {
            return add;
        }

        // 修改附件
        List<File> files = bug.getFiles();
        if (files != null && !files.isEmpty())
        {
            files.forEach(item -> item.setBizType(FileBizType.BUG_FILE_UPLOAD).setBizId(bug.getId()));

            fileApi.updateBatchById(files);
        }

        // 推送 工作项改变事件
        IssueUpdateEvent.sendEvent(TypeClassify.BUG, bug.getId(), null, bug);
        // 场景1：创建缺陷时发送通知
        // 构建跳转字符串
        String createJumpString = DetectionMessageContent.buildJumpString(
                bug.getCode(), bug.getName(), bug.getId(), TypeClassify.BUG, bug.getProjectId(),bug.getTypeCode());
        String creatorName = ContextUtil.getUserName();
        String creatorAccount = ContextUtil.getUserAccount();

        // 给负责人发送通知
        sendBugNotification(bug.getLeadingBy(), bug.getId(), "缺陷创建通知",
                String.format("%s(%s)创建了一个您负责的缺陷【%s】", creatorName, creatorAccount, createJumpString));

        // 给处理人发送通知
        sendBugNotification(bug.getHandleBy(), bug.getId(), "缺陷创建通知",
                String.format("%s(%s)创建了一个您处理的缺陷【%s】", creatorName, creatorAccount, createJumpString));
        return add;
    }

    /**
     * 获取缺陷的Code，从redis中获取
     *
     * @param type 工作项类型
     * @return String code
     * <AUTHOR>
     * @date 2021/11/12 9:42
     * @update zxy 2021/11/12 9:42
     * @since 1.0
     */
    private synchronized String getCode(Type type)
    {
        String prefix = type.getPrefix();
        CacheKey cacheKey = new TypeCodePrefixCacheKeyBuilder().key(prefix);
        Long codeNum = cacheOps.get(cacheKey, key -> {
            // 获取前缀相同的类型，防止相同前缀但不同类型的工作项 导致获取到值不是最大的bug
            Set<String> typeCodeSet = typeService.list(Wraps.<Type>lbQ().eq(Type::getPrefix, prefix))
                    .stream().map(Type::getCode).collect(Collectors.toSet());
            // 根据时间排序，获取最新一条数据的code
            String code = super.getObj(
                    Wraps.<Bug>lbQ().select(Bug::getCode)
                            .in(Bug::getTypeCode, typeCodeSet)
                            .likeRight(Bug::getCode, prefix)
                            .orderByDesc(Bug::getCreateTime)
                            .last(" limit 1 "),
                    Convert::toStr);
            return StringUtil.getLongToStr(StringUtil.removePrefix(code, prefix));
        });
        codeNum++;
        cacheOps.set(cacheKey, codeNum);
        return prefix + codeNum;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<? extends Serializable> ids)
    {
        // 删除附件信息
        fileApi.removeByFileBizTypeAndBizIds(FileBizType.BUG_FILE_UPLOAD, ids);

        baseMapper.selectList(Wraps.<Bug>lbQ().in(Bug::getId, ids))
                .forEach(bug -> {
                    deleteOverView(bug);
                });
        //删除状态流转历史
        projectWorkflowTransitionHistoryMapper.delete(Wraps.<ProjectWorkflowTransitionHistory>lbQ()
                .in(ProjectWorkflowTransitionHistory::getBizId, ids));

        boolean remove = super.removeByIds(ids);

        // 发送工作项改变事件
        ids.forEach(id -> IssueUpdateEvent.sendEvent(TypeClassify.BUG, (Long) id, null, null));

        return remove;
    }

    @Override
    public boolean saveBatch(Collection<Bug> entityList, int batchSize) {
        boolean saveBatch = super.saveBatch(entityList, batchSize);
        entityList.forEach(e -> {
            IssueUpdateEvent.sendEvent(TypeClassify.BUG, e.getId(), null, e);
        });
        return saveBatch;
    }

    @Override
    public boolean updateAllById(Bug bug)
    {
        Bug oldBug = super.getById(bug.getId());

        if (oldBug == null)
        {
            throw BizException.validFail("修改失败,原因:未查询到该缺陷");
        }
        setOverView(bug);

        List<File> oldFiles = fileApi.findByBizTypeAndBizId(FileBizType.BUG_FILE_UPLOAD, bug.getId());

        List<Long> oldFileIds = oldFiles.stream().map(File::getId).collect(Collectors.toList());

        List<File> newFiles = bug.getFiles();

        if (newFiles == null || newFiles.isEmpty())
        {
            if (!oldFileIds.isEmpty())
            {
                fileApi.removeByFileBizTypeAndBizIds(FileBizType.BUG_FILE_UPLOAD, oldFileIds);
            }
        }
        else
        {
            List<Long> newFileIds = newFiles.stream().map(File::getId).collect(Collectors.toList());

            if (!oldFileIds.isEmpty())
            {
                // 筛选出需要删除的数据
                List<Long> deleteFileIds =
                        oldFileIds.stream().filter(item -> !newFileIds.contains(item)).collect(Collectors.toList());
                if (!deleteFileIds.isEmpty())
                {
                    fileApi.removeByFileBizTypeAndBizIds(FileBizType.BUG_FILE_UPLOAD, deleteFileIds);
                }

            }
            newFiles.forEach(item -> item.setBizId(bug.getId()).setBizType(FileBizType.BUG_FILE_UPLOAD));
            fileApi.updateBatchById(newFiles);
        }

        bug.setCode(oldBug.getCode());
        bug.setTypeCode(oldBug.getTypeCode());
        if (!bug.getStateCode().equals(oldBug.getStateCode()))
        {
            transitionState(bug.getId(), oldBug.getStateCode(), bug.getStateCode());
        }
        else
        {
            bug.setRateProgress(oldBug.getRateProgress());
            bug.setEndTime(oldBug.getEndTime());
        }
        bug.setCreateTime(oldBug.getCreateTime());
        bug.setCreatedBy(oldBug.getCreatedBy());
        bug.setUpdateTime(LocalDateTime.now());
        bug.setUpdatedBy(ContextUtil.getUserId());

        boolean updateAllById = super.updateAllById(bug);

        // 发送工作项改变事件
        oldBug.setFiles(oldFiles);
        if (!bug.getStateCode().equals(oldBug.getStateCode()))
        {
            IssueUpdateEvent.sendEvent(TypeClassify.BUG, bug.getId(), oldBug, bug);
        }
        // 场景2：变更缺陷时发送通知
        // 构建跳转字符串
        String updateJumpString = DetectionMessageContent.buildJumpString(
                oldBug.getCode(), bug.getName(), bug.getId(), TypeClassify.BUG, bug.getProjectId(),bug.getTypeCode());
        String creatorName = ContextUtil.getUserName();
        String creatorAccount = ContextUtil.getUserAccount();
        // 给负责人发送通知
        sendBugNotification(bug.getLeadingBy(), bug.getId(), "缺陷变更通知",
                String.format("%s(%s)变更了一个您负责的缺陷【%s】", creatorName, creatorAccount, updateJumpString));

        // 给处理人发送通知
        sendBugNotification(bug.getHandleBy(), bug.getId(), "缺陷变更通知",
                String.format("%s(%s)变更了一个您处理的缺陷【%s】", creatorName, creatorAccount, updateJumpString));

        return updateAllById;
    }

    @Override
    public void transitionState(Long bugId, String sourceStateCode, String targetStateCode)
    {
        // 查询当前缺陷节点id
        Bug bug = super.getById(bugId);
        if (null == bug)
        {
            throw BizException.validFail("状态流转失败,原因:未查询到缺陷信息");
        }
        if (!bug.getStateCode().equals(sourceStateCode))
        {
            throw BizException.validFail("状态流转失败,原因:缺陷状态已更新,请刷新重试");
        }

        // 查询缺陷使用的工作流
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(bug.getProjectId(),
                        TypeClassify.BUG, bug.getTypeCode());
        if (null == projectWorkflow || null == projectWorkflow.getWorkflowInfo())
        {
            throw BizException.validFail("状态流转失败,原因:获取缺陷使用工作流失败");
        }

        // 获取最后一次流转记录
        ProjectWorkflowTransitionHistory projectWorkflowTransitionHistory =
                projectWorkflowTransitionHistoryMapper.selectOne(Wraps.<ProjectWorkflowTransitionHistory>lbQ()
                        .eq(ProjectWorkflowTransitionHistory::getBizId, bugId)
                        .eq(ProjectWorkflowTransitionHistory::getTypeCode, bug.getTypeCode()).orderByDesc(
                                ProjectWorkflowTransitionHistory::getCreateTime).last(" limit 1"));

        Long sourceNodeId;
        LocalDateTime lastDateTime;
        // 如果最后一次流转记录为null，则使用缺陷的创建时间
        if (null == projectWorkflowTransitionHistory)
        {
            lastDateTime = bug.getCreateTime();

            WorkflowNode workflowNode =
                    projectWorkflowService.findProjectFirstNode(bug.getProjectId(), TypeClassify.BUG,
                            bug.getTypeCode());
            if (workflowNode == null)
            {
                throw BizException.validFail("状态流转失败,原因:获取缺陷初始状态节点失败");
            }
            sourceNodeId = workflowNode.getId();
        }
        else
        {

            lastDateTime = projectWorkflowTransitionHistory.getCreateTime();
            sourceNodeId = projectWorkflowTransitionHistory.getTarget();
        }
        // 查询目标节点id
        WorkflowNode targetNode = workflowNodeMapper.selectOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, targetStateCode));
        if (null == targetNode)
        {
            throw BizException.validFail("状态流转失败,原因:获取缺陷流转到状态信息失败");
        }
        // 计算流转花费时间
        LocalDateTime now = LocalDateTime.now();
        long spendTime = ChronoUnit.MILLIS.between(lastDateTime, now);

        // 修改缺陷信息
        Bug updateBug = Bug.builder()
                .id(bugId)
                .stateCode(targetStateCode)
                .rateProgress(targetNode.getProgress())
                .build();
        if (WorkflowNodeType.END_NODE.eq(targetNode.getNodeType()))
        {
            updateBug.setEndTime(now);
            updateBug.setDelay(bug.getPlanEtime() != null && now.isAfter(bug.getPlanEtime()));
        }else if(WorkflowNodeType.INTERMEDIATE_NODE.eq(targetNode.getNodeType())&&bug.getStartTime()==null){
            updateBug.setStartTime(now);
        }
        super.updateById(updateBug);

        // 添加流转历史记录
        ProjectWorkflowTransitionHistory insertHistory = ProjectWorkflowTransitionHistory.builder().bizId(bugId)
                .typeCode(bug.getTypeCode())
                .source(sourceNodeId)
                .sourceStateCode(sourceStateCode)
                .target(targetNode.getId())
                .targetStateCode(targetStateCode)
                .spendTime(spendTime)
                .createTime(now)
                .build();

        projectWorkflowTransitionHistoryMapper.insert(insertHistory);

        // 发送工作项改变事件
        IssueUpdateEvent.sendEvent(TypeClassify.BUG, bug.getId(),
                Bug.builder().id(bug.getId()).stateCode(sourceStateCode).build(),
                Bug.builder().id(bug.getId()).stateCode(targetStateCode).build()
                );
        // 场景3：缺陷状态流转时发送通知
        List<String> stateCodes = Arrays.asList(sourceStateCode, targetStateCode);
        List<State> states = stateMapper.selectList(Wraps.<State>lbQ().in(State::getCode, stateCodes));
        Map<String, String> stateCodeNameMap = states.stream()
                .collect(Collectors.toMap(State::getCode, State::getName));

        // 构建跳转字符串
        String stateJumpString = DetectionMessageContent.buildJumpString(
                bug.getCode(), bug.getName(), bug.getId(), TypeClassify.BUG, bug.getProjectId(),bug.getTypeCode());
        String creatorName = ContextUtil.getUserName();
        String creatorAccount = ContextUtil.getUserAccount();
        String sourceState = stateCodeNameMap.get(sourceStateCode);
        String targetState = stateCodeNameMap.get(targetStateCode);

        // 给负责人发送通知
        sendBugNotification(bug.getLeadingBy(), bug.getId(), "缺陷状态流转通知",
                String.format("%s(%s)将您负责的缺陷【%s】由【%s】流转到【%s】",
                        creatorName, creatorAccount, stateJumpString, sourceState, targetState));

        // 给处理人发送通知
        sendBugNotification(bug.getHandleBy(), bug.getId(), "缺陷状态流转通知",
                String.format("%s(%s)将您处理的缺陷【%s】由【%s】流转到【%s】",
                        creatorName, creatorAccount, stateJumpString, sourceState, targetState));

    }

    @Override
    public void calculationDelay()
    {
        baseMapper.calculationDelay();
    }

    @Override
    public List<WorkflowNode> findNextNode(Long bugId)
    {
        Bug bug = super.getById(bugId);
        if (null == bug.getProjectId())
        {
            throw BizException.validFail("当前缺陷未关联项目，无法查询流转状态");
        }
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(bug.getProjectId(),
                        TypeClassify.BUG, bug.getTypeCode());

        if (null == projectWorkflow)
        {
            throw BizException.validFail("当前缺陷未查询到工作流，无法查询流转状态");
        }
        // 查询该缺陷的当前节点
        WorkflowNode thisWorkflowNode = workflowNodeMapper.selectOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, bug.getStateCode()));

        if (null == thisWorkflowNode)
        {
            throw BizException.validFail("获取当前缺陷工作流状态节点失败");
        }

        // 查询可流转到的节点
        return getTargetNodeIds(bug, projectWorkflow.getWorkflowId(), thisWorkflowNode.getId());
    }

    /**
     * 获取流转到的节点
     *
     * @param bug        缺陷
     * @param workflowId 工作流id
     * @param thisNodeId 当前节点id
     * @return List<Long> 目标节点id
     * <AUTHOR>
     * @date 2021/11/15 13:01
     * @update zxy 2021/11/15 13:01
     * @since 1.0
     */
    private List<WorkflowNode> getTargetNodeIds(Bug bug, Long workflowId, Long thisNodeId)
    {
        List<WorkflowTransition> workflowTransitions = workflowTransitionMapper.selectList(
                Wraps.<WorkflowTransition>lbQ().eq(WorkflowTransition::getWorkflowId, workflowId)
                        .eq(WorkflowTransition::getSource, thisNodeId));

        if (workflowTransitions.isEmpty())
        {
            return Collections.emptyList();
        }
        boolean remove = true;
        Iterator<WorkflowTransition> iterator = workflowTransitions.iterator();
        while (iterator.hasNext())
        {
            WorkflowTransition workflowTransition = iterator.next();
            // 查询流转权限
            List<ProjectWorkflowAuthority> projectWorkflowAuthorities = projectWorkflowAuthorityMapper.selectList(
                    Wraps.<ProjectWorkflowAuthority>lbQ().eq(ProjectWorkflowAuthority::getWorkflowId, workflowId)
                            .eq(ProjectWorkflowAuthority::getProjectId, bug.getProjectId())
                            .eq(ProjectWorkflowAuthority::getTypeClassify, TypeClassify.BUG)
                            .eq(ProjectWorkflowAuthority::getTypeCode, bug.getTypeCode())
                            .eq(ProjectWorkflowAuthority::getTransitionId, workflowTransition.getId()));

            List<WorkflowAuthority> workflowAuthorities;
            if (projectWorkflowAuthorities.isEmpty())
            {
                workflowAuthorities = workflowAuthorityMapper.selectList(
                        Wraps.<WorkflowAuthority>lbQ()
                                .eq(WorkflowAuthority::getTransitionId, workflowTransition.getId()));
            }
            else
            {
                workflowAuthorities = BeanPlusUtil.toBeanList(projectWorkflowAuthorities, WorkflowAuthority.class);
            }
            boolean authorityBreak = false;
            for (WorkflowAuthority workflowAuthority : workflowAuthorities)
            {
                switch (workflowAuthority.getType())
                {
                    case RESPONSIBLE:
                        if (ContextUtil.getUserId().equals(bug.getLeadingBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case HANDLER:
                        if (ContextUtil.getUserId().equals(bug.getHandleBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case CREATOR:
                        if (ContextUtil.getUserId().equals(bug.getPutBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case USER:
                        if (ContextUtil.getUserId().equals(workflowAuthority.getUserOrRole()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case ROLE:
                        List<ProjectUserRole> userRoles =
                                projectUserRoleMapper.selectList(
                                        Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getRoleId)
                                                .eq(ProjectUserRole::getProjectId, bug.getProjectId())
                                                .eq(ProjectUserRole::getUserId, ContextUtil.getUserId()));
                        Set<Long> roleIds =
                                userRoles.stream().map(ProjectUserRole::getRoleId).collect(Collectors.toSet());
                        if (roleIds.contains(workflowAuthority.getUserOrRole()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    default:
                        break;
                }
                if (authorityBreak)
                {
                    break;
                }
            }

            if (remove)
            {
                iterator.remove();
            }

        }

        if (workflowTransitions.isEmpty())
        {
            return Collections.emptyList();
        }

        return workflowTransitions.stream().map(transition -> {
            WorkflowNode workflowNode = workflowNodeMapper.selectById(transition.getTarget());

            List<ProjectWorkflowTransitionCheck> checkList = projectWorkflowTransitionCheckService.findCheckList(transition.getId(),
                    bug.getProjectId(), TypeClassify.BUG, bug.getTypeCode());
            workflowNode.getEchoMap().put("check", checkList);
            return workflowNode;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return CollHelper.uniqueIndex(super.listByIds(ids), Bug::getId, bug -> bug.setDescription(null));
    }

    @Override
    public IssueTypeComponentResult findTypeByProjectId(Long projectId)
    {
        // 根据项目id查询所有缺陷
        List<Bug> bugs = baseMapper.selectList(Wraps.<Bug>lbQ().select(Bug.class, b -> !"description".equals(b.getProperty())).eq(Bug::getProjectId, projectId));

        // 查询所有状态
        List<State> states = stateMapper.selectList(Wraps.lbQ());

        // 按code和name分组
        Map<String, State> codeNameMap = states.stream().collect(Collectors.toMap(State::getCode, state -> state));

        Map<String, List<Bug>> groupStateCodeBugs = bugs.stream().collect(Collectors.groupingBy(Bug::getStateCode));

        List<IssueTypeComponentResult.Pie> data = new LinkedList<>();

        for (Map.Entry<String, List<Bug>> entry : groupStateCodeBugs.entrySet())
        {
            data.add(IssueTypeComponentResult.Pie.builder()
                    .name(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getName() :
                            entry.getKey())
                    .color(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getColor() :
                            entry.getKey())
                    .code(entry.getKey())
                    .value(entry.getValue().size()).build());
        }

        return IssueTypeComponentResult.builder().count(bugs.size()).data(data).build();
    }

    @Override
    public IssueTypeComponentResult findBugComponentByProductId(Long productId)
    {
        // 根据产品id查询所有缺陷
        List<Bug> bugs = super.list(Wraps.<Bug>lbQ().select(Bug.class, b -> !"description".equals(b.getProperty())).eq(Bug::getProductId, productId));

        // 查询所有状态
        List<State> states = stateMapper.selectList(Wraps.lbQ());

        // 按code和name分组
        Map<String, State> codeNameMap = states.stream().collect(Collectors.toMap(State::getCode, state -> state));

        Map<String, List<Bug>> groupStateCodeRequirements =
                bugs.stream().collect(Collectors.groupingBy(Bug::getStateCode));

        List<IssueTypeComponentResult.Pie> data = new LinkedList<>();

        for (Map.Entry<String, List<Bug>> entry : groupStateCodeRequirements.entrySet())
        {
            data.add(IssueTypeComponentResult.Pie.builder()
                    .name(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getName() :
                            entry.getKey())
                    .color(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getColor() :
                            entry.getKey())
                    .code(entry.getKey())
                    .value(entry.getValue().size()).build());
        }

        return IssueTypeComponentResult.builder().count(bugs.size()).data(data).build();
    }

    @Override
    public BugTrendComponentResult findTrendByProjectId(Long projectId, DateGranularityEnum granularity,
                                                        DateTypeEnum dateType)
    {
        LocalDate startDate = LocalDate.now().minus(7, ChronoUnit.DAYS);
        LocalDate endDate = LocalDate.now();
        // 解析时间查询条件
        switch (dateType)
        {
            case LAST_YEAR:
                startDate = startDate.minus(1, ChronoUnit.YEARS);
                break;
            case LAST_QUARTER:
                startDate = startDate.minus(3, ChronoUnit.MONTHS);
                break;
            case LAST_MONTH:
                startDate = startDate.minus(1, ChronoUnit.MONTHS);
                break;
            case LAST_WEEK:
            default:
                break;
        }
        // 根据项目id查询所有缺陷
        List<Bug> bugs = baseMapper.selectList(Wraps.<Bug>lbQ().select(Bug.class, b -> !"description".equals(b.getProperty())).eq(Bug::getProjectId, projectId)
                .between(Bug::getCreateTime, DateUtils.getDate0000(startDate), DateUtils.getDate2359(endDate)));

        // 按创建时间分组
        Map<String, List<Bug>> groupDateAddBugs;
        // 按完成（修复）时间分组
        Map<String, List<Bug>> groupDateCloseBugs;
        List<String> date;
        // 根据时间粒度分组
        switch (granularity)
        {
            case MONTH:
                date = DateUtils.getBetweenMonth(startDate, endDate);
                groupDateAddBugs = bugs.stream()
                        .collect(Collectors.groupingBy(item -> DateUtils.formatAsYearMonth(item.getCreateTime())));
                groupDateCloseBugs = bugs.stream().filter(item -> item.getEndTime() != null)
                        .collect(Collectors.groupingBy(item -> DateUtils.formatAsYearMonth(item.getEndTime())));
                break;
            case DAY:
            default:
                date = DateUtils.getBetweenDay(startDate, endDate);
                groupDateAddBugs = bugs.stream()
                        .collect(Collectors.groupingBy(item -> DateUtils.formatAsDate(item.getCreateTime())));
                groupDateCloseBugs =
                        bugs.stream().filter(item -> item.getEndTime() != null)
                                .collect(Collectors.groupingBy(item -> DateUtils.formatAsDate(item.getEndTime())));
                break;
        }
        List<Integer> residue = new LinkedList<>();
        int sumNum = 0;
        int sumClose = 0;
        List<Integer> add = new LinkedList<>();
        List<Integer> close = new LinkedList<>();
        for (String str : date)
        {
            if (groupDateAddBugs.containsKey(str))
            {
                List<Bug> thisBugs = groupDateAddBugs.get(str);
                sumNum += thisBugs.size();

                add.add(thisBugs.size());
            }
            else
            {
                add.add(0);
            }
            if (groupDateCloseBugs.containsKey(str))
            {
                sumClose += groupDateCloseBugs.get(str).size();
                close.add(groupDateCloseBugs.get(str).size());
            }
            else
            {
                close.add(0);
            }
            residue.add(sumNum - sumClose);
        }

        return BugTrendComponentResult.builder().date(date).residue(residue).add(add).close(close).build();
    }

    @Override
    public BugTrendComponentResult findTrendByProductId(Long productId, DateGranularityEnum granularity,
            DateTypeEnum dateType)
    {
        LocalDate startDate = LocalDate.now().minus(7, ChronoUnit.DAYS);
        LocalDate endDate = LocalDate.now();
        // 解析时间查询条件
        switch (dateType)
        {
            case LAST_YEAR:
                startDate = startDate.minus(1, ChronoUnit.YEARS);
                break;
            case LAST_QUARTER:
                startDate = startDate.minus(3, ChronoUnit.MONTHS);
                break;
            case LAST_MONTH:
                startDate = startDate.minus(1, ChronoUnit.MONTHS);
                break;
            case LAST_WEEK:
            default:
                break;
        }
        // 根据产品id查询所有缺陷
        List<Bug> bugs = super.list(Wraps.<Bug>lbQ().select(Bug.class, b -> !"description".equals(b.getProperty()))
                .eq(Bug::getProductId, productId)
                .between(Bug::getCreateTime, DateUtils.getDate0000(startDate), DateUtils.getDate2359(endDate)));

        // 按创建时间分组
        Map<String, List<Bug>> groupDateAddBugs;
        // 按完成（修复）时间分组
        Map<String, List<Bug>> groupDateCloseBugs;
        List<String> date;
        // 根据时间粒度分组
        switch (granularity)
        {
            case MONTH:
                date = DateUtils.getBetweenMonth(startDate, endDate);
                groupDateAddBugs = bugs.stream()
                        .collect(Collectors.groupingBy(item -> DateUtils.formatAsYearMonth(item.getCreateTime())));
                groupDateCloseBugs = bugs.stream()
                        .collect(Collectors.groupingBy(item -> DateUtils.formatAsYearMonth(item.getEndTime())));
                break;
            case DAY:
            default:
                date = DateUtils.getBetweenDay(startDate, endDate);
                groupDateAddBugs = bugs.stream()
                        .collect(Collectors.groupingBy(item -> DateUtils.formatAsDate(item.getCreateTime())));
                groupDateCloseBugs =
                        bugs.stream().filter(b -> b.getEndTime() != null)
                                .collect(Collectors.groupingBy(item -> DateUtils.formatAsDate(item.getEndTime())));
                break;
        }
        List<Integer> residue = new LinkedList<>();
        int sumNum = 0;
        int sumClose = 0;
        List<Integer> add = new LinkedList<>();
        List<Integer> close = new LinkedList<>();
        for (String str : date)
        {
            if (groupDateAddBugs.containsKey(str))
            {
                List<Bug> thisBugs = groupDateAddBugs.get(str);
                sumNum += thisBugs.size();

                add.add(thisBugs.size());
            }
            else
            {
                add.add(0);
            }
            if (groupDateCloseBugs.containsKey(str))
            {
                sumClose += groupDateCloseBugs.get(str).size();
                close.add(groupDateCloseBugs.get(str).size());
            }
            else
            {
                close.add(0);
            }
            residue.add(sumNum - sumClose);
        }

        return BugTrendComponentResult.builder().date(date).residue(residue).add(add).close(close).build();
    }

    @Override
    public IssueTypeComponentResult findBugComponentByPlanId(Long planId)
    {
        // 根据计划id查询所有缺陷
        List<Bug> bugs = super.list(Wraps.<Bug>lbQ().select(Bug.class, b -> !"description".equals(b.getProperty())).eq(Bug::getPlanId, planId));

        // 查询所有状态
        List<State> states = stateMapper.selectList(Wraps.lbQ());

        // 按code和name分组
        Map<String, State> codeNameMap = states.stream().collect(Collectors.toMap(State::getCode, state -> state));

        Map<String, List<Bug>> groupStateCodeRequirements =
                bugs.stream().collect(Collectors.groupingBy(Bug::getStateCode));

        List<IssueTypeComponentResult.Pie> data = new LinkedList<>();

        for (Map.Entry<String, List<Bug>> entry : groupStateCodeRequirements.entrySet())
        {
            data.add(IssueTypeComponentResult.Pie.builder()
                    .name(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getName() :
                            entry.getKey())
                    .color(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getColor() :
                            entry.getKey())
                    .code(entry.getKey())
                    .value(entry.getValue().size()).build());
        }

        return IssueTypeComponentResult.builder().count(bugs.size()).data(data).build();
    }

    @Override
    public Bug getById(Serializable id)
    {
        Bug bug = super.getById(id);

        if (bug == null) {
            return null;
        }

        bug.setFiles(fileApi.findByBizTypeAndBizId(FileBizType.BUG_FILE_UPLOAD, id));

        //测试计划被删除时，保留缺陷
//        Long testPlanId = bug.getTestPlanId();
//        TestPlan byId = testPlanApi.getById(testPlanId);
//        if (byId == null) {
//            bug.setTestPlanId(null);
//        } else {
//            bug.getEchoMap().put("testPlanId", byId);
//        }
//        Long testTaskId = bug.getTestTaskId();
//        Task byId = taskApi.getById(testTaskId);
//        if (byId == null) {
//            bug.setTestTaskId(null);
//        } else {
//            bug.getEchoMap().put("testTaskId", byId);
//        }

        //缺陷环境信息赋值
        String envCode = bug.getEnvCode();
        if(StringUtils.isNotEmpty(envCode)){
           Dictionary dic =  new Dictionary();
           dic.setCode(envCode);
           List<Dictionary> dicList = dictionaryApi.query(dic);
           if(dicList != null && !dicList.isEmpty()){
               bug.getEchoMap().put("envCode", dicList.get(0));
           }
        }
        return bug;
    }

    @Override
    public TestSummaryComponent dataFormat(Map<String, Long> map, Integer size) {
        TestSummaryComponent testCaseTypeComponentResult = new TestSummaryComponent();
        List<TestSummaryComponent.Pie> pies = new ArrayList<>();
        testCaseTypeComponentResult.setCount(size);

        for (String t : map.keySet()) {
            if ("HIGHEST".equals(t)) {
                pies.add(new TestSummaryComponent.Pie().setName("HIGHEST").setValue(map.get(t).intValue()));
            }
            if ("HIGH".equals(t)) {
                pies.add(new TestSummaryComponent.Pie().setName("HIGH").setValue(map.get(t).intValue()));
            }
            if ("MEDIUM".equals(t)) {
                pies.add(new TestSummaryComponent.Pie().setName("MEDIUM").setValue(map.get(t).intValue()));
            }
            if ("LOW".equals(t)) {
                pies.add(new TestSummaryComponent.Pie().setName("LOW").setValue(map.get(t).intValue()));
            }
            if ("LOWEST".equals(t)) {
                pies.add(new TestSummaryComponent.Pie().setName("LOWEST").setValue(map.get(t).intValue()));
            }
        }
        testCaseTypeComponentResult.setData(defaultPriorityData(pies));
        return testCaseTypeComponentResult;
    }

    public List<TestSummaryComponent.Pie> defaultPriorityData(List<TestSummaryComponent.Pie> pies) {
        List<String> list = new ArrayList<>();
        list.add("HIGHEST");
        list.add("HIGH");
        list.add("MEDIUM");
        list.add("LOW");
        list.add("LOWEST");
        for (TestSummaryComponent.Pie t : pies)
        {
            noExistPriority(list, t.getName());
        }
        for (String t : list)
        {
            pies.add(new TestSummaryComponent.Pie().setName(t).setValue(0));
        }
        return pies;
    }

    @Override
    public List<BugReportVO> queryBugReportVo(List<Long> planIds){
        List<BugReportVO> bugReportList = bugMapper.queryBugReportVo(planIds);
        Long allBugNum = 0L;
        for (BugReportVO bugReportVO : bugReportList){

            if (Objects.equals(bugReportVO.getPriorityCode(), null)){

                bugReportVO.setPriorityCode("MEDIUM");
            }
            allBugNum = allBugNum + bugReportVO.getNum();
        }
        for (BugReportVO bugReportVO : bugReportList){

            bugReportVO.setRate(new Long(bugReportVO.getNum() / allBugNum *100).intValue());
        }
        //???? 是不是有问题
        if (!bugReportList.contains("HIGHEST")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("HIGHEST");
            bugStatusVO.setNum(0L);
        }else if (!bugReportList.contains("HIGH")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("HIGH");
            bugStatusVO.setNum(0L);
        }else if (!bugReportList.contains("MEDIUM")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("MEDIUM");
            bugStatusVO.setNum(0L);
        }else if (!bugReportList.contains("LOW")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("LOW");
            bugStatusVO.setNum(0L);
        }else if (!bugReportList.contains("LOWEST")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("LOWEST");
            bugStatusVO.setNum(0L);
        }
        return bugReportList;
    }


    @Override
    public List<BugReportVO> defaultBugReportVoData(List<BugReportVO> bugReportVOS)
    {
        List<String> list = new ArrayList<>();
        list.add("HIGHEST");
        list.add("HIGH");
        list.add("MEDIUM");
        list.add("LOW");
        list.add("LOWEST");
        for (BugReportVO t : bugReportVOS)
        {
            noExistPriority(list, t.getPriorityCode());
        }
        for (String t : list) {
            bugReportVOS.add(new BugReportVO().setPriorityCode(t));
        }
        return bugReportVOS;
    }

    public void noExistPriority(List<String> list, String s) {
        String remove = "";
        for (String t : list) {
            if (t.equals(s)) {
                remove = s;
                break;
            }
        }
        list.remove(remove);
    }

    @Override
    public void saveOverView(Bug bug)
    {
        TestOverview testOverview = testPlanApi.getOverView();
        switch (bug.getPriorityCode())
        {
            case "HIGHEST":
                testOverview.setBugHighest(testOverview.getBugHighest()==null? 0:testOverview.getBugHighest() + 1);
                break;
            case "HIGH":
                testOverview.setBugHigher(testOverview.getBugHigher()==null? 0:testOverview.getBugHigher() + 1);
                break;
            case "MEDIUM":
                testOverview.setBugOrdinary(testOverview.getBugOrdinary()==null? 0:testOverview.getBugOrdinary() + 1);
                break;
            case "LOW":
                testOverview.setBugLower(testOverview.getBugLower()==null? 0:testOverview.getBugLower() + 1);
                break;
            case "LOWEST":
                testOverview.setBugMinimun(testOverview.getBugMinimun()==null? 0:testOverview.getBugMinimun() + 1);
                break;
            default:
                break;
        }
        testPlanApi.updateOverview(testOverview);
    }

    @Override
    public void setOverView(Bug bug)
    {
        Bug oldBug = baseMapper.selectById(bug.getId());
        TestOverview testOverview = testPlanApi.getOverView();
        if (!oldBug.getPriorityCode().equals(bug.getPriorityCode()))
        {
            switch (bug.getPriorityCode())
            {
                case "HIGHEST":
                    testOverview.setBugHighest(testOverview.getBugHighest()==null? 0:testOverview.getBugHighest() + 1);
                    break;
                case "HIGH":
                    testOverview.setBugHigher(testOverview.getBugHigher()==null? 0:testOverview.getBugHigher() + 1);
                    break;
                case "MEDIUM":
                    testOverview.setBugOrdinary(testOverview.getBugOrdinary()==null? 0:testOverview.getBugOrdinary() + 1);
                    break;
                case "LOW":
                    testOverview.setBugLower(testOverview.getBugLower()==null? 0:testOverview.getBugLower() + 1);
                    break;
                case "LOWEST":
                    testOverview.setBugMinimun(testOverview.getBugMinimun()==null? 0:testOverview.getBugMinimun() + 1);
                    break;
                default:
                    break;
            }
            switch (oldBug.getPriorityCode())
            {
                case "HIGHEST":
                    testOverview.setBugHighest(testOverview.getBugHighest() - 1);
                    break;
                case "HIGH":
                    testOverview.setBugHigher(testOverview.getBugHigher() - 1);
                    break;
                case "MEDIUM":
                    testOverview.setBugOrdinary(testOverview.getBugOrdinary() - 1);
                    break;
                case "LOW":
                    testOverview.setBugLower(testOverview.getBugLower() - 1);
                    break;
                case "LOWEST":
                    testOverview.setBugMinimun(testOverview.getBugMinimun() - 1);
                    break;
                default:
                    break;
            }
        }
        testPlanApi.updateOverview(testOverview);
    }

    @Override
    public void deleteOverView(Bug bug)
    {
        if (bug.getPriorityCode() == null){
            return;
        }
        TestOverview testOverview = testPlanApi.getOverView();
            switch (bug.getPriorityCode())
            {
                case "HIGHEST":
                    testOverview.setBugHighest(testOverview.getBugHighest() - 1);
                    break;
                case "HIGH":
                    testOverview.setBugHigher(testOverview.getBugHigher() - 1);
                    break;
                case "MEDIUM":
                    testOverview.setBugOrdinary(testOverview.getBugOrdinary() - 1);
                    break;
                case "LOW":
                    testOverview.setBugLower(testOverview.getBugLower() - 1);
                    break;
                case "LOWEST":
                    testOverview.setBugMinimun(testOverview.getBugMinimun() - 1);
                    break;
                default:
                    break;

        }
        testPlanApi.updateOverview(testOverview);
    }


    @Override
    public List<LocalDateTime> selectdoneNum(Bug bug)
    {
        return baseMapper.selectdoneNum(bug);
    }


    @Override
    public BugTrendComponentResult queryViewByTime(BugStateViewVO viewVO) {
        // 获取入参数据
        Long projectId = viewVO.getProjectId();
        Long productId = viewVO.getProductId();
        Long planId = viewVO.getPlanId();
        PeriodQuery<LocalDate> scopeDate = viewVO.getScopeTime();
        LocalDate nowDate = LocalDate.now();
        if (scopeDate == null) {
            scopeDate = PeriodQuery.of(nowDate.minusDays(7), nowDate);
        }

        LocalDate start = ObjectUtil.defaultIfNull(scopeDate.getStart(), nowDate.minusDays(7));
        LocalDate end = ObjectUtil.defaultIfNull(scopeDate.getEnd(), nowDate);
        Integer cycle = ObjectUtil.defaultIfNull(viewVO.getCycle(), 1);

        // 构建查询条件
        LbqWrapper<Bug> wrapper = Wraps.lbQ();
        if (projectId != null) {
            wrapper.eq(Bug::getProjectId, projectId);
        } else if (planId != null) {
            wrapper.eq(Bug::getPlanId, planId);
        } else if (productId != null) {
            wrapper.eq(Bug::getProductId, productId);
        } else {
            throw BizException.wrap("项目id、产品id、计划id不能同时为空");
        }
        // 拼接时间查询条件
        PeriodQuery<LocalDateTime> scopeTime = PeriodQuery.of(start.atTime(LocalTime.MIN), end.atTime(LocalTime.MAX));
        scopeTime.appendQuery(wrapper, Bug::getCreateTime);

        // 查询出所有的缺陷
        List<Bug> bugList = this.list(wrapper);


        List<String> date = new ArrayList<>();
        List<Integer> residue = new ArrayList<>();
        List<Integer> add = new ArrayList<>();
        List<Integer> close = new ArrayList<>();

        // 循环计算每天的数据
        for (LocalDate now = LocalDate.from(start); now.isBefore(end.plusDays(1)); now = now.plusDays(cycle)) {
            date.add(ISO_LOCAL_DATE.format(now));
            LocalDateTime finalStart = now.atTime(LocalTime.MIN);
            LocalDateTime finalEnd = now.plusDays(cycle).atTime(LocalTime.MIN);

            // 计算当天新增的缺陷数
            long addCount = bugList.stream()
                    .filter(bug -> bug.getCreateTime().isAfter(finalStart))
                    .filter(bug -> bug.getCreateTime().isBefore(finalEnd))
                    .count();
            add.add((int) addCount);
            // 计算当天关闭的缺陷数
            long closeCount = bugList.stream()
                    .filter(bug -> bug.getEndTime() != null)
                    .filter(bug -> bug.getEndTime().isAfter(finalStart))
                    .filter(bug -> bug.getEndTime().isBefore(finalEnd))
                    .count();
            close.add((int) closeCount);
            // 计算当天剩余的缺陷数
            long residueCount = bugList.stream()
                    .filter(bug -> bug.getCreateTime().isBefore(finalEnd))
                    .filter(bug -> bug.getEndTime() == null || bug.getEndTime().isAfter(finalEnd))
                    .count();
            residue.add((int) residueCount);

        }
        return BugTrendComponentResult.builder()
                .date(date)
                .add(add)
                .close(close)
                .residue(residue)
                .build();
    }

    /**
     * 查询具有流转权限角色的用户id
     * @param oldBug
     * @return {@link List< Long>}
     * @throws
     * <AUTHOR>
     * @date 2025/8/7 11:48
     * @update sxh 2025/8/7 11:48
     * @since 1.0
     */
    @Override
    public List<Long> getWorkFlowUserIds(Bug oldBug) {
        List<Long> allUserIds = new ArrayList<>();
        // 查询该任务使用的工作流
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(oldBug.getProjectId(),
                        TypeClassify.BUG,
                        oldBug.getTypeCode());
        if (null == projectWorkflow || null == projectWorkflow.getWorkflowInfo()) {
            throw BizException.validFail("当前任务未查询到工作流，无法查询可流转状态");
        }
        // 查询该任务的当前节点
        WorkflowNode thisWorkflowNode = workflowNodeMapper.selectOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, oldBug.getStateCode()));

        if (null == thisWorkflowNode) {
            throw BizException.validFail("获取当前任务工作流状态节点失败");
        }
        // 获取流转到的节点
        List<WorkflowTransition> workflowTransitions = workflowTransitionMapper.selectList(
                Wraps.<WorkflowTransition>lbQ().eq(WorkflowTransition::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowTransition::getSource, thisWorkflowNode.getId()));

        if (workflowTransitions.isEmpty()) {
            return Collections.emptyList();
        }
        Iterator<WorkflowTransition> iterator = workflowTransitions.iterator();
        while (iterator.hasNext()) {
            WorkflowTransition workflowTransition = iterator.next();
            // 查询流转权限
            List<ProjectWorkflowAuthority> projectWorkflowAuthorities = projectWorkflowAuthorityMapper.selectList(
                    Wraps.<ProjectWorkflowAuthority>lbQ().eq(ProjectWorkflowAuthority::getWorkflowId, projectWorkflow.getWorkflowId())
                            .eq(ProjectWorkflowAuthority::getProjectId, oldBug.getProjectId())
                            .eq(ProjectWorkflowAuthority::getTypeClassify, TypeClassify.BUG)
                            .eq(ProjectWorkflowAuthority::getTypeCode, oldBug.getTypeCode())
                            .eq(ProjectWorkflowAuthority::getTransitionId, workflowTransition.getId())
                            .eq(ProjectWorkflowAuthority::getType, WorkflowAuthorityType.ROLE));


            if (projectWorkflowAuthorities.size() != 0) {
                List<WorkflowAuthority> workflowAuthorities = BeanPlusUtil.toBeanList(projectWorkflowAuthorities, WorkflowAuthority.class);
                for(WorkflowAuthority workflowAuthority:workflowAuthorities){
                    List<ProjectUserRole> projectUserRoles =
                            projectUserRoleMapper.selectList(
                                    Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getUserId)
                                            .eq(ProjectUserRole::getProjectId, oldBug.getProjectId())
                                            .eq(ProjectUserRole::getRoleId, workflowAuthority.getUserOrRole()));
                    List<Long> userIds =
                            projectUserRoles.stream().map(ProjectUserRole::getUserId).collect(Collectors.toList());
                    allUserIds.addAll(userIds);
                }

            }

            List<WorkflowAuthority> workflowAuthorities = workflowAuthorityMapper.selectList(
                    Wraps.<WorkflowAuthority>lbQ()
                            .eq(WorkflowAuthority::getTransitionId, workflowTransition.getId())
                            .eq(WorkflowAuthority::getType, WorkflowAuthorityType.ROLE));
            for(WorkflowAuthority workflowAuthority:workflowAuthorities){
                List<ProjectUserRole> projectUserRoles =
                        projectUserRoleMapper.selectList(
                                Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getUserId)
                                        .eq(ProjectUserRole::getProjectId, oldBug.getProjectId())
                                        .eq(ProjectUserRole::getRoleId, workflowAuthority.getUserOrRole()));
                List<Long> userIds =
                        projectUserRoles.stream().map(ProjectUserRole::getUserId).collect(Collectors.toList());
                allUserIds.addAll(userIds);
            }

        }
        //新增项目经理用户的id
        ProjectInfo projectInfo = projectInfoMapper.selectById(oldBug.getProjectId());
        String pmRoleCode = projectInfo.getTypeCode() + "_PM";
        // 查询项目经理角色id
        ProjectRole projectRole = projectRoleService.getOne(
                Wraps.<ProjectRole>lbQ().eq(ProjectRole::getCode, pmRoleCode).eq(ProjectRole::getReadonly, true)
                        .eq(ProjectRole::getType, true).eq(ProjectRole::getTypeCode, projectInfo.getTypeCode())
                        .last(" limit 1"));
        //查询用户信息
        List<Long> pmUserIds = projectUserRoleMapper.selectList(
                        Wraps.<ProjectUserRole>lbQ()
                                .eq(ProjectUserRole::getProjectId, oldBug.getProjectId())
                                .eq(ProjectUserRole::getRoleId, projectRole.getId())
                ).stream()
                .map(ProjectUserRole::getUserId)
                .collect(Collectors.toList());
        allUserIds.addAll(pmUserIds);
        return allUserIds;
    }

    @Override
    public List<BugReportVO> queryBugReportVoByTaskIds(List<Long> taskIds){
        List<BugReportVO> bugReportVOS = bugMapper.queryBugReportVoByTaskIds(taskIds);
        Long allBugNum = 0L;
        for (BugReportVO bugReportVO : bugReportVOS){

            if (Objects.equals(bugReportVO.getPriorityCode(), null)){

                bugReportVO.setPriorityCode("MEDIUM");
            }
            allBugNum = allBugNum + bugReportVO.getNum();
        }
        for (BugReportVO bugReportVO : bugReportVOS){

            bugReportVO.setRate(new Long(bugReportVO.getNum() / allBugNum *100).intValue());
        }
        if (!bugReportVOS.contains("HIGHEST")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("HIGHEST");
            bugStatusVO.setNum(0L);
        }else if (!bugReportVOS.contains("HIGH")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("HIGH");
            bugStatusVO.setNum(0L);
        }else if (!bugReportVOS.contains("MEDIUM")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("MEDIUM");
            bugStatusVO.setNum(0L);
        }else if (!bugReportVOS.contains("LOW")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("LOW");
            bugStatusVO.setNum(0L);
        }else if (!bugReportVOS.contains("LOWEST")){
            BugReportVO bugStatusVO = new BugReportVO();
            bugStatusVO.setRate(0);
            bugStatusVO.setPriorityCode("LOWEST");
            bugStatusVO.setNum(0L);
        }
        return bugReportVOS;
    }

    // 提取发送站内信的通用方法
    private void sendBugNotification(Long userId, Long bugId, String title, String content) {
        // 校验用户ID，若为空或为创建人则不发送
        if (userId == null || userId.equals(ContextUtil.getUserId())) {
            return;
        }

        // 构建消息对象
        MsgDTO msgDTO = MsgDTO.buildNotify(MsgBizType.BUG_NOTIFY, bugId, title, content);
        msgDTO.setAuthorId(ContextUtil.getUserId());

        // 构建消息保存对象并发送
        Set<Long> userIds = Collections.singleton(userId);
        MsgSaveDTO msgSaveDTO = MsgSaveDTO.builder()
                .userIdList(userIds)
                .msgDTO(msgDTO)
                .build();
        msgApi.save(msgSaveDTO);
    }

}
