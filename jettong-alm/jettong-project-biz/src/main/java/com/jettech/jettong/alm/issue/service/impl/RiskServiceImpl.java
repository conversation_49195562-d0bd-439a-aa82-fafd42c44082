package com.jettech.jettong.alm.issue.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.repository.CacheOps;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.StringUtil;
import com.jettech.jettong.alm.issue.dao.RiskMapper;
import com.jettech.jettong.alm.issue.dao.StateMapper;
import com.jettech.jettong.alm.issue.dto.RiskExportQuery;
import com.jettech.jettong.alm.issue.entity.Bug;
import com.jettech.jettong.alm.issue.entity.Risk;
import com.jettech.jettong.alm.issue.entity.Type;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.issue.service.RiskService;
import com.jettech.jettong.alm.issue.service.TypeService;
import com.jettech.jettong.alm.issue.service.event.IssueUpdateEvent;


import com.jettech.jettong.alm.project.dao.*;
import com.jettech.jettong.alm.project.entity.*;
import com.jettech.jettong.alm.project.service.ProjectWorkflowService;
import com.jettech.jettong.alm.project.service.ProjectWorkflowTransitionCheckService;
import com.jettech.jettong.alm.workflow.dao.WorkflowAuthorityMapper;
import com.jettech.jettong.alm.workflow.dao.WorkflowNodeMapper;
import com.jettech.jettong.alm.workflow.dao.WorkflowTransitionMapper;
import com.jettech.jettong.alm.workflow.entity.WorkflowAuthority;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;
import com.jettech.jettong.alm.workflow.entity.WorkflowTransition;
import com.jettech.jettong.alm.workflow.enumeration.WorkflowNodeType;
import com.jettech.jettong.base.api.FileApi;
import com.jettech.jettong.base.api.MsgApi;
import com.jettech.jettong.base.dto.msg.MsgDTO;
import com.jettech.jettong.base.dto.msg.MsgSaveDTO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.common.cache.alm.TypeCodePrefixCacheKeyBuilder;
import com.jettech.jettong.common.constant.MsgBizType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 风险信息业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 风险信息业务层
 * @projectName jettong
 * @package com.jettech.jettong.alm.issue.service.impl
 * @className RiskServiceImpl
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class RiskServiceImpl extends SuperServiceImpl<RiskMapper, Risk> implements RiskService
{
    private final CacheOps cacheOps;
    private final TypeService typeService;
    private final ProjectWorkflowService projectWorkflowService;
    private final ProjectWorkflowAuthorityMapper projectWorkflowAuthorityMapper;
    private final WorkflowAuthorityMapper workflowAuthorityMapper;
    private final WorkflowTransitionMapper workflowTransitionMapper;
    private final WorkflowNodeMapper workflowNodeMapper;
    private final ProjectUserMapper projectUserMapper;
    private final ProjectUserRoleMapper projectUserRoleMapper;
    private final ProjectWorkflowTransitionHistoryMapper projectWorkflowTransitionHistoryMapper;
    private final StateMapper stateMapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final FileApi fileApi;
    private final ProjectWorkflowTransitionCheckService transitionCheckService;

    private static final String PROJECT = "project";
    private static final String PROGRAM = "program";
    private final MsgApi msgApi;

    @Override
    public boolean saveBatch(Collection<Risk> entityList, int batchSize) {
        boolean saveBatch = super.saveBatch(entityList, batchSize);
        // 推送 工作项改变事件
        entityList.forEach(e -> IssueUpdateEvent.sendEvent(TypeClassify.RISK, e.getId(), null, e));
        return saveBatch;
    }

    @Override
    public boolean save(Risk risk)
    {
        Type type = typeService.findByCode(risk.getTypeCode());

        if (type == null)
        {
            throw BizException.validFail("获取事项类型失败,请联系管理员");
        }
        // 自动生成一个code
        risk.setCode(typeService.getCode(type));

        // 查询风险的初始化状态
        if (null == risk.getProjectId() && null == risk.getProgramId())
        {
            throw BizException.validFail("缺少必填参数projectId或programId");
        }
        WorkflowNode workflowNode;
        if (null != risk.getProgramId())
        {
            workflowNode = projectWorkflowService.findProjectFirstNode(risk.getProjectId(), TypeClassify.RISK,
                    risk.getTypeCode());

        }
        else
        {
            workflowNode = projectWorkflowService.findProjectProgramFirstNode(risk.getProgramId(), TypeClassify.RISK,
                    risk.getTypeCode());
        }

        if (null == workflowNode)
        {
            throw BizException.validFail("获取该风险类型工作流失败，请联系管理员");
        }
        risk.setStateCode(workflowNode.getStateCode());


        boolean add = super.save(risk);

        // 推送 工作项改变事件
        IssueUpdateEvent.sendEvent(TypeClassify.RISK, risk.getId(), null, risk);

        if (add)
        {
            // 修改附件
            List<File> files = risk.getFiles();
            if (files != null && !files.isEmpty())
            {
                files.forEach(item -> item.setBizType(FileBizType.RISK_FILE_UPLOAD).setBizId(risk.getId()));

                fileApi.updateBatchById(files);
            }
            // 给负责人和处理人发送站内信（创建场景）
            String jumpString = DetectionMessageContent.buildJumpString(
                    risk.getCode(),
                    risk.getName(),
                    risk.getId(),
                    TypeClassify.RISK,
                    risk.getProjectId(),risk.getTypeCode());

            // 发送给负责人（非创建人时）
            sendRiskNotifyMsg(
                    risk.getLeadingBy(),
                    ContextUtil.getUserId(),
                    "您负责的",
                    jumpString,
                    risk.getId(),
                    "创建了",  // 动作描述：创建/变更
                    "风险创建通知"  // 消息标题
            );

            // 发送给处理人（非创建人时）
            sendRiskNotifyMsg(
                    risk.getHandleBy(),
                    ContextUtil.getUserId(),
                    "您处理的",
                    jumpString,
                    risk.getId(),
                    "创建了",
                    "风险创建通知"
            );
        }
        return add;
    }

    /**
     * 获取风险的Code，从redis中获取
     *
     * @param type 工作项类型
     * @return String code
     * <AUTHOR>
     * @date 2021/11/12 9:42
     * @update zxy 2021/11/12 9:42
     * @since 1.0
     */
    private synchronized String getCode(Type type)
    {
        String prefix = type.getPrefix();
        CacheKey cacheKey = new TypeCodePrefixCacheKeyBuilder().key(prefix);
        Long codeNum = cacheOps.get(cacheKey, key -> {
            // 获取前缀相同的类型，防止相同前缀但不同类型的工作项 导致获取到值不是最大的bug
            Set<String> typeCodeSet = typeService.list(Wraps.<Type>lbQ().eq(Type::getPrefix, prefix))
                    .stream().map(Type::getCode).collect(Collectors.toSet());
            // 根据时间排序，获取最新一条数据的code
            String code = super.getObj(
                    Wraps.<Risk>lbQ().select(Risk::getCode)
                            .in(Risk::getTypeCode, typeCodeSet)
                            .likeRight(Risk::getCode, prefix)
                            .orderByDesc(Risk::getCreateTime)
                            .last(" limit 1 "),
                    Convert::toStr);
            return StringUtil.getLongToStr(StringUtil.removePrefix(code, prefix));
        });
        codeNum++;
        cacheOps.set(cacheKey, codeNum);
        return prefix + codeNum;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<? extends Serializable> ids)
    {
        // 删除附件信息
        fileApi.removeByFileBizTypeAndBizIds(FileBizType.RISK_FILE_UPLOAD, ids);

        //删除状态流转历史
        projectWorkflowTransitionHistoryMapper.delete(Wraps.<ProjectWorkflowTransitionHistory>lbQ()
                .in(ProjectWorkflowTransitionHistory::getBizId, ids));

        boolean remove = super.removeByIds(ids);

        // 推送 工作项改变事件
        ids.forEach(id -> IssueUpdateEvent.sendEvent(TypeClassify.RISK, (Long) id, null, null));

        return remove;
    }

    @Override
    public boolean updateAllById(Risk risk)
    {
        Risk oldRisk = super.getById(risk.getId());

        if (oldRisk == null) {
            throw BizException.validFail("修改失败,原因:未查询到该风险");
        }

        List<File> oldFiles = fileApi.findByBizTypeAndBizId(FileBizType.RISK_FILE_UPLOAD, risk.getId());

        List<Long> oldFileIds = oldFiles.stream().map(File::getId).collect(Collectors.toList());

        List<File> newFiles = risk.getFiles();

        if (newFiles == null || newFiles.isEmpty())
        {
            if (!oldFileIds.isEmpty())
            {
                fileApi.removeByFileBizTypeAndBizIds(FileBizType.RISK_FILE_UPLOAD, oldFileIds);
            }
        }
        else
        {
            List<Long> newFileIds = newFiles.stream().map(File::getId).collect(Collectors.toList());

            if (!oldFileIds.isEmpty())
            {
                // 筛选出需要删除的数据
                List<Long> deleteFileIds =
                        oldFileIds.stream().filter(item -> !newFileIds.contains(item)).collect(Collectors.toList());

                fileApi.removeByFileBizTypeAndBizIds(FileBizType.RISK_FILE_UPLOAD, deleteFileIds);

            }
            newFiles.forEach(item -> item.setBizId(risk.getId()).setBizType(FileBizType.RISK_FILE_UPLOAD));
            fileApi.updateBatchById(newFiles);
        }

        risk.setCode(oldRisk.getCode());
        risk.setTypeCode(oldRisk.getTypeCode());
        if (!risk.getStateCode().equals(oldRisk.getStateCode()))
        {
            transitionState(risk.getId(), oldRisk.getStateCode(), risk.getStateCode(), null != oldRisk.getProjectId() ? PROJECT : PROGRAM);
        }
        else
        {
            risk.setEndTime(oldRisk.getEndTime());
        }
        risk.setCreateTime(oldRisk.getCreateTime());
        risk.setCreatedBy(oldRisk.getCreatedBy());
        risk.setUpdateTime(LocalDateTime.now());
        risk.setUpdatedBy(ContextUtil.getUserId());

        boolean update = super.updateAllById(risk);

        // 推送 工作项改变事件
        oldRisk.setFiles(oldFiles);
        if (!risk.getStateCode().equals(oldRisk.getStateCode()))
        {
            IssueUpdateEvent.sendEvent(TypeClassify.RISK, risk.getId(), oldRisk, risk);
        }
        // 给负责人和处理人发送站内信

        // 给负责人和处理人发送站内信（变更场景，复用同一方法）
        String updateJumpString = DetectionMessageContent.buildJumpString(
                oldRisk.getCode(),
                risk.getName(),
                risk.getId(),
                TypeClassify.RISK,
                risk.getProjectId(),risk.getTypeCode());

        // 发送给负责人（非创建人时）
        sendRiskNotifyMsg(
                risk.getLeadingBy(),
                ContextUtil.getUserId(),
                "您负责的",
                updateJumpString,
                risk.getId(),
                "变更了",  // 动作描述：创建/变更
                "风险变更通知"  // 消息标题
        );

        // 发送给处理人（非创建人时）
        sendRiskNotifyMsg(
                risk.getHandleBy(),
                ContextUtil.getUserId(),
                "您处理的",
                updateJumpString,
                risk.getId(),
                "变更了",
                "风险变更通知"
        );

        return update;
    }

    @Override
    public void transitionState(Long riskId, String sourceStateCode, String targetStateCode, String projectType)
    {
        // 查询当前风险节点id
        Risk risk = super.getById(riskId);
        if (null == risk)
        {
            throw BizException.validFail("状态流转失败,原因:未查询到风险信息");
        }
        if (!risk.getStateCode().equals(sourceStateCode))
        {
            throw BizException.validFail("状态流转失败,原因:风险状态已更新,请刷新重试");
        }

        // 查询风险使用的工作流
        ProjectWorkflow projectWorkflow = null;
        if (PROJECT.equals(projectType))
        {
            projectWorkflow =
                    projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(risk.getProjectId(),
                            TypeClassify.RISK, risk.getTypeCode());
        }
        else if (PROGRAM.equals(projectType))
        {
            projectWorkflow =
                    projectWorkflowService.findProjectByProgramIdAndTypeClassifyAndTypeCode(risk.getProgramId(),
                            TypeClassify.RISK, risk.getTypeCode());
        }
        if (null == projectWorkflow || null == projectWorkflow.getWorkflowId())
        {
            throw BizException.validFail("状态流转失败,原因:获取风险使用工作流失败");
        }

        // 获取最后一次流转记录
        ProjectWorkflowTransitionHistory projectWorkflowTransitionHistory =
                projectWorkflowTransitionHistoryMapper.selectOne(Wraps.<ProjectWorkflowTransitionHistory>lbQ()
                        .eq(ProjectWorkflowTransitionHistory::getBizId, riskId)
                        .eq(ProjectWorkflowTransitionHistory::getTypeCode, risk.getTypeCode()).orderByDesc(
                                ProjectWorkflowTransitionHistory::getCreateTime).last(" limit 1"));

        Long sourceNodeId;
        LocalDateTime lastDateTime;
        // 如果最后一次流转记录为null，则使用风险的创建时间
        if (null == projectWorkflowTransitionHistory)
        {
            lastDateTime = risk.getCreateTime();

            WorkflowNode workflowNode = null;
            if (PROJECT.equals(projectType))
            {
                workflowNode = projectWorkflowService.findProjectFirstNode(risk.getProjectId(), TypeClassify.RISK,
                        risk.getTypeCode());
            }
            else if (PROGRAM.equals(projectType))
            {
                workflowNode =
                        projectWorkflowService.findProjectProgramFirstNode(risk.getProgramId(), TypeClassify.RISK,
                                risk.getTypeCode());
            }
            if (workflowNode == null)
            {
                throw BizException.validFail("状态流转失败,原因:获取风险初始状态节点失败");
            }
            sourceNodeId = workflowNode.getId();
        }
        else
        {

            lastDateTime = projectWorkflowTransitionHistory.getCreateTime();
            sourceNodeId = projectWorkflowTransitionHistory.getTarget();
        }
        // 查询目标节点id
        WorkflowNode targetNode = workflowNodeMapper.selectOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, targetStateCode));
        if (null == targetNode)
        {
            throw BizException.validFail("状态流转失败,原因:获取风险流转到状态信息失败");
        }
        // 计算流转花费时间
        LocalDateTime now = LocalDateTime.now();
        long spendTime = ChronoUnit.MILLIS.between(lastDateTime, now);

        // 修改风险信息
        Risk updateRisk = Risk.builder()
                .id(riskId)
                .stateCode(targetStateCode)
                .build();
        if (WorkflowNodeType.END_NODE.eq(targetNode.getNodeType()))
        {
            updateRisk.setEndTime(now);
        }else if(WorkflowNodeType.INTERMEDIATE_NODE.eq(targetNode.getNodeType())&&risk.getStartTime()==null){
            updateRisk.setStartTime(now);
        }
        super.updateById(updateRisk);

        // 添加流转历史记录
        ProjectWorkflowTransitionHistory insertHistory = ProjectWorkflowTransitionHistory.builder().bizId(riskId)
                .typeCode(risk.getTypeCode())
                .source(sourceNodeId)
                .sourceStateCode(sourceStateCode)
                .target(targetNode.getId())
                .targetStateCode(targetStateCode)
                .spendTime(spendTime)
                .createTime(now)
                .build();

        projectWorkflowTransitionHistoryMapper.insert(insertHistory);

        // 推送 工作项改变事件
        IssueUpdateEvent.sendEvent(TypeClassify.RISK, risk.getId(),
                Bug.builder().id(risk.getId()).stateCode(sourceStateCode).build(),
                Bug.builder().id(risk.getId()).stateCode(targetStateCode).build()
        );

    }

    @Override
    public List<WorkflowNode> findNextNode(Long riskId, String projectType)
    {
        Risk risk = super.getById(riskId);
        if (projectType.equals(PROJECT) && null == risk.getProjectId())
        {
            throw BizException.validFail("当前风险未关联项目，无法查询流转状态");
        }
        else if (projectType.equals(PROGRAM) && null == risk.getProgramId())
        {
            throw BizException.validFail("当前风险未关联项目集，无法查询流转状态");
        }
        if (StrUtil.isEmpty(risk.getTypeCode()))
        {
            throw BizException.validFail("当前风险没有风险类型，无法查询流转状态");
        }
        // 查询该风险使用的工作流
        ProjectWorkflow projectWorkflow = null;
        if (PROJECT.equals(projectType))
        {
            projectWorkflow =
                    projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(risk.getProjectId(),
                            TypeClassify.RISK, risk.getTypeCode());
        }
        else if (PROGRAM.equals(projectType))
        {
            projectWorkflow =
                    projectWorkflowService.findProjectByProgramIdAndTypeClassifyAndTypeCode(risk.getProgramId(),
                            TypeClassify.RISK, risk.getTypeCode());
        }
        if (null == projectWorkflow || null == projectWorkflow.getWorkflowInfo())
        {
            throw BizException.validFail("获取流转状态失败,原因:获取风险使用工作流失败");
        }
        // 查询该风险的当前节点
        WorkflowNode thisWorkflowNode = workflowNodeMapper.selectOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, risk.getStateCode()));

        if (null == thisWorkflowNode)
        {
            throw BizException.validFail("获取当前风险工作流状态节点失败");
        }

        // 查询可流转到的节点
        return getTargetNodeIds(risk, projectWorkflow.getWorkflowId(), thisWorkflowNode.getId(), projectType);
    }

    @Override
    public List<Map<String, Object>> findRiskByProjectRiskExportQuery(RiskExportQuery model)
    {
         return baseMapper.findRiskByProjectRiskExportQuery(model);
    }

    /**
     * 获取流转到的节点
     *
     * @param risk 风险
     * @param workflowId 工作流id
     * @param thisNodeId 当前节点id
     * @param projectType 项目类型
     * @return List<Long> 目标节点id
     * <AUTHOR>
     * @date 2021/11/15 13:01
     * @update zxy 2021/11/15 13:01
     * @since 1.0
     */
    private List<WorkflowNode> getTargetNodeIds(Risk risk, Long workflowId, Long thisNodeId, String projectType)
    {
        List<WorkflowTransition> workflowTransitions = workflowTransitionMapper.selectList(
                Wraps.<WorkflowTransition>lbQ().eq(WorkflowTransition::getWorkflowId, workflowId)
                        .eq(WorkflowTransition::getSource, thisNodeId));

        if (workflowTransitions.isEmpty())
        {
            return Collections.emptyList();
        }
        boolean remove = true;
        Iterator<WorkflowTransition> iterator = workflowTransitions.iterator();
        while (iterator.hasNext())
        {
            WorkflowTransition workflowTransition = iterator.next();
            // 查询流转权限
            LbqWrapper<ProjectWorkflowAuthority> wrapper = Wraps.lbQ();
            wrapper.eq(ProjectWorkflowAuthority::getWorkflowId, workflowId)
                    .eq(ProjectWorkflowAuthority::getTypeClassify, TypeClassify.RISK)
                    .eq(ProjectWorkflowAuthority::getTypeCode, risk.getTypeCode())
                    .eq(ProjectWorkflowAuthority::getTransitionId, workflowTransition.getId());
            if (PROJECT.equals(projectType))
            {
                wrapper.eq(ProjectWorkflowAuthority::getProjectId, risk.getProjectId());
            }
            if (PROGRAM.equals(projectType))
            {
                wrapper.eq(ProjectWorkflowAuthority::getProgramId, risk.getProgramId());
            }
            List<ProjectWorkflowAuthority> projectWorkflowAuthorities =
                    projectWorkflowAuthorityMapper.selectList(wrapper);

            List<WorkflowAuthority> workflowAuthorities;
            if (projectWorkflowAuthorities.isEmpty())
            {
                workflowAuthorities = workflowAuthorityMapper.selectList(
                        Wraps.<WorkflowAuthority>lbQ()
                                .eq(WorkflowAuthority::getTransitionId, workflowTransition.getId()));
            }
            else
            {
                workflowAuthorities = BeanPlusUtil.toBeanList(projectWorkflowAuthorities, WorkflowAuthority.class);
            }
            boolean authorityBreak = false;
            for (WorkflowAuthority workflowAuthority : workflowAuthorities)
            {
                switch (workflowAuthority.getType())
                {
                    case HANDLER:
                        if (ContextUtil.getUserId().equals(risk.getHandleBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case RESPONSIBLE:
                        if (ContextUtil.getUserId().equals(risk.getLeadingBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case CREATOR:
                        if (ContextUtil.getUserId().equals(risk.getPutBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case USER:
                        if (ContextUtil.getUserId().equals(workflowAuthority.getUserOrRole()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case ROLE:
                        if (PROJECT.equals(projectType))
                        {
                            List<ProjectUserRole> userRoles =
                                    projectUserRoleMapper.selectList(
                                            Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getRoleId)
                                                    .eq(ProjectUserRole::getProjectId, risk.getProjectId())
                                                    .eq(ProjectUserRole::getUserId, ContextUtil.getUserId()));
                            Set<Long> roleIds =
                                    userRoles.stream().map(ProjectUserRole::getRoleId).collect(Collectors.toSet());
                            if (roleIds.contains(workflowAuthority.getUserOrRole()))
                            {
                                remove = false;
                                authorityBreak = true;
                            }
                        }
                        else if (PROGRAM.equals(projectType))
                        {
                            ProjectUser projectUser = projectUserMapper.selectOne(
                                    Wraps.<ProjectUser>lbQ().eq(ProjectUser::getProgramId, risk.getProgramId())
                                            .eq(ProjectUser::getUserId, ContextUtil.getUserId()));
                            if (null != projectUser &&
                                    projectUser.getUserId().equals(workflowAuthority.getUserOrRole()))
                            {
                                remove = false;
                                authorityBreak = true;
                            }
                        }

                        break;
                    default:
                        break;
                }
                if (authorityBreak)
                {
                    break;
                }
            }

            if (remove)
            {
                iterator.remove();
            }

        }
        if (workflowTransitions.isEmpty())
        {
            return Collections.emptyList();
        }
        return workflowTransitions.stream().map(transition -> {
            WorkflowNode workflowNode = workflowNodeMapper.selectById(transition.getTarget());

            List<ProjectWorkflowTransitionCheck> checkList = transitionCheckService.findCheckList(transition.getId(),
                    risk.getProjectId(), TypeClassify.RISK, risk.getTypeCode());
            workflowNode.getEchoMap().put("check", checkList);
            return workflowNode;
        }).collect(Collectors.toList());
    }

    @Override
    public Risk getById(Serializable id)
    {
        Risk risk = super.getById(id);
        if (risk != null)
        {
            risk.setFiles(fileApi.findByBizTypeAndBizId(FileBizType.RISK_FILE_UPLOAD, id));
        }

        return risk;
    }
    // 统一的发送风险通知方法（支持创建和变更场景）
    private void sendRiskNotifyMsg(Long targetUserId, Long creatorId, String description,
            String jumpString, Long riskId, String action, String title) {
        // 校验目标用户存在且不是创建人
        if (targetUserId == null || targetUserId.equals(creatorId)) {
            return;
        }

        // 构建通知内容（动态传入动作描述）
        String notifyContent = String.format(
                "%s(%s)%s一个%s风险【%s】",
                ContextUtil.getUserName(),
                ContextUtil.getUserAccount(),
                action,  // 区分"创建了"还是"变更了"
                description,
                jumpString
        );

        // 构建消息DTO（动态传入标题）
        MsgDTO msgDTO = MsgDTO.buildNotify(
                MsgBizType.RISK_NOTIFY,
                riskId,
                title,  // 区分"风险创建通知"还是"风险变更通知"
                notifyContent
        );
        msgDTO.setAuthorId(creatorId);

        // 发送消息
        Set<Long> userIds = Collections.singleton(targetUserId);
        MsgSaveDTO msgSaveDTO = MsgSaveDTO.builder()
                .userIdList(userIds)
                .msgDTO(msgDTO)
                .build();
        msgApi.save(msgSaveDTO);
    }
}
