package com.jettech.jettong.alm.issue.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.database.mybatis.conditions.query.QueryWrap;
import com.jettech.basic.database.mybatis.conditions.update.LbuWrapper;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.StrPool;
import com.jettech.jettong.alm.issue.dao.DetectionMapper;
import com.jettech.jettong.alm.issue.dto.DetectionSaveDTO;
import com.jettech.jettong.alm.issue.dto.DetectionUpdateDTO;
import com.jettech.jettong.alm.issue.dto.WorkItemDTO;
import com.jettech.jettong.alm.issue.dto.WorkItemQuery;
import com.jettech.jettong.alm.issue.entity.*;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.issue.service.*;
import com.jettech.jettong.alm.workinghours.entity.WorkingHoursInfo;
import com.jettech.jettong.alm.workinghours.service.WorkingHoursInfoService;
import com.jettech.jettong.base.api.FileApi;
import com.jettech.jettong.base.api.MsgApi;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.dto.msg.MsgDTO;
import com.jettech.jettong.base.dto.msg.MsgSaveDTO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.google.common.collect.Maps;
import com.jettech.jettong.common.constant.MsgBizType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.jettech.jettong.base.enumeration.file.FileBizType.DETECTION_FILE_UPLOAD;

/**
 * 评论 service
 *
 * <AUTHOR>
 * @version 1.0
 * @description 评论 service
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.controller.issue
 * @className DetectionServiceImpl
 * @date 2022/4/19 19:28
 * @copyright 2022 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class DetectionServiceImpl extends SuperServiceImpl<DetectionMapper, Detection>
        implements DetectionService {

    private final Map<TypeClassify, BiConsumer<DetectionMessageContent.ACTION, Detection>> TYPE_SEND_MESSAGE_MAP =
            Maps.newHashMapWithExpectedSize(5);
    private final MsgApi msgApi;

    @PostConstruct
    public void init() {
        TYPE_SEND_MESSAGE_MAP.put(TypeClassify.BUG, this::bugMessage);
        TYPE_SEND_MESSAGE_MAP.put(TypeClassify.ISSUE, this::issueMessage);
        TYPE_SEND_MESSAGE_MAP.put(TypeClassify.TESTREQ, this::testreqMessage);
        TYPE_SEND_MESSAGE_MAP.put(TypeClassify.TASK, this::taskMessage);
        TYPE_SEND_MESSAGE_MAP.put(TypeClassify.IDEA, this::ideaMessage);
        TYPE_SEND_MESSAGE_MAP.put(TypeClassify.RISK, this::riskMessage);
//        TYPE_SEND_MESSAGE_MAP.put(TypeClassify.WORK_ITEM, this::workItemMessage);
    }



    private final UserApi userApi;
    private final FileApi fileApi;

    private final BugService bugService;
    private final RequirementService requirementService;
    private final TaskService taskService;
    private final IdeaService ideaService;
    private final RiskService riskService;
    private final TestreqService testreqService;
    private final WorkingHoursInfoService workingHoursInfoService;

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        List<Detection> detections = baseMapper.selectBatchIds(ids);
        return detections.stream().collect(Collectors.toMap(Detection::getId, Function.identity()));
    }

    @Override
    public List<Detection> getBySourceTypeAndId(Long sourceId, TypeClassify sourceType) {
        LbqWrapper<Detection> wrapper = Wraps.lbQ();
        wrapper.eq(Detection::getSourceType, sourceType);
        wrapper.eq(Detection::getSourceId, sourceId);

        wrapper.orderByAsc(Detection::getCreateTime);

        List<Detection> list = this.list(wrapper);
        list.forEach(detection -> {
            List<File> files = fileApi.findByBizTypeAndBizId(DETECTION_FILE_UPLOAD, detection.getId());
            detection.setFiles(files);
            if (detection.getDeleted()) {
                detection.setDescription(null);
            }
        });
        return list;
    }

    @Override
    public Detection handlerSave(DetectionSaveDTO model) {
        Detection detection = BeanUtil.toBean(model, Detection.class);
        if (detection.getFromUser() == null) {
            Long userId = ContextUtil.getUserId();
            detection.setFromUser(userId);
        }
        this.save(detection);

        // 文件处理
        List<File> files = model.getFiles();
        if (CollUtil.isNotEmpty(files)) {
            files.forEach(file -> file.setBizType(DETECTION_FILE_UPLOAD).setBizId(detection.getId()));
            fileApi.updateBatchById(files);
        }

        noteUser(DetectionMessageContent.ACTION.ADD, detection);
        return detection;
    }

    @Override
    public Detection handlerUpdate(DetectionUpdateDTO model) {
        checkSelf(model.getId());
        Detection detection = BeanUtil.toBean(model, Detection.class);
        detection.setEdited(true);

        // 文件处理
        List<File> files = detection.getFiles();
        if (CollUtil.isNotEmpty(files)) {
            files.forEach(file -> file.setBizType(DETECTION_FILE_UPLOAD).setBizId(detection.getId()));
            fileApi.updateBatchById(files);
        }

        boolean update = this.updateById(detection);
        if (update) {
            noteUser(DetectionMessageContent.ACTION.EDIT, detection);
        }
        return detection;
    }

    @Override
    public boolean deleteById(Long id) {
        Detection detection = checkSelf(id);
        LbuWrapper<Detection> wrapper = Wraps.lbU();
        wrapper.set(Detection::getDeleted, true);
        wrapper.eq(Detection::getId, id);
        boolean delete = this.update(wrapper);
        if (delete) {
            noteUser(DetectionMessageContent.ACTION.DELETE, detection);
        }
        return delete;
    }

    @Override
    public List<WorkItemDTO> searchWorkItem(WorkItemQuery query) {
        List<WorkItemDTO> workItemDTOList = new ArrayList<>();

        List<TypeClassify> sourceTypes = query.getSourceTypes();
        if (CollUtil.isEmpty(sourceTypes)) {
            sourceTypes = Arrays.stream(TypeClassify.values()).collect(Collectors.toList());
            query.setSourceTypes(sourceTypes);
        }

        // 查询风险项
        if (sourceTypes.contains(TypeClassify.RISK)) {
            QueryWrap<Risk> wrap = getWrap(query, true);
            wrap.isNotNull("project_id");
            List<Risk> riskList = riskService.list(wrap);
            riskList.stream()
                    .map(r -> BeanUtil.toBean(r, WorkItemDTO.class))
                    .peek(w -> w.setSourceType(TypeClassify.RISK))
                    .forEach(workItemDTOList::add);
        }

        // 查询 需求
        if (sourceTypes.contains(TypeClassify.ISSUE)) {
            List<Requirement> requirementList = requirementService.list(getWrap(query, true));
            requirementList.stream()
                    .map(r -> BeanUtil.toBean(r, WorkItemDTO.class))
                    .peek(w -> w.setSourceType(TypeClassify.ISSUE))
                    .forEach(workItemDTOList::add);
        }


        // 查询 任务
        if (sourceTypes.contains(TypeClassify.TASK)) {
            List<Task> taskList = taskService.list(getWrap(query, true));

            List<Long> collect = taskList.stream().map(Task::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                //查询已经填报工时
                List<WorkingHoursInfo> workingHoursInfos = workingHoursInfoService.list(Wraps.<WorkingHoursInfo>lbQ()
                        .in(WorkingHoursInfo::getBizId, collect)
                        .eq(WorkingHoursInfo::getType, "TASK")
                        .ne(WorkingHoursInfo::getValid,false));
                //计算出任务工时
                Map<Long, Double> workingHoursInfoMap = workingHoursInfos.stream()
                        .collect(Collectors.groupingBy(WorkingHoursInfo::getBizId,
                                Collectors.summingDouble(WorkingHoursInfo::getDuration)));
                List<WorkItemDTO> peek = taskList.stream()
                        .map(r -> BeanUtil.toBean(r, WorkItemDTO.class))
                        .peek(w -> {
                            w.setSourceType(TypeClassify.TASK);
                            if(workingHoursInfoMap.get(w.getId())!=null){
                                w.setIsFillInHour(w.getEstimateHour() - workingHoursInfoMap.get(w.getId()));
                            }else {
                                w.setIsFillInHour(w.getEstimateHour());
                            }
                        }).collect(Collectors.toList());
                workItemDTOList.addAll(peek);
            }
        }

        // 查询 用户需求
        if (sourceTypes.contains(TypeClassify.IDEA)) {
            List<Idea> ideaList = ideaService.list(getWrap(query, false));
            ideaList.stream()
                    .map(r -> BeanUtil.toBean(r, WorkItemDTO.class))
                    .peek(w -> w.setSourceType(TypeClassify.IDEA))
                    .forEach(workItemDTOList::add);
        }

        // 查询 缺陷
        if (sourceTypes.contains(TypeClassify.BUG)) {
            List<Bug> bugList = bugService.list(getWrap(query, true));
            bugList.stream()
                    .map(r -> BeanUtil.toBean(r, WorkItemDTO.class))
                    .peek(w -> w.setSourceType(TypeClassify.BUG))
                    .forEach(workItemDTOList::add);
        }

        return workItemDTOList;
    }

    private <T> QueryWrap<T> getWrap(WorkItemQuery query, boolean project) {
        String search = query.getSearch();
        Integer limit = Optional.ofNullable(query.getLimit()).orElse(10);
        QueryWrap<T> queryWrap = Wraps.q();

        queryWrap.like("code", query.getCode());
        queryWrap.like("name", query.getName());
        queryWrap.eq("type_code", query.getTypeCode());
        queryWrap.eq("state_code", query.getStateCode());
        queryWrap.eq("leading_by", query.getLeadingBy());
        queryWrap.eq("priority_code", query.getPriorityCode());
        queryWrap.eq("handle_by", query.getHandleBy());
        //处理进度 0未开始 0-100进行中 100已完成
        if ((query.getSourceTypes().contains(TypeClassify.TASK)||query.getSourceTypes().contains(TypeClassify.BUG)||query.getSourceTypes().contains(TypeClassify.ISSUE))){
            if (StrUtil.isNotEmpty(query.getRateProgress())){
                List<String> statusList = StrUtil.split(query.getRateProgress(), StrPool.COMMA);
                if (statusList.contains("0")&&statusList.contains("100")&&statusList.contains("0-100")){

                }else if (statusList.contains("0")&&statusList.contains("100"))
                {
                    queryWrap.and(i -> i.eq("rate_progress", 0).or().eq("rate_progress", 100));
                }else if (statusList.contains("0")&&statusList.contains("0-100"))
                {
                    queryWrap.lt("rate_progress", 100);
                }else if (statusList.contains("100")&&statusList.contains("0-100"))
                {
                    queryWrap.gt("rate_progress", 0);
                }else if (statusList.contains("0")){
                    queryWrap.eq("rate_progress", 0);
                }else if (statusList.contains("100")){
                    queryWrap.eq("rate_progress", 100);
                } else if (statusList.contains("0-100")){
                    queryWrap.gt("rate_progress", 0);
                    queryWrap.lt("rate_progress", 100);
                }
            }
        }
        if (project) {
            queryWrap.eq(query.getProjectId()!=null,"project_id", query.getProjectId());
            //queryWrap.select("id", "code", "name", "type_code", "project_id");
        }
//        else {
//            queryWrap.select("id", "code", "name", "type_code");
//        }

        queryWrap.and(StrUtil.isNotBlank(search),
                w -> w.like("code", search).or().like("name", search));
        queryWrap.orderByDesc("create_time");
        if (limit> 0){
            queryWrap.last("limit " + limit);
        }
        return queryWrap;
    }

    /**
     * 校验权限
     * 仅能自己对自己的评论进行删除、修改操作
     *
     * @param detectionId 评论ID
     */
    private Detection checkSelf(Long detectionId) {
        Long currentUser = ContextUtil.getUserId();
        Detection detection = this.getById(detectionId);
        Long createdBy = detection.getCreatedBy();
        Long fromUser = detection.getFromUser();
        if (ObjectUtil.notEqual(fromUser, currentUser) && ObjectUtil.notEqual(createdBy, currentUser)) {
            throw BizException.wrap(401, "仅能自己对自己的评论进行删除、修改操作");
        }
        return detection;
    }

    /**
     * 通知对应的用户
     *
     * @param detection
     * @return
     */
    private void noteUser(DetectionMessageContent.ACTION action, Detection detection) {
        TypeClassify sourceType = detection.getSourceType();
        BiConsumer<DetectionMessageContent.ACTION, Detection> consumer = TYPE_SEND_MESSAGE_MAP.get(sourceType);
        // 执行 发送消息事件
        consumer.accept(action, detection);

    }

    private void riskMessage(DetectionMessageContent.ACTION action, Detection detection) {
        Long sourceId = detection.getSourceId();
        TypeClassify sourceType = detection.getSourceType();
        Long fromUserId = detection.getFromUser();
        User user = userApi.findUserById(fromUserId);
        Risk risk = riskService.getById(sourceId);

        if (user == null || risk == null) {
            log.warn("note user fail, beacause user or source is null. userId:{}, sourceType:{}, sourceId:{}",
                    fromUserId, sourceType, sourceId);
            return;
        }

        List<Long> relateds = composeRelateds(risk.getPutBy(), risk.getLeadingBy(), risk.getHandleBy());

        // 构建前端用来跳转的特殊字符串
        String jumpString = DetectionMessageContent.buildJumpString(risk.getCode(), risk.getName(),
                risk.getId(), TypeClassify.RISK, risk.getProjectId(),risk.getTypeCode());
        sendMessage(action, detection, sourceType, user, jumpString, relateds,BeanUtil.toBean(risk,WorkItemDTO.class));

    }

    private void ideaMessage(DetectionMessageContent.ACTION action, Detection detection) {
        Long sourceId = detection.getSourceId();
        TypeClassify sourceType = detection.getSourceType();
        Long fromUserId = detection.getFromUser();
        User user = userApi.findUserById(fromUserId);
        Idea idea = ideaService.getById(sourceId);

        if (user == null || idea == null) {
            log.warn("note user fail, beacause user or source is null. userId:{}, sourceType:{}, sourceId:{}",
                    fromUserId, sourceType, sourceId);
            return;
        }

        List<Long> relateds = composeRelateds(idea.getPutBy(), idea.getLeadingBy(), idea.getHandleBy());

        // 构建前端用来跳转的特殊字符串
        String jumpString = DetectionMessageContent.buildJumpString(idea.getCode(), idea.getName(),
                idea.getId(), TypeClassify.IDEA, null,idea.getTypeCode());
        sendMessage(action, detection, sourceType, user, jumpString, relateds,BeanUtil.toBean(idea,WorkItemDTO.class));

    }

    private void bugMessage(DetectionMessageContent.ACTION action, Detection detection) {
        Long sourceId = detection.getSourceId();
        TypeClassify sourceType = detection.getSourceType();
        Long fromUserId = detection.getFromUser();
        User user = userApi.findUserById(fromUserId);
        Bug bug = bugService.getById(sourceId);

        if (user == null || bug == null) {
            log.warn("note user fail, beacause user or source is null. userId:{}, sourceType:{}, sourceId:{}",
                    fromUserId, sourceType, sourceId);
            return;
        }

        List<Long> bugRelateds = composeRelateds(bug.getPutBy(), bug.getLeadingBy(), bug.getHandleBy());
        // 构建前端用来跳转的特殊字符串
        String jumpString = DetectionMessageContent.buildJumpString(bug.getCode(), bug.getName(),
                bug.getId(), TypeClassify.BUG, bug.getProjectId(),bug.getTypeCode());

        sendMessage(action, detection, sourceType, user, jumpString, bugRelateds,BeanUtil.toBean(bug,WorkItemDTO.class));
    }

    private void issueMessage(DetectionMessageContent.ACTION action, Detection detection) {
        Long sourceId = detection.getSourceId();
        TypeClassify sourceType = detection.getSourceType();
        User user = userApi.findUserById(detection.getFromUser());
        Requirement requirement = requirementService.getById(sourceId);
        if (user == null || requirement == null) {
            log.warn("note user fail, beacause user or source is null. userId:{}, sourceType:{}, sourceId:{}",
                    detection.getFromUser(), sourceType, sourceId);
            return;
        }
        List<Long> relateds =
                composeRelateds(requirement.getPutBy(), requirement.getLeadingBy(), requirement.getHandleBy());
        // 构建前端用来跳转的特殊字符串
        String jumpString = DetectionMessageContent.buildJumpString(requirement.getCode(), requirement.getName(),
                requirement.getId(), TypeClassify.ISSUE, requirement.getProjectId(),requirement.getTypeCode());

        sendMessage(action, detection, sourceType, user, jumpString, relateds,BeanUtil.toBean(requirement,WorkItemDTO.class));

    }
    private void testreqMessage(DetectionMessageContent.ACTION action, Detection detection) {
        Long sourceId = detection.getSourceId();
        TypeClassify sourceType = detection.getSourceType();
        User user = userApi.findUserById(detection.getFromUser());
        Testreq testreq = testreqService.getById(sourceId);
        if (user == null || testreq == null) {
            log.warn("note user fail, beacause user or source is null. userId:{}, sourceType:{}, sourceId:{}",
                    detection.getFromUser(), sourceType, sourceId);
            return;
        }
        List<Long> relateds =
                composeRelateds(testreq.getPutBy(), testreq.getLeadingBy(), testreq.getHandleBy());
        // 构建前端用来跳转的特殊字符串
        String jumpString = DetectionMessageContent.buildJumpString(testreq.getCode(), testreq.getName(),
                testreq.getId(), TypeClassify.TESTREQ, testreq.getProjectId(),testreq.getTypeCode());

        sendMessage(action, detection, sourceType, user, jumpString, relateds,BeanUtil.toBean(testreq,WorkItemDTO.class));

    }

    private void taskMessage(DetectionMessageContent.ACTION action, Detection detection) {
        Long sourceId = detection.getSourceId();
        TypeClassify sourceType = detection.getSourceType();
        Task task = taskService.getById(sourceId);
        User user = userApi.findUserById(detection.getFromUser());
        if (user == null || task == null) {
            log.warn("note user fail, beacause user or source is null. userId:{}, sourceType:{}, sourceId:{}",
                    detection.getFromUser(), sourceType, sourceId);
            return;
        }
        List<Long> taskRelateds = composeRelateds(task.getCreatedBy(), task.getLeadingBy(), task.getHandleBy());
        // 构建前端用来跳转的特殊字符串
        String jumpString = DetectionMessageContent.buildJumpString(task.getCode(), task.getName(), task.getId(),
                TypeClassify.TASK, task.getProjectId(),task.getTypeCode());

        sendMessage(action, detection, sourceType, user, jumpString, taskRelateds,BeanUtil.toBean(task,WorkItemDTO.class));
    }

    private List<Long> composeRelateds(Long putBy, Long leadingBy, Long handleBy) {
        // 相关人员
        Set<Long> relateds = new HashSet<>();
        Optional.ofNullable(putBy).ifPresent(relateds::add);
        Optional.ofNullable(leadingBy).ifPresent(relateds::add);
        Optional.ofNullable(handleBy).ifPresent(relateds::add);

        return new ArrayList<>(relateds);
    }

    /**
     * 区分类型规则:
     * 1.自己均不会接受到消息通知
     * 2.被回复人 > 被@人 > 相关人员
     * 3.同一个人在同一条评论中只会被通知一次
     */
    private Map<Long, DetectionMessageContent> differentiateUser(DetectionMessageContent.ACTION action,
            Detection detection, List<Long> relateds) {
        HashMap<Long, DetectionMessageContent> userTypeMap = new HashMap<>();
        DetectionNoticeUser noticeUsers = detection.getNoticeUsers();
        if (noticeUsers == null) {
            noticeUsers = new DetectionNoticeUser();
        }
        List<Long> mentioneds = noticeUsers.getMentioneds();
        if (mentioneds == null) {
            mentioneds = new ArrayList<>();
        }

        Long fromUser = detection.getFromUser();
        Long toUser = detection.getToUser();

        // 自己均不会接受到消息通知
        mentioneds.remove(fromUser);
        relateds.remove(fromUser);

        // 被回复人
        if (toUser != null) {
            mentioneds.remove(toUser);
            DetectionMessageContent messageBody =
                    DetectionMessageContent.create(action, DetectionMessageContent.TYPE.REPLIED);
            userTypeMap.put(toUser, messageBody);
        }


        // 不通知相关人员
        if (!noticeUsers.getAllRelated()) {
            mentioneds.forEach(m -> userTypeMap.put(m, DetectionMessageContent.create(action, DetectionMessageContent.TYPE.MENTIONED)));
            return userTypeMap;
        }

        // 通知相关人员，并在相关人员中去掉被@的人员
        mentioneds.forEach(m -> {
            DetectionMessageContent messageBody =
                    DetectionMessageContent.create(action, DetectionMessageContent.TYPE.MENTIONED);
            userTypeMap.put(m, messageBody);
            relateds.remove(m);
        });

        relateds.forEach(r -> userTypeMap.put(r, DetectionMessageContent.create(action, DetectionMessageContent.TYPE.COMMENTED)));

        return userTypeMap;
    }
    private void sendMessage(DetectionMessageContent.ACTION action, Detection detection, TypeClassify sourceType,
            User user, String sourceName, List<Long> relateds,WorkItemDTO workItemDTO)
    {
        Map<Long, DetectionMessageContent> userTypeMap = differentiateUser(action, detection, relateds);
        userTypeMap.forEach((notedUserId, body) ->
        {
            String content = body.formatBody(user.getName(), sourceType, sourceName);
            String title = body.title();
            MsgDTO msgDTO = MsgDTO.buildNotify(MsgBizType.DETECTION_NOTIFY, detection.getId(), title, content);

            msgApi.save(MsgSaveDTO.buildPersonal(msgDTO, notedUserId));
        });
    }
}
