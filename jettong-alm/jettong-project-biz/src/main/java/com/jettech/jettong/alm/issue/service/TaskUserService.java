package com.jettech.jettong.alm.issue.service;

import com.jettech.basic.base.service.SuperService;
import com.jettech.jettong.alm.issue.entity.Task;
import com.jettech.jettong.alm.issue.entity.TaskUser;

import java.util.List;
import java.util.Map;

/**
 * 任务和人员关联业务接口
 * <AUTHOR>
 * @version 1.0
 * @description 任务和人员关联业务接口
 * @projectName jettong
 * @package com.jettech.jettong.workflow.service
 * @className TaskUserService
 * @date 2025-09-15
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface TaskUserService extends SuperService<TaskUser>
{


    List<Long> findUserIdsByTaskId(Long taskId);

    /**
     * 根据任务id查询处理人员信息
     * @param task
     * @return {@link List< TaskUser>}
     * @throws
     * <AUTHOR> @date 2025/9/15 19:52
     * @update  2025/9/15 19:52
     * @since 1.0
     */
    List<TaskUser> actionHandleBy(Task task);
    /**
     * 根据任务id查询处理人员信息
     * @param tasks
     * @return {@link Map< Long, List< TaskUser>>} 《任务id,任务人员列表》
     * @throws
     * <AUTHOR> @date 2025/9/15 19:52
     * @update  2025/9/15 19:52
     * @since 1.0
     */
    Map<Long,List<TaskUser>> actionHandleBy(List<Task> tasks);

    /**
     * 只补充处理人id，不补充处理人信息
     * @param tasks
     * @return
     * @throws
     * <AUTHOR>
     * @date 2025/9/16 17:11
     * @update  2025/9/16 17:11
     * @since 1.0
     */
    void actionHandleById(List<Task> tasks);
}
