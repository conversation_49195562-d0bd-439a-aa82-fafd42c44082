package com.jettech.jettong.alm.msg;

import cn.hutool.extra.spring.SpringUtil;
import com.jettech.jettong.alm.issue.entity.Task;
import com.jettech.jettong.alm.issue.service.TaskUserService;
import com.jettech.jettong.common.event.AbstractMsgEventFactory;
import com.jettech.jettong.common.event.enumeration.MsgEventType;
import com.jettech.jettong.common.event.enumeration.MsgReceiveType;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;

/**
 * 意向消息事件工厂
 *
 * <AUTHOR>
 * @version 1.0
 * @description 意向消息事件工厂
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.msg
 * @className IdeaMsgEventFactory
 * @date 2023/6/2 17:38
 * @copyright 2023 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */

public abstract class TaskMsgEventFactory extends AbstractMsgEventFactory<Task> {

    public TaskMsgEventFactory(MsgEventType type) {
        super(type);
    }

    @Override
    public Map<MsgReceiveType, Function<Task, List<Long>>> getReceiveFunctionMap() {

        HashMap<MsgReceiveType, Function<Task, List<Long>>> funcMap = new HashMap<>();
        funcMap.put(MsgReceiveType.createdBy, toList(Task::getCreatedBy));
        funcMap.put(MsgReceiveType.updatedBy, toList(Task::getUpdatedBy));
        funcMap.put(MsgReceiveType.leadingBy, toList(Task::getLeadingBy));
//        funcMap.put(MsgReceiveType.putBy, toList(Task::getPutBy));
        funcMap.put(MsgReceiveType.handleBy, task -> {
            // 1. 从Task对象中获取任务ID
            Long taskId = task.getId();
            // 2. 调用TaskUserService查询处理人ID列表
            return SpringUtil.getBean(TaskUserService.class).findUserIdsByTaskId(taskId);
        });
        return funcMap;
    }

    private Function<Task, List<Long>> toList(Function<Task, Long> function) {

        return idea ->  {
            if (idea == null) {
                return new ArrayList<>();
            }
            List<Long> ids = new ArrayList<>();
            Long id = function.apply(idea);
            if (id != null) {
                ids.add(id);
            }
            return ids;
        };
    }

    @Service
    public static class Create extends TaskMsgEventFactory {
        public Create() {
            super(MsgEventType.TASK_CREATE);
        }
    }

    @Service
    public static class Update extends TaskMsgEventFactory {
        public Update() {
            super(MsgEventType.TASK_UPDATE);
        }
    }

    @Service
    public static class Delete extends TaskMsgEventFactory {
        public Delete() {
            super(MsgEventType.TASK_DELETE);
        }
    }

    @Service
    public static class Transition extends TaskMsgEventFactory {
        public Transition() {
            super(MsgEventType.TASK_TRANSITION);
        }
    }


}
