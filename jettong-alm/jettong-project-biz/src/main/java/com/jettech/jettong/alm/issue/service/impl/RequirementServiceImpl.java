package com.jettech.jettong.alm.issue.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.repository.CacheOps;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.CollHelper;
import com.jettech.basic.utils.StringUtil;
import com.jettech.jettong.alm.issue.dao.PriorityMapper;
import com.jettech.jettong.alm.issue.dao.RequirementMapper;
import com.jettech.jettong.alm.issue.dao.StateMapper;
import com.jettech.jettong.alm.issue.dto.RequirementCorrelatedIdeaDTO;

import com.jettech.jettong.alm.issue.entity.*;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;

import com.jettech.jettong.alm.issue.service.*;
import com.jettech.jettong.alm.issue.service.event.IssueUpdateEvent;
import com.jettech.jettong.alm.issue.vo.*;
import com.jettech.jettong.alm.project.dao.*;
import com.jettech.jettong.alm.project.entity.*;
import com.jettech.jettong.alm.project.service.ProjectPlanService;
import com.jettech.jettong.alm.project.service.ProjectRoleService;
import com.jettech.jettong.alm.project.service.ProjectWorkflowService;
import com.jettech.jettong.alm.project.service.ProjectWorkflowTransitionCheckService;
import com.jettech.jettong.alm.redis.base.ApplicationEngineRedis;
import com.jettech.jettong.alm.workflow.dao.WorkflowAuthorityMapper;
import com.jettech.jettong.alm.workflow.dao.WorkflowNodeMapper;
import com.jettech.jettong.alm.workflow.dao.WorkflowTransitionMapper;
import com.jettech.jettong.alm.workflow.entity.WorkflowAuthority;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;
import com.jettech.jettong.alm.workflow.entity.WorkflowTransition;
import com.jettech.jettong.alm.workflow.enumeration.WorkflowAuthorityType;
import com.jettech.jettong.alm.workflow.enumeration.WorkflowNodeType;
import com.jettech.jettong.base.api.FileApi;
import com.jettech.jettong.base.api.MsgApi;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.dto.msg.MsgDTO;
import com.jettech.jettong.base.dto.msg.MsgSaveDTO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.engine.Engine;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.cmdb.application.vo.ApplicationEngineQuery;
import com.jettech.jettong.common.cache.alm.TypeCodePrefixCacheKeyBuilder;
import com.jettech.jettong.common.constant.MsgBizType;
import com.jettech.jettong.common.enumeration.EngineInstance;
import com.jettech.jettong.product.api.ProductApplicationApi;
import com.jettech.jettong.product.api.ProductModuleFunctionApi;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.api.TestPlanApi;
import com.jettech.jettong.testm.entity.TestPlan;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.jettech.jettong.alm.project.service.impl.ProjectMenuServiceImpl.PM_CODE;

/**
 * 需求信息业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 需求信息业务层
 * @projectName jettong
 * @package com.jettech.jettong.alm.issue.service.impl
 * @className RequirementServiceImpl
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class RequirementServiceImpl extends SuperServiceImpl<RequirementMapper, Requirement> implements
        RequirementService
{
    private final static String ADD_KEY = "ADD";

    private final CacheOps cacheOps;
    private final TypeService typeService;
    private final ProjectWorkflowService projectWorkflowService;
    private final WorkflowAuthorityMapper workflowAuthorityMapper;
    private final ProjectWorkflowAuthorityMapper projectWorkflowAuthorityMapper;
    private final WorkflowTransitionMapper workflowTransitionMapper;
    private final WorkflowNodeMapper workflowNodeMapper;
    private final ProjectUserRoleMapper projectUserRoleMapper;
    private final ProjectWorkflowTransitionHistoryMapper projectWorkflowTransitionHistoryMapper;
    private final StateMapper stateMapper;
    private final ProjectPlanService projectPlanService;
    private final TaskService taskService;
    private final BugService bugService;
    private final ProductApplicationApi productApplicationApi;
    private final TestPlanApi testPlanApi;
    private final ApplicationEngineRedis applicationEngineRedis;
    private final ProjectInfoMapper projectInfoMapper;
    private final FileApi fileApi;
    private final IssueIdeaProductRequirementService ideaRequirementService;
    private static final String GROUP_NAME="未定义";
    private final String creatorInfo = buildUserString();
    private final PriorityMapper priorityMapper;
    private final UserApi userApi;
    private final MsgApi msgApi;
    private final ProductModuleFunctionApi productModuleFunctionApi;

    private final ProjectWorkflowTransitionCheckService transitionCheckService;
    private final ProjectRoleService projectRoleService;

    @Override
    public boolean saveBatch(Collection<Requirement> entityList, int batchSize) {
        boolean saveBatch = super.saveBatch(entityList, batchSize);

        // 推送 工作项改变事件
        entityList.forEach(e -> IssueUpdateEvent.sendEvent(TypeClassify.ISSUE, e.getId(), null, e));
        return saveBatch;
    }

    @Override
    public boolean save(Requirement requirement)
    {
        Type type = typeService.findByCode(requirement.getTypeCode());

        if (type == null)
        {
            throw BizException.validFail("获取事项类型失败,请联系管理员");
        }

        // 自动生成一个code
        requirement.setCode(typeService.getCode(type));

        // 查询需求的初始化状态
        WorkflowNode workflowNode =
                projectWorkflowService.findProjectFirstNode(requirement.getProjectId(), TypeClassify.ISSUE,
                        requirement.getTypeCode());
        if (null == workflowNode)
        {
            throw BizException.validFail("获取该需求类型工作流失败，请联系管理员");
        }
        requirement.setStateCode(workflowNode.getStateCode());
        requirement.setRateProgress(workflowNode.getProgress());

        // 查询项目信息
        if (requirement.getProjectId() != null)
        {
            ProjectInfo projectInfo = projectInfoMapper.selectById(requirement.getProjectId());
            if (projectInfo == null)
            {
                throw BizException.validFail("创建失败,原因:获取项目信息失败");
            }

            requirement.setProgramId(projectInfo.getProgramId());
        }
        boolean add = super.save(requirement);

        // 推送 工作项改变事件
        IssueUpdateEvent.sendEvent(TypeClassify.ISSUE, requirement.getId(), null, requirement);

        if (add)
        {
            // 修改附件
            List<File> files = requirement.getFiles();
            if (files != null && !files.isEmpty())
            {
                files.forEach(item -> item.setBizType(FileBizType.ISSUE_FILE_UPLOAD).setBizId(requirement.getId()));

                fileApi.updateBatchById(files);
            }

        }
        // 场景1：软件需求创建时发送通知
        String createJumpStr = buildJumpString(requirement.getCode(), requirement.getName(), requirement.getId(), requirement.getProjectId(),requirement.getTypeCode());

        // 给负责人发送通知
        sendNotification(
                requirement.getLeadingBy(),
                requirement.getId(),
                "软件需求创建通知",
                String.format("%s创建了一个您负责的软件需求【%s】", creatorInfo, createJumpStr)
        );

        // 给处理人发送通知
        sendNotification(
                requirement.getHandleBy(),
                requirement.getId(),
                "软件需求创建通知",
                String.format("%s创建了一个您处理的软件需求【%s】", creatorInfo, createJumpStr)
        );

        return add;
    }

    /**
     * 获取需求的Code，从redis中获取
     *
     * @param type 工作项类型
     * @return String code
     * <AUTHOR>
     * @date 2021/11/12 9:42
     * @update zxy 2021/11/12 9:42
     * @since 1.0
     */
    private synchronized String getCode(Type type)
    {
        String prefix = type.getPrefix();
        CacheKey cacheKey = new TypeCodePrefixCacheKeyBuilder().key(prefix);
        Long codeNum = cacheOps.get(cacheKey, key -> {
            // 获取前缀相同的类型，防止相同前缀但不同类型的工作项 导致获取到值不是最大的bug
            Set<String> typeCodeSet = typeService.list(Wraps.<Type>lbQ().eq(Type::getPrefix, prefix))
                    .stream().map(Type::getCode).collect(Collectors.toSet());
            // 根据时间排序，获取最新一条数据的code
            String code = super.getObj(
                    Wraps.<Requirement>lbQ().select(Requirement::getCode)
                            .in(Requirement::getTypeCode, typeCodeSet)
                            .likeRight(Requirement::getCode, prefix)
                            .orderByDesc(Requirement::getCreateTime)
                            .last(" limit 1 "),
                    Convert::toStr);
            return StringUtil.getLongToStr(StringUtil.removePrefix(code, prefix));
        });
        codeNum++;
        cacheOps.set(cacheKey, codeNum);
        return prefix + codeNum;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<? extends Serializable> ids)
    {
        // 删除附件信息
        fileApi.removeByFileBizTypeAndBizIds(FileBizType.ISSUE_FILE_UPLOAD, ids);

        //删除状态流转历史
        projectWorkflowTransitionHistoryMapper.delete(Wraps.<ProjectWorkflowTransitionHistory>lbQ()
                .in(ProjectWorkflowTransitionHistory::getBizId, ids));

        boolean remove = super.removeByIds(ids);
        // 推送 工作项改变事件
        ids.forEach(id -> IssueUpdateEvent.sendEvent(TypeClassify.ISSUE, (Long) id, null, null));

        return remove;
    }

    @Override
    public boolean updateAllById(Requirement requirement)
    {
        Requirement oldRequirement = super.getById(requirement.getId());

        if (oldRequirement == null)
        {
            throw BizException.validFail("修改失败,原因:未查询到该需求");
        }

        List<File> oldFiles = fileApi.findByBizTypeAndBizId(FileBizType.ISSUE_FILE_UPLOAD, requirement.getId());

        List<Long> oldFileIds = oldFiles.stream().map(File::getId).collect(Collectors.toList());

        List<File> newFiles = requirement.getFiles();

        if (newFiles == null || newFiles.isEmpty())
        {
            if (!oldFileIds.isEmpty())
            {
                fileApi.removeByFileBizTypeAndBizIds(FileBizType.ISSUE_FILE_UPLOAD, oldFileIds);
            }
        }
        else
        {
            List<Long> newFileIds = newFiles.stream().map(File::getId).collect(Collectors.toList());

            if (!oldFileIds.isEmpty())
            {
                // 筛选出需要删除的数据
                List<Long> deleteFileIds =
                        oldFileIds.stream().filter(item -> !newFileIds.contains(item)).collect(Collectors.toList());
                if (!deleteFileIds.isEmpty())
                {
                    fileApi.removeByFileBizTypeAndBizIds(FileBizType.ISSUE_FILE_UPLOAD, deleteFileIds);
                }

            }
            newFiles.forEach(item -> item.setBizId(requirement.getId()).setBizType(FileBizType.ISSUE_FILE_UPLOAD));
            fileApi.updateBatchById(newFiles);
        }

        requirement.setCode(oldRequirement.getCode());
        requirement.setTypeCode(oldRequirement.getTypeCode());
        if (!requirement.getStateCode().equals(oldRequirement.getStateCode()))
        {
            transitionState(requirement.getId(), oldRequirement.getStateCode(), requirement.getStateCode());
        }
        else
        {
            requirement.setRateProgress(oldRequirement.getRateProgress());
            requirement.setEndTime(oldRequirement.getEndTime());
        }

        requirement.setCreateTime(oldRequirement.getCreateTime());
        requirement.setCreatedBy(oldRequirement.getCreatedBy());
        requirement.setUpdateTime(LocalDateTime.now());
        requirement.setUpdatedBy(ContextUtil.getUserId());
        boolean update = super.updateAllById(requirement);

        // 推送 工作项改变事件
        oldRequirement.setFiles(oldFiles);
        if (!requirement.getStateCode().equals(oldRequirement.getStateCode()))
        {
            IssueUpdateEvent.sendEvent(TypeClassify.ISSUE, requirement.getId(), oldRequirement, requirement);
        }
        // 场景2：软件需求变更时发送通知
        String updateJumpStr = buildJumpString(oldRequirement.getCode(), requirement.getName(), requirement.getId(), requirement.getProjectId(),requirement.getTypeCode());

        // 给负责人发送通知
        sendNotification(
                requirement.getLeadingBy(),
                requirement.getId(),
                "软件需求变更通知",
                String.format("%s变更了一个您负责的软件需求【%s】", creatorInfo, updateJumpStr)
        );

        // 给处理人发送通知
        sendNotification(
                requirement.getHandleBy(),
                requirement.getId(),
                "软件需求变更通知",
                String.format("%s变更了一个您处理的软件需求【%s】", creatorInfo, updateJumpStr)
        );

        return update;
    }

    @Override
    public void transitionState(Long requirementId, String sourceStateCode, String targetStateCode)
    {
        // 查询当前需求节点id
        Requirement requirement = super.getById(requirementId);
        if (null == requirement)
        {
            throw BizException.validFail("状态流转失败,原因:未查询到需求信息");
        }
        if (!requirement.getStateCode().equals(sourceStateCode))
        {
            throw BizException.validFail("状态流转失败,原因:需求状态已更新,请刷新重试");
        }

        // 查询需求使用的工作流
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(requirement.getProjectId(),
                        TypeClassify.ISSUE,
                        requirement.getTypeCode());
        if (null == projectWorkflow || null == projectWorkflow.getWorkflowInfo())
        {
            throw BizException.validFail("状态流转失败,原因:获取需求使用工作流失败");
        }

        // 获取最后一次流转记录
        ProjectWorkflowTransitionHistory projectWorkflowTransitionHistory =
                projectWorkflowTransitionHistoryMapper.selectOne(Wraps.<ProjectWorkflowTransitionHistory>lbQ()
                        .eq(ProjectWorkflowTransitionHistory::getBizId, requirementId)
                        .eq(ProjectWorkflowTransitionHistory::getTypeCode, requirement.getTypeCode()).orderByDesc(
                                ProjectWorkflowTransitionHistory::getCreateTime).last(" limit 1"));

        Long sourceNodeId;
        LocalDateTime lastDateTime;
        // 如果最后一次流转记录为null，则使用需求的创建时间
        if (null == projectWorkflowTransitionHistory)
        {
            lastDateTime = requirement.getCreateTime();

            WorkflowNode workflowNode =
                    projectWorkflowService.findProjectFirstNode(requirement.getProjectId(), TypeClassify.ISSUE,
                            requirement.getTypeCode());
            if (workflowNode == null)
            {
                throw BizException.validFail("状态流转失败,原因:获取需求初始状态节点失败");
            }
            sourceNodeId = workflowNode.getId();
        }
        else
        {

            lastDateTime = projectWorkflowTransitionHistory.getCreateTime();
            sourceNodeId = projectWorkflowTransitionHistory.getTarget();
        }
        // 查询目标节点id
        WorkflowNode targetNode = workflowNodeMapper.selectOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, targetStateCode));
        if (null == targetNode)
        {
            throw BizException.validFail("状态流转失败,原因:获取需求流转到状态信息失败");
        }
        // 计算流转花费时间
        LocalDateTime now = LocalDateTime.now();
        long spendTime = ChronoUnit.MILLIS.between(lastDateTime, now);

        // 修改需求信息
        Requirement updateRequirement = Requirement.builder()
                .id(requirementId)
                .stateCode(targetStateCode)
                .rateProgress(targetNode.getProgress())
                .build();
        if (WorkflowNodeType.END_NODE.eq(targetNode.getNodeType()))
        {
            updateRequirement.setEndTime(now);
            updateRequirement.setDelay(requirement.getPlanEtime() != null && now.isAfter(requirement.getPlanEtime()));
        }
        else if(WorkflowNodeType.INTERMEDIATE_NODE.eq(targetNode.getNodeType()) && requirement.getStartTime()==null)
        {
            updateRequirement.setStartTime(now);
        }

        baseMapper.updateById(updateRequirement);

        // 添加流转历史记录
        ProjectWorkflowTransitionHistory insertHistory = ProjectWorkflowTransitionHistory.builder()
                .bizId(requirementId)
                .typeCode(requirement.getTypeCode())
                .source(sourceNodeId)
                .sourceStateCode(sourceStateCode)
                .target(targetNode.getId())
                .targetStateCode(targetStateCode)
                .spendTime(spendTime)
                .createTime(now)
                .systemFlow((ContextUtil.getUserId() == null || ContextUtil.getUserId() == 0L))
                .build();

        projectWorkflowTransitionHistoryMapper.insert(insertHistory);

        // 推送 工作项改变事件
        IssueUpdateEvent.sendEvent(TypeClassify.ISSUE, requirement.getId(),
                Bug.builder().id(requirement.getId()).stateCode(sourceStateCode).build(),
                Bug.builder().id(requirement.getId()).stateCode(targetStateCode).build()
        );
        // 场景3：软件需求状态流转时发送通知
        // 获取状态信息
        List<String> stateCodes = Arrays.asList(sourceStateCode, targetStateCode);
        List<State> states = stateMapper.selectList(Wraps.<State>lbQ().in(State::getCode, stateCodes));
        Map<String, String> stateNameMap = states.stream()
                .collect(Collectors.toMap(State::getCode, State::getName, (k1, k2) -> k1));

        String stateJumpStr = buildJumpString(requirement.getCode(), requirement.getName(), requirement.getId(), requirement.getProjectId(),requirement.getTypeCode());
        String sourceState = stateNameMap.get(sourceStateCode);
        String targetState = stateNameMap.get(targetStateCode);

        // 给负责人发送通知
        sendNotification(
                requirement.getLeadingBy(),
                requirement.getId(),
                "软件需求状态流转通知",
                String.format("%s将您负责的软件需求【%s】由【%s】流转到【%s】", creatorInfo, stateJumpStr, sourceState, targetState)
        );

        // 给处理人发送通知
        sendNotification(
                requirement.getHandleBy(),
                requirement.getId(),
                "软件需求状态流转通知",
                String.format("%s将您处理的软件需求【%s】由【%s】流转到【%s】", creatorInfo, stateJumpStr, sourceState, targetState)
        );

    }

    @Override
    public void calculationDelay()
    {
        baseMapper.calculationDelay();
    }

    @Override
    public List<WorkflowNode> findNextNode(Long requirementId)
    {
        Requirement requirement = super.getById(requirementId);
//        if (null == requirement.getProjectId())
//        {
//            throw BizException.validFail("当前需求未关联项目，无法查询流转状态");
//        }
        if (StrUtil.isEmpty(requirement.getTypeCode()))
        {
            throw BizException.validFail("当前需求没有需求类型，无法查询流转状态");
        }
        // 查询该需求使用的工作流
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(requirement.getProjectId(),
                        TypeClassify.ISSUE, requirement.getTypeCode());

        if (null == projectWorkflow)
        {
            throw BizException.validFail("当前需求未查询到工作流，无法查询可流转状态");
        }
        // 查询该需求的当前节点
        WorkflowNode thisWorkflowNode = workflowNodeMapper.selectOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, requirement.getStateCode()));

        if (null == thisWorkflowNode)
        {
            throw BizException.validFail("获取当前需求工作流状态节点失败");
        }

        // 查询可流转到的节点
        return getTargetNodeIds(requirement, projectWorkflow.getWorkflowId(), thisWorkflowNode.getId());
    }

    /**
     * 获取流转到的节点
     *
     * @param requirement 需求
     * @param workflowId 工作流id
     * @param thisNodeId 当前节点id
     * @return List<Long> 目标节点id
     * <AUTHOR>
     * @date 2021/11/15 13:01
     * @update zxy 2021/11/15 13:01
     * @since 1.0
     */
    private List<WorkflowNode> getTargetNodeIds(Requirement requirement, Long workflowId, Long thisNodeId)
    {

        List<WorkflowTransition> workflowTransitions = workflowTransitionMapper.selectList(
                Wraps.<WorkflowTransition>lbQ().eq(WorkflowTransition::getWorkflowId, workflowId)
                        .eq(WorkflowTransition::getSource, thisNodeId));

        if (workflowTransitions.isEmpty())
        {
            return Collections.emptyList();
        }
        boolean remove = true;
        Iterator<WorkflowTransition> iterator = workflowTransitions.iterator();
        while (iterator.hasNext())
        {
            WorkflowTransition workflowTransition = iterator.next();
            // 查询流转权限
            List<ProjectWorkflowAuthority> projectWorkflowAuthorities = projectWorkflowAuthorityMapper.selectList(
                    Wraps.<ProjectWorkflowAuthority>lbQ().eq(ProjectWorkflowAuthority::getWorkflowId, workflowId)
                            .eq(ProjectWorkflowAuthority::getProjectId, requirement.getProjectId())
                            .eq(ProjectWorkflowAuthority::getTypeClassify, TypeClassify.ISSUE)
                            .eq(ProjectWorkflowAuthority::getTypeCode, requirement.getTypeCode())
                            .eq(ProjectWorkflowAuthority::getTransitionId, workflowTransition.getId()));

            List<WorkflowAuthority> workflowAuthorities;
            if (projectWorkflowAuthorities.isEmpty())
            {
                workflowAuthorities = workflowAuthorityMapper.selectList(
                        Wraps.<WorkflowAuthority>lbQ()
                                .eq(WorkflowAuthority::getTransitionId, workflowTransition.getId()));
            }
            else
            {
                workflowAuthorities = BeanPlusUtil.toBeanList(projectWorkflowAuthorities, WorkflowAuthority.class);
            }
            boolean authorityBreak = false;
            for (WorkflowAuthority workflowAuthority : workflowAuthorities)
            {
                switch (workflowAuthority.getType())
                {
                    case RESPONSIBLE:
                        if (ContextUtil.getUserId().equals(requirement.getLeadingBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case HANDLER:
                        if (ContextUtil.getUserId().equals(requirement.getHandleBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case CREATOR:
                        if (ContextUtil.getUserId().equals(requirement.getPutBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case USER:
                        if (ContextUtil.getUserId().equals(workflowAuthority.getUserOrRole()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case ROLE:
                        List<ProjectUserRole> userRoles =
                                projectUserRoleMapper.selectList(
                                        Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getRoleId)
                                                .eq(ProjectUserRole::getProjectId, requirement.getProjectId())
                                                .eq(ProjectUserRole::getUserId, ContextUtil.getUserId()));
                        Set<Long> roleIds =
                                userRoles.stream().map(ProjectUserRole::getRoleId).collect(Collectors.toSet());
                        if (roleIds.contains(workflowAuthority.getUserOrRole()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    default:
                        break;
                }
                if (authorityBreak)
                {
                    break;
                }
            }

            if (remove)
            {
                iterator.remove();
            }

        }
        if (workflowTransitions.isEmpty())
        {
            return Collections.emptyList();
        }

        return workflowTransitions.stream().map(transition -> {
            WorkflowNode workflowNode = workflowNodeMapper.selectById(transition.getTarget());

            List<ProjectWorkflowTransitionCheck> checkList = transitionCheckService.findCheckList(transition.getId(),
                    requirement.getProjectId(), TypeClassify.ISSUE, requirement.getTypeCode());
            workflowNode.getEchoMap().put("check", checkList);
            return workflowNode;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return CollHelper.uniqueIndex(super.listByIds(ids), Requirement::getId, requirement -> requirement.setDescription(null));
    }

    @Override
    public IssueTypeComponentResult findTypeByProjectId(Long projectId)
    {
        // 根据项目id查询所有任务
        List<Requirement> bugs =
                baseMapper.selectList(Wraps.<Requirement>lbQ().eq(Requirement::getProjectId, projectId));

        // 查询所有状态
        List<State> states = stateMapper.selectList(Wraps.lbQ());

        // 按code和name分组
        Map<String, State> codeNameMap = states.stream().collect(Collectors.toMap(State::getCode, state -> state));

        Map<String, List<Requirement>> groupStateCodeRequirements =
                bugs.stream().collect(Collectors.groupingBy(Requirement::getStateCode));

        List<IssueTypeComponentResult.Pie> data = new LinkedList<>();

        for (Map.Entry<String, List<Requirement>> entry : groupStateCodeRequirements.entrySet())
        {
            data.add(IssueTypeComponentResult.Pie.builder()
                    .name(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getName() :
                            entry.getKey())
                    .color(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getColor() :
                            entry.getKey())
                    .code(entry.getKey())
                    .value(entry.getValue().size()).build());
        }

        return IssueTypeComponentResult.builder().count(bugs.size()).data(data).build();
    }

    @Override
    public IssueTypeComponentResult findRequirementComponentByProductId(Long productId)
    {
        // 根据产品id查询所有需求
        List<Requirement> requirements = super.list(Wraps.<Requirement>lbQ().eq(Requirement::getProductId, productId));

        // 查询所有状态
        List<State> states = stateMapper.selectList(Wraps.lbQ());

        // 按code和name分组
        Map<String, State> codeNameMap = states.stream().collect(Collectors.toMap(State::getCode, state -> state));

        Map<String, List<Requirement>> groupStateCodeRequirements =
                requirements.stream().collect(Collectors.groupingBy(Requirement::getStateCode));

        List<IssueTypeComponentResult.Pie> data = new LinkedList<>();

        for (Map.Entry<String, List<Requirement>> entry : groupStateCodeRequirements.entrySet())
        {
            data.add(IssueTypeComponentResult.Pie.builder()
                    .name(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getName() :
                            entry.getKey())
                    .color(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getColor() :
                            entry.getKey())
                    .code(entry.getKey())
                    .value(entry.getValue().size()).build());
        }

        return IssueTypeComponentResult.builder().count(requirements.size()).data(data).build();
    }

    @Override
    public IssueTypeComponentResult findRequirementComponentByPlanId(Long planId)
    {
        // 根据计划d查询所有需求
        List<Requirement> requirements = super.list(Wraps.<Requirement>lbQ().eq(Requirement::getPlanId, planId));

        // 查询所有状态
        List<State> states = stateMapper.selectList(Wraps.lbQ());

        // 按code和name分组
        Map<String, State> codeNameMap = states.stream().collect(Collectors.toMap(State::getCode, state -> state));

        Map<String, List<Requirement>> groupStateCodeRequirements =
                requirements.stream().collect(Collectors.groupingBy(Requirement::getStateCode));

        List<IssueTypeComponentResult.Pie> data = new LinkedList<>();

        for (Map.Entry<String, List<Requirement>> entry : groupStateCodeRequirements.entrySet())
        {
            data.add(IssueTypeComponentResult.Pie.builder()
                    .name(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getName() :
                            entry.getKey())
                    .color(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getColor() :
                            entry.getKey())
                    .code(entry.getKey())
                    .value(entry.getValue().size()).build());
        }

        return IssueTypeComponentResult.builder().count(requirements.size()).data(data).build();
    }

    @Override
    public Requirement getById(Serializable id)
    {
        Requirement requirement = super.getById(id);
        if (requirement != null)
        {
            requirement.setFiles(fileApi.findByBizTypeAndBizId(FileBizType.ISSUE_FILE_UPLOAD, id));
        }

        return requirement;
    }

    @Override
    public Map<String, Object> showIssueTopology(Long requirementId)
    {
        // 组装数据
        Map<String, Object> resultDataMap = Maps.newHashMapWithExpectedSize(10);
        //查询组装参数
        Map<String, Object> pmaps = Maps.newHashMapWithExpectedSize(3);
        //组装当前需求、任务、缺陷等编号
        List<String> issueCodeList = new ArrayList<>();
        //连线关系数据
        List<RequirementTopologyResult> topologyResultLst = new ArrayList<>();
        /**
         * 需求
         */
        //获取需求信息
        Requirement requirement = super.getById(requirementId);
        if (requirement == null)
        {
            throw BizException.validFail("查询需求信息错误，原因：当前需求信息为空");

        }
        issueCodeList.add(requirement.getCode());
        resultDataMap.put("requirement", requirement);
        // 查询父需求
        if (requirement.getParentId() != null)
        {
            Requirement requirementParent = super.getById(requirement.getParentId());
            resultDataMap.put("parent", requirementParent);
        }
        // 子需求
        List<Requirement> sonList =
                super.list(Wraps.<Requirement>lbQ().eq(Requirement::getParentId, requirement.getId()));
        if (CollectionUtils.isNotEmpty(sonList))
        {
            resultDataMap.put("son", sonList);
        }

        // todo idea 和 requirement 是 M:N 关系
        List<Idea> ideaList = ideaRequirementService.getIdeaList(requirementId);
        for (Idea idea : ideaList) {
            topologyResultLst.add(RequirementTopologyResult.builder().source(idea.getId() + "")
                    .target(requirement.getId() + "").build());
        }

        /**
         * 规划
         */
        //根据需求查询迭代信息
        ProjectPlan projectPlan = projectPlanService.getById(requirement.getPlanId());
        if (projectPlan != null)
        {
            resultDataMap.put("spring", projectPlan);
            topologyResultLst.add(RequirementTopologyResult.builder().source(requirement.getId() + "")
                    .target(projectPlan.getId() + "").build());
        }
        // 根据需求查询关联的任务List
        List<Task> taskLists = taskService.list(Wraps.<Task>lbQ().eq(Task::getRequirementId, requirement.getId()));
        if (!taskLists.isEmpty())
        {
            resultDataMap.put("taskList", taskLists);

            // 将任务key放入列表
            for (Task task : taskLists)
            {
                issueCodeList.add(task.getCode());
                topologyResultLst.add(
                        RequirementTopologyResult.builder().source(requirement.getId() + "").target(task.getId() + "")
                                .build());
            }
        }
        else
        {
            resultDataMap.put("taskList", new ArrayList<>());
        }

        /**
         * 测试
         */
        //计划查询
        List<TestPlan> testPlanList = testPlanApi.getTestPlanByRequirementId(requirement.getId());
        if (!testPlanList.isEmpty())
        {
            //计划信息
            resultDataMap.put("testPlanList", testPlanList);
            for (TestPlan plan : testPlanList)
            {
                topologyResultLst.add(
                        RequirementTopologyResult.builder().source(requirement.getId() + "").target(plan.getId() + "")
                                .build());
            }
        }
        else
        {
            resultDataMap.put("testPlanList", new ArrayList<>());
        }

        // 根据需求查询关联的缺陷List
        List<Bug> bugList = bugService.list(Wraps.<Bug>lbQ().eq(Bug::getRequirementId, requirement.getId()));
        if (!bugList.isEmpty())
        {
            List<Map<String, Object>> bugMapList = new ArrayList<>();
            //将与当前需求信息关联的缺陷信息进行遍历
            for (Bug bug : bugList)
            {
                //存放数据
                Map<String, Object> bugMap = Maps.newHashMapWithExpectedSize(2);
                //获取到缺陷id
                Long bugId = bug.getId();
                //获取缺陷的code，流水线查询时需要
                String bugCode = bug.getCode();
                // 将缺陷key放入集合
                issueCodeList.add(bugCode);
                //返回数据
                bugMap.put("bug", bug);
                topologyResultLst.add(
                        RequirementTopologyResult.builder().source(requirement.getId() + "").target(bug.getId() + "")
                                .build());
                //查询当前缺陷下的任务信息
                List<Task> bugTaskList = taskService.list(Wraps.<Task>lbQ().eq(Task::getBugId, bugId));
                if (!bugTaskList.isEmpty())
                {
                    bugMap.put("taskList", bugTaskList);

                    for (Task taskByBug : bugTaskList)
                    {
                        // 将缺陷下的任务key放入集合
                        issueCodeList.add(taskByBug.getCode());
                        topologyResultLst.add(
                                RequirementTopologyResult.builder().source(bugId + "").target(taskByBug.getId() + "")
                                        .build());
                    }
                }
                bugMapList.add(bugMap);
            }
            resultDataMap.put("bugList", bugList);
        }
        else
        {
            resultDataMap.put("bugList", new ArrayList<>());
        }

        /**
         * 开发
         */
        // 根据需求查询代码库Objec
        // 根据需求查询分支List
        Long productId = requirement.getProductId();
        if (productId != null)
        {

            //根据产品id查询服务引用
            List<Long> applicationidList = productApplicationApi.getApplicationIdListByProductId(productId);

            for (Long applicationid : applicationidList)
            {
                //根据服务应用查询代码库引擎id
              //  Long engineId = 0L;
                ApplicationEngineQuery applicationEngineQuery =
                        ApplicationEngineQuery.builder().applicationId(applicationid).build();
                List<Engine> saltEngines =
                        applicationEngineRedis.getByEngineByApplicationIdAndEnvId(applicationEngineQuery);
                for (Engine engine : saltEngines)
                {
                    if (engine.getState() && engine.getInstance().equals(EngineInstance.GIT_LAB))
                    {
                     //   engineId = engine.getId();
                        break;
                    }
                }
                //根据服务应用id获取代码库信息 todo
//                List<CodeRepository> codeRepositoryList = repositoryApi.getRepositoryList(applicationid);
//                if (codeRepositoryList != null && !codeRepositoryList.isEmpty())
//                {
//                    resultDataMap.put("codeRepository", codeRepositoryList);
//                    List<Map<String, Object>> branchList = new ArrayList<>();
//                    for (CodeRepository codeRepository : codeRepositoryList)
//                    {
//                        topologyResultLst.add(RequirementTopologyResult.builder().source(requirement.getId() + "")
//                                .target(codeRepository.getId() + "").build());
//                        //根据代码库信息查询分支信息
//                        List<CodeBranch> codeBranchList = repositoryApi.getRepositoryBranchList(codeRepository.getId(),
//                                codeRepository.getRepositoryType());
//                        for (CodeBranch codeBranch : codeBranchList)
//                        {
//                            topologyResultLst.add(
//                                    RequirementTopologyResult.builder().source(codeRepository.getId() + "")
//                                            .target(codeBranch.getId() + "").build());
//                            List logList = new ArrayList();
//                            List<Commit> commitList =
//                                    repositoryApi.allCommits(codeBranch.getId(), codeBranch.getBranchName(), engineId);
//                            if (commitList != null)
//                            {
//                                for (Commit commit : commitList)
//                                {
//                                    // 判断当前流水线提交日志需求号，是否在当前需求中
//                                    for (String key : issueCodeList)
//                                    {
//                                        if (commit.getMessage().contains(key))
//                                        {
//                                            topologyResultLst.add(
//                                                    RequirementTopologyResult.builder().source(codeBranch.getId() + "")
//                                                            .target(commit.getId()).build());
//                                            logList.add(commit);
//                                        }
//                                    }
//                                }
//                            }
//                            if (!logList.isEmpty())
//                            {
//                                Map<String, Object> branch = Maps.newHashMapWithExpectedSize(2);
//                                branch.put("branch", codeBranch);
//                                branch.put("commitLog", logList);
//
//                                branchList.add(branch);
//                            }
//                        }
//                    }
//
//                    if (!branchList.isEmpty())
//                    {
//                        resultDataMap.put("branchList", branchList);
//                    }
//                    else
//                    {
//                        resultDataMap.put("branchList", new ArrayList<>());
//                    }
//                }
//                else
//                {
//                    if(resultDataMap.get("codeRepository")==null){
//                        resultDataMap.put("codeRepository", new ArrayList<>());
//                    }
//                }

            }
        }
        /**
         * 测试
         */
     //   Map<String, Object> map = getPackAndPipelineByIssueCode(issueCodeList, topologyResultLst, requirement.getId());

//        if (!map.isEmpty())
//        {
//            resultDataMap.put("pack", map.get("pack"));
//            resultDataMap.put("pipelineList", map.get("pipelineList"));
//        }
//        resultDataMap.put("topologyResultLst", map.get("topologyResultLst"));

        return resultDataMap;
    }

//    private Map<String, Object> getPackAndPipelineByIssueCode(List<String> issueCodeList,
//            List<RequirementTopologyResult> topologyResultLst, Long requirementId)
//    {
//        // 根据需求查询制品List
//        // 根据根据制品反推流水线List
//        // 拿到所有的issueKey(需求、任务、缺陷)，并拼成String，以逗号分隔
//        Map<String, Object> resultMap = Maps.newHashMapWithExpectedSize(2);
//        ObjectMapper mapper = new ObjectMapper();
//
//        List<PackBase> packBaseList = packApi.selectPackListByIssueCode(issueCodeList);
//
//        if (!packBaseList.isEmpty())
//        {
//            resultMap.put("pack", packBaseList);
//
//            List<Map<String, Object>> pipelineJobAndPackIdGroupList = new ArrayList<>();
//            List<PipelineJob> pipelineJobList = new ArrayList<>();
//            // 流水线去重,并添加流水线下制品包列表
//            Map<String, List<Long>> packIdGroupByPipelineId = Maps.newHashMapWithExpectedSize(2);
//            for (PackBase packBase : packBaseList)
//            {
//                topologyResultLst.add(
//                        RequirementTopologyResult.builder().source(requirementId + "").target(packBase.getId() + "")
//                                .build());
//                // 根据制品包版本，查找流水线构建历史（CD或者CICD流水线）
//                List<PipelineJob> pipelineList =
//                        pipelineApi.selectListByVersionAndStatus(packBase.getVersion(),
//                                PipelineStatusType.SUCCESS);
//
//                for (PipelineJob pipelineJob : pipelineList)
//                {
//                    if (!packIdGroupByPipelineId.containsKey(pipelineJob.getId()))
//                    {
//                        List<Long> packIdGroup = new ArrayList<>();
//                        packIdGroup.add(packBase.getId());
//                        packIdGroupByPipelineId.put(pipelineJob.getId() + "", packIdGroup);
//
//                        pipelineJobList.add(pipelineJob);
//                        topologyResultLst.add(RequirementTopologyResult.builder().source(requirementId + "")
//                                .target(pipelineJob.getId() + "").build());
//                    }
//                }
//            }
//            for (PipelineJob pipelineJob : pipelineJobList)
//            {
//                Map<String, Object> pipelineJobAndPackIdGroupMap = Maps.newHashMapWithExpectedSize(2);
//                pipelineJobAndPackIdGroupMap.put("pipeline", pipelineJob);
//                pipelineJobAndPackIdGroupMap.put("packIdList",
//                        packIdGroupByPipelineId.get(String.valueOf(pipelineJob.getId())));
//                pipelineJobAndPackIdGroupList.add(pipelineJobAndPackIdGroupMap);
//            }
//            resultMap.put("pipelineList", pipelineJobAndPackIdGroupList);
//
//        }
//        else
//        {
//            resultMap.put("pack", new ArrayList<>());
//            resultMap.put("pipelineList", new ArrayList<>());
//        }
//        return resultMap;
//    }

    /**
     * 根据需求id获取需求拓扑图
     *
     * @param requirementId 需求id
     * @return
     */
    @Override
    public Map<String, Object> showRequirementTopology(Long requirementId)
    {
        // 组装数据
        Map<String, Object> resultDataMap = Maps.newHashMapWithExpectedSize(10);
        //组装当前需求、任务、缺陷等编号
        List<String> issueCodeList = new ArrayList<>();
        //连线关系数据
        List<RequirementTopologyResult> topologyResultLst = new ArrayList<>();
        /**
         * 需求
         */
        //获取需求信息
        Requirement requirement = super.getById(requirementId);
        if (requirement == null)
        {
            throw BizException.validFail("查询需求信息错误，原因：当前需求信息为空");

        }
        issueCodeList.add(requirement.getCode());
        resultDataMap.put("requirement", requirement);
        // todo idea 和 requirement 是 M:N 关系
        List<Idea> ideaList = ideaRequirementService.getIdeaList(requirementId);
        for (Idea idea : ideaList) {
            topologyResultLst.add(RequirementTopologyResult.builder().source(idea.getId() + "")
                    .target(requirement.getId() + "").build());
        }
//        topologyResultLst.add(RequirementTopologyResult.builder().source(requirement.getIdeaId() + "")
//                .target(requirement.getId() + "").build());

        // 查询父需求
        if (requirement.getParentId() != null)
        {
            Requirement requirementParent = super.getById(requirement.getParentId());
            resultDataMap.put("parent", requirementParent);
        }
        // 子需求
        List<Requirement> sonList =
                super.list(Wraps.<Requirement>lbQ().eq(Requirement::getParentId, requirement.getId()));
        if (CollectionUtils.isNotEmpty(sonList))
        {
            resultDataMap.put("son", sonList);
        }
        resultDataMap.put("issueCodeList", issueCodeList);
        resultDataMap.put("topologyResultLst", topologyResultLst);
        return resultDataMap;
    }

    @Override
    public Map<String, Object> showPlanTopology(Long requirementId, List<String> issueCodeList)
    {
        // 组装数据
        Map<String, Object> resultDataMap = Maps.newHashMapWithExpectedSize(10);
        Requirement requirement = super.getById(requirementId);
        //连线关系数据
        List<RequirementTopologyResult> topologyResultLst = new ArrayList<>();
        /**
         * 规划
         */
        //根据需求查询迭代信息
        ProjectPlan projectPlan = projectPlanService.getById(requirement.getPlanId());
        if (projectPlan != null)
        {
            resultDataMap.put("projectPlan", projectPlan);
            topologyResultLst.add(RequirementTopologyResult.builder().source(requirement.getId() + "")
                    .target(projectPlan.getId() + "").build());
        }
        // 根据需求查询关联的任务List
        List<Task> taskLists = taskService.list(Wraps.<Task>lbQ().eq(Task::getRequirementId, requirement.getId()));
        if (!taskLists.isEmpty())
        {
            resultDataMap.put("taskList", taskLists);

            // 将任务key放入列表
            for (Task task : taskLists)
            {
                issueCodeList.add(task.getCode());
                topologyResultLst.add(
                        RequirementTopologyResult.builder().source(requirement.getId() + "").target(task.getId() + "")
                                .build());
            }
        }
        else
        {
            resultDataMap.put("taskList", new ArrayList<>());
        }
        resultDataMap.put("issueCodeList", issueCodeList);
        resultDataMap.put("topologyResultLst", topologyResultLst);
        return resultDataMap;
    }

    @Override
    public Map<String, Object> showTestmTopology(Long requirementId, List<String> issueCodeList)
    {
        // 组装数据
        Map<String, Object> resultDataMap = Maps.newHashMapWithExpectedSize(10);
        Requirement requirement = super.getById(requirementId);
        //连线关系数据
        List<RequirementTopologyResult> topologyResultLst = new ArrayList<>();
        /**
         * 测试
         */
        //计划查询
        List<TestPlan> testPlanList = testPlanApi.getTestPlanByRequirementId(requirement.getId());
        if (!testPlanList.isEmpty())
        {
            //计划信息
            resultDataMap.put("testPlanList", testPlanList);
            for (TestPlan plan : testPlanList)
            {
                topologyResultLst.add(
                        RequirementTopologyResult.builder().source(requirement.getId() + "").target(plan.getId() + "")
                                .build());
            }
        }
        else
        {
            resultDataMap.put("testPlanList", new ArrayList<>());
        }

        // 根据需求查询关联的缺陷List
        List<Bug> bugList = bugService.list(Wraps.<Bug>lbQ().eq(Bug::getRequirementId, requirement.getId()));
        if (!bugList.isEmpty())
        {
            List<Map<String, Object>> bugMapList = new ArrayList<>();
            //将与当前需求信息关联的缺陷信息进行遍历
            for (Bug bug : bugList)
            {
                topologyResultLst.add(
                        RequirementTopologyResult.builder().source(requirement.getId() + "").target(bug.getId() + "")
                                .build());
                //存放数据
                Map<String, Object> bugMap = Maps.newHashMapWithExpectedSize(2);
                //获取到缺陷id
                Long bugId = bug.getId();
                //获取缺陷的code，流水线查询时需要
                String bugCode = bug.getCode();
                // 将缺陷key放入集合
                issueCodeList.add(bugCode);
                //返回数据
                bugMap.put("bug", bug);
                //查询当前缺陷下的任务信息
                List<Task> bugTaskList = taskService.list(Wraps.<Task>lbQ().eq(Task::getBugId, bugId));
                if (!bugTaskList.isEmpty())
                {
                    bugMap.put("taskList", bugTaskList);

                    for (Task taskByBug : bugTaskList)
                    {
                        // 将缺陷下的任务key放入集合
                        issueCodeList.add(taskByBug.getCode());
                        topologyResultLst.add(RequirementTopologyResult.builder().source(bug.getId() + "")
                                .target(taskByBug.getId() + "").build());
                    }
                }
                bugMapList.add(bugMap);
            }
            resultDataMap.put("bugList", bugList);
        }
        else
        {
            resultDataMap.put("bugList", new ArrayList<>());
        }
       // Map<String, Object> map = getPackAndPipelineByIssueCode(issueCodeList, topologyResultLst, requirementId);

//        if (!map.isEmpty())
//        {
//            resultDataMap.put("pack", map.get("pack"));
//            resultDataMap.put("pipelineList", map.get("pipelineList"));
//        }
//        resultDataMap.put("issueCodeList", issueCodeList);
//        resultDataMap.put("topologyResultLst", map.get("topologyResultLst"));
        return resultDataMap;
    }

    @Override
    public Map<String, Object> showDevelopmentTopology(Long requirementId, List<String> issueCodeList)
    {
        // 组装数据
        Map<String, Object> resultDataMap = Maps.newHashMapWithExpectedSize(10);
        Requirement requirement = super.getById(requirementId);
        //连线关系数据
        List<RequirementTopologyResult> topologyResultLst = new ArrayList<>();

        /**
         * 开发
         */
        // 根据需求查询代码库Objec
        // 根据需求查询分支List
        Long productId = requirement.getProductId();
        if (productId != null)
        {

            //根据产品id查询服务引用
            List<Long> applicationIdList = productApplicationApi.getApplicationIdListByProductId(productId);
            for (Long applicationId : applicationIdList)
            {
                //根据服务应用查询代码库引擎id
             //   Long engineId = 0L;
                ApplicationEngineQuery applicationEngineQuery =
                        ApplicationEngineQuery.builder().applicationId(applicationId).build();
                List<Engine> saltEngines =
                        applicationEngineRedis.getByEngineByApplicationIdAndEnvId(applicationEngineQuery);
                for (Engine engine : saltEngines)
                {
                    if (engine.getState() && engine.getInstance().equals(EngineInstance.GIT_LAB))
                    {
                   //     engineId = engine.getId();
                        break;
                    }
                }
                //根据服务应用id获取代码库信息 todo
//                List<CodeRepository> codeRepositoryList = repositoryApi.getRepositoryList(applicationId);
//                if (codeRepositoryList != null && !codeRepositoryList.isEmpty())
//                {
//                    resultDataMap.put("codeRepository", codeRepositoryList);
//                    List<Map<String, Object>> branchList = new ArrayList<>();
//                    for (CodeRepository codeRepository : codeRepositoryList)
//                    {
//                        topologyResultLst.add(RequirementTopologyResult.builder().source(requirement.getId() + "")
//                                .target(codeRepository.getId() + "").build());
//                        //根据代码库信息查询分支信息
//                        List<CodeBranch> codeBranchList = repositoryApi.getRepositoryBranchList(codeRepository.getId(),
//                                codeRepository.getRepositoryType());
//                        for (CodeBranch codeBranch : codeBranchList)
//                        {
//                            List<Commit> logList = new ArrayList<>();
//                            List<Commit> commitList = repositoryApi.allCommits(codeBranch.getCodeRepositoryId(),
//                                    codeBranch.getBranchName(), engineId);
//                            topologyResultLst.add(
//                                    RequirementTopologyResult.builder().source(codeRepository.getId() + "")
//                                            .target(codeBranch.getId() + "").build());
//                            if (commitList != null)
//                            {
//                                for (Commit commit : commitList)
//                                {
//                                    // 判断当前流水线提交日志需求号，是否在当前需求中
//                                    for (String key : issueCodeList)
//                                    {
//                                        if (commit.getMessage().contains(key))
//                                        {
//                                            logList.add(commit);
//                                            topologyResultLst.add(
//                                                    RequirementTopologyResult.builder().source(codeBranch.getId() + "")
//                                                            .target(commit.getId() + "").build());
//                                        }
//                                    }
//                                }
//                            }
//                            if (!logList.isEmpty())
//                            {
//                                Map<String, Object> branch = Maps.newHashMapWithExpectedSize(2);
//                                branch.put("branch", codeBranch);
//                                branch.put("commitLog", logList);
//
//                                branchList.add(branch);
//                            }
//                        }
//                    }
//
//                    if (!branchList.isEmpty())
//                    {
//                        resultDataMap.put("branchList", branchList);
//                    }
//                    else
//                    {
//                        resultDataMap.put("branchList", new ArrayList<>());
//                    }
//                }
//                else
//                {
//                    resultDataMap.computeIfAbsent("codeRepository", k -> new ArrayList<>());
//                }

            }
        }
        resultDataMap.put("topologyResultLst", topologyResultLst);
        resultDataMap.put("issueCodeList", issueCodeList);
        return resultDataMap;
    }

    @Override
    public List<Requirement> selectRequirementByIdeaIdOrRequirementId(Requirement requirement)
    {
        List<Requirement> list = new ArrayList<>();
        if (requirement.getId() != null)
        {
            list = super.list(Wraps.<Requirement>lbQ().eq(Requirement::getParentId, requirement.getId()));
        }
        List<Long> idList = list.stream().map(Requirement::getId).collect(Collectors.toList());

        if (idList.isEmpty())
        {
            return list;
        }
        //判断是否可以需要查询
        boolean isFor = true;
        List<Long> requirementIds = idList;
        while (isFor)
        {
            List<Requirement> requirementList =
                    super.list(Wraps.<Requirement>lbQ().in(Requirement::getParentId, requirementIds));
            if (!requirementList.isEmpty())
            {
                requirementList.removeIf(requirement1 -> idList.contains(requirement1.getId()));
                requirementIds = requirementList.stream().map(Requirement::getId).collect(Collectors.toList());
                list.addAll(requirementList);
            }
            else
            {
                isFor = false;
            }
        }
        return list;
    }

    @Override
    public void correlatedIdea(RequirementCorrelatedIdeaDTO model) {

        if (ADD_KEY.equals(model.getCorrelatedType())) {
            LbqWrapper<IssueIdeaProductRequirement> wrapper = Wraps.lbQ();
            wrapper.eq(IssueIdeaProductRequirement::getIdeaId, model.getIdeaId());
            wrapper.eq(IssueIdeaProductRequirement::getRequirementId, model.getRequirementId());
            if (ideaRequirementService.count(wrapper) > 0) {
                throw BizException.validFail("需求和用户需求关系已存在");
            }
            ideaRequirementService.save(
                    IssueIdeaProductRequirement.builder()
                            .ideaId(model.getIdeaId())
                            .requirementId(model.getRequirementId())
                            .build());
        } else {
            LbqWrapper<IssueIdeaProductRequirement> wrapper = Wraps.lbQ();
            wrapper.eq(IssueIdeaProductRequirement::getIdeaId, model.getIdeaId());
            wrapper.eq(IssueIdeaProductRequirement::getRequirementId, model.getRequirementId());
            ideaRequirementService.remove(wrapper);
        }
    }

    @Override
    public List<RequirementViewResultVo> getRequirementGroupByProductIdAndProductVersionId(Long productId, Long productVersionId, RequirementViewResultVo vo)
    {
        List<RequirementViewResultVo> resultVoList =new ArrayList<>();
        if (productId == null)
        {
            throw BizException.validFail("查询用户故事地图失败，原因：当前产品id为空");

        }
        List<Requirement> requirementList;
        if(productVersionId!=null){
            requirementList =baseMapper.selectList(Wraps.<Requirement>lbQ()
                    .eq(Requirement::getProductId, productId)
                    .eq(Requirement::getTypeCode, "STORY")
                    .eq(Requirement::getProductVersionId, productVersionId));
        }
        else
        {
            requirementList =baseMapper.selectList(Wraps.<Requirement>lbQ()
                    .eq(Requirement::getProductId, productId)
                    .eq(Requirement::getTypeCode, "STORY")
                    .isNull(Requirement::getProductVersionId));
        }
        //不分组，直接组装数据返回
        if(!vo.getIsgroup())
        {
            List<RequirementMapVo> results =
                    BeanPlusUtil.toBeanList(requirementList, RequirementMapVo.class);

            RequirementViewResultVo resultVo= RequirementViewResultVo.builder()
                    .isgroup(false)
                    .groupName("未分组").requirementMapVoList(results).build();
            resultVoList.add(resultVo);
        }
        else
        {
            switch (vo.getGroupType())
            {
                case "MODULE":
                    return getmoduleGroupList(requirementList);
                case "LEADING_BY":
                    return getLeadingByGroupList(requirementList);
                case "PRIORITY_CODE":
                    return getPriorityCodeGroupList(requirementList);
                case "STATE_CODE":
                    return getStateCodeGroupList(requirementList);
                default:
                    return resultVoList;
            }
        }
        return resultVoList;
    }

    /**
     * 获取具有流转权限角色的用户id
     * @param oldRequirement
     * @return {@link List< Long>}
     * @throws
     * <AUTHOR>
     * @date 2025/8/7 10:52
     * @update sxh 2025/8/7 10:52
     * @since 1.0
     */
    @Override
    public List<Long> getWorkFlowUserIds(Requirement oldRequirement) {
        List<Long> allUserIds = new ArrayList<>();
        // 查询该任务使用的工作流
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(oldRequirement.getProjectId(),
                        TypeClassify.ISSUE,
                        oldRequirement.getTypeCode());
        if (null == projectWorkflow || null == projectWorkflow.getWorkflowInfo()) {
            throw BizException.validFail("当前任务未查询到工作流，无法查询可流转状态");
        }
        // 查询该任务的当前节点
        WorkflowNode thisWorkflowNode = workflowNodeMapper.selectOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, oldRequirement.getStateCode()));

        if (null == thisWorkflowNode) {
            throw BizException.validFail("获取当前任务工作流状态节点失败");
        }
        // 获取流转到的节点
        List<WorkflowTransition> workflowTransitions = workflowTransitionMapper.selectList(
                Wraps.<WorkflowTransition>lbQ().eq(WorkflowTransition::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowTransition::getSource, thisWorkflowNode.getId()));

        if (workflowTransitions.isEmpty()) {
            return Collections.emptyList();
        }
        Iterator<WorkflowTransition> iterator = workflowTransitions.iterator();
        while (iterator.hasNext()) {
            WorkflowTransition workflowTransition = iterator.next();
            // 查询流转权限
            List<ProjectWorkflowAuthority> projectWorkflowAuthorities = projectWorkflowAuthorityMapper.selectList(
                    Wraps.<ProjectWorkflowAuthority>lbQ().eq(ProjectWorkflowAuthority::getWorkflowId, projectWorkflow.getWorkflowId())
                            .eq(ProjectWorkflowAuthority::getProjectId, oldRequirement.getProjectId())
                            .eq(ProjectWorkflowAuthority::getTypeClassify, TypeClassify.ISSUE)
                            .eq(ProjectWorkflowAuthority::getTypeCode, oldRequirement.getTypeCode())
                            .eq(ProjectWorkflowAuthority::getTransitionId, workflowTransition.getId())
                            .eq(ProjectWorkflowAuthority::getType, WorkflowAuthorityType.ROLE));


            if (!projectWorkflowAuthorities.isEmpty()) {
                List<WorkflowAuthority> workflowAuthorities = BeanPlusUtil.toBeanList(projectWorkflowAuthorities, WorkflowAuthority.class);
                for(WorkflowAuthority workflowAuthority:workflowAuthorities){
                    List<ProjectUserRole> projectUserRoles =
                            projectUserRoleMapper.selectList(
                                    Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getUserId)
                                            .eq(ProjectUserRole::getProjectId, oldRequirement.getProjectId())
                                            .eq(ProjectUserRole::getRoleId, workflowAuthority.getUserOrRole()));
                    List<Long> userIds =
                            projectUserRoles.stream().map(ProjectUserRole::getUserId).collect(Collectors.toList());
                    allUserIds.addAll(userIds);
                }

            }

            List<WorkflowAuthority> workflowAuthorities = workflowAuthorityMapper.selectList(
                    Wraps.<WorkflowAuthority>lbQ()
                            .eq(WorkflowAuthority::getTransitionId, workflowTransition.getId())
                            .eq(WorkflowAuthority::getType, WorkflowAuthorityType.ROLE));
            for(WorkflowAuthority workflowAuthority:workflowAuthorities){
                List<ProjectUserRole> projectUserRoles =
                        projectUserRoleMapper.selectList(
                                Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getUserId)
                                        .eq(ProjectUserRole::getProjectId, oldRequirement.getProjectId())
                                        .eq(ProjectUserRole::getRoleId, workflowAuthority.getUserOrRole()));
                List<Long> userIds =
                        projectUserRoles.stream().map(ProjectUserRole::getUserId).collect(Collectors.toList());
                allUserIds.addAll(userIds);
            }

        }
        if (oldRequirement.getProjectId()==null){
            return allUserIds;
        }
        //新增项目经理用户的id
        ProjectInfo projectInfo = projectInfoMapper.selectById(oldRequirement.getProjectId());
        String pmRoleCode = projectInfo.getTypeCode() + PM_CODE;
        // 查询项目经理角色id
        ProjectRole projectRole = projectRoleService.getOne(
                Wraps.<ProjectRole>lbQ().eq(ProjectRole::getCode, pmRoleCode).eq(ProjectRole::getReadonly, true)
                        .eq(ProjectRole::getType, true).eq(ProjectRole::getTypeCode, projectInfo.getTypeCode())
                        .last(" limit 1"));
        //查询用户信息
        List<Long> pmUserIds = projectUserRoleMapper.selectList(
                        Wraps.<ProjectUserRole>lbQ()
                                .eq(ProjectUserRole::getProjectId, oldRequirement.getProjectId())
                                .eq(ProjectUserRole::getRoleId, projectRole.getId())
                ).stream()
                .map(ProjectUserRole::getUserId)
                .collect(Collectors.toList());
        allUserIds.addAll(pmUserIds);
        return allUserIds;
    }

    /**
     * 根据模块分组查询用户故事地图
     *
     * @param requirementList 根据条件查询的需求数据
     * @return List<RequirementViewResultVo> 需求数据
     * <AUTHOR>
     * @date 2022/09/13 14:59
     * @update lxr 2022/09/13 14:59
     * @since 1.0
     */
    private List<RequirementViewResultVo> getmoduleGroupList(List<Requirement> requirementList){

        List<RequirementViewResultVo> resultVoList =new ArrayList<>();
        // 把需求信息按模块功能分组
        List<Requirement> moduleIsNotNull = requirementList.stream().filter((filed) -> filed.getProductModuleFunctionId()!=null).collect(Collectors.toList());
        List<Requirement> moduleIsNull = requirementList.stream().filter((filed) -> filed.getProductModuleFunctionId()==null).collect(Collectors.toList());
        Map<Long, List<Requirement>> leadingGroupMap =
                moduleIsNotNull.stream().collect(Collectors.groupingBy(Requirement::getProductModuleFunctionId));

        //获取需求集合中模块功能id集合
        List<Long> ids = requirementList.stream().map(Requirement::getProductModuleFunctionId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        List<ProductModuleFunction> moduleFunctionList= productModuleFunctionApi.getProductModuleFunctionListByIds(ids);
        // 把模块功能根据id分组
        Map<Long, String> moduleFunctionMap = moduleFunctionList.stream().collect(Collectors.toMap(ProductModuleFunction::getId,ProductModuleFunction::getName));

        //循环组装数据
        for (Map.Entry<Long, List<Requirement>> moduleFunctionEntry : leadingGroupMap.entrySet())
        {
            Long moduleFunctionId = moduleFunctionEntry.getKey();
            List<Requirement> reqList = moduleFunctionEntry.getValue();
            List<RequirementMapVo> results =
                    BeanPlusUtil.toBeanList(reqList, RequirementMapVo.class);

            if (moduleFunctionId!=null)
            {
                RequirementViewResultVo resultVo= RequirementViewResultVo.builder()
                        .isgroup(true)
                        .groupCount(results.size())
                        .groupName(moduleFunctionMap.get(moduleFunctionId)!=null?moduleFunctionMap.get(moduleFunctionId):moduleFunctionId.toString()).requirementMapVoList(results).build();
                resultVoList.add(resultVo);
            }
        }
        if(!moduleIsNull.isEmpty()){
            List<RequirementMapVo> results =
                    BeanPlusUtil.toBeanList(moduleIsNull, RequirementMapVo.class);
            RequirementViewResultVo resultVo= RequirementViewResultVo.builder()
                    .isgroup(true)
                    .groupCount(moduleIsNull.size())
                    .groupName(GROUP_NAME).requirementMapVoList(results).build();
            resultVoList.add(resultVo);
        }
        return resultVoList;
    }

    /**
     * 根据负责人分组查询用户故事地图
     *
     * @param requirementList 根据条件查询的需求数据
     * @return List<RequirementViewResultVo> 需求数据
     * <AUTHOR>
     * @date 2022/09/13 14:59
     * @update lxr 2022/09/13 14:59
     * @since 1.0
     */
    private List<RequirementViewResultVo> getLeadingByGroupList(List<Requirement> requirementList){
        List<RequirementViewResultVo> resultVoList =new ArrayList<>();
        // 把需求信息按负责人分组
        List<Requirement> leadingByIsNotNull = requirementList.stream().filter((filed) -> filed.getLeadingBy()!=null).collect(Collectors.toList());
        List<Requirement> leadingByIsNull = requirementList.stream().filter((filed) -> filed.getLeadingBy()==null).collect(Collectors.toList());
        Map<Long, List<Requirement>> leadingGroupMap =
                leadingByIsNotNull.stream().collect(Collectors.groupingBy(Requirement::getLeadingBy));

        //获取需求集合中负责人id集合
        List<Long> ids = requirementList.stream().map(Requirement::getLeadingBy).collect(Collectors.toList());
        Map<Long, User> userMap= userApi.selectByIds(ids);
        for (Map.Entry<Long, List<Requirement>> leadingEntry : leadingGroupMap.entrySet())
        {
            Long userId = leadingEntry.getKey();
            List<Requirement> reqList = leadingEntry.getValue();
            List<RequirementMapVo> results =
                    BeanPlusUtil.toBeanList(reqList, RequirementMapVo.class);

            if (userId!=null)
            {
                RequirementViewResultVo resultVo= RequirementViewResultVo.builder()
                        .isgroup(true)
                        .groupCount(results.size())
                        .groupName(userMap.get(userId)!=null?userMap.get(userId).getName():userId.toString()).requirementMapVoList(results).build();
                resultVoList.add(resultVo);
            }
        }
        if(!leadingByIsNull.isEmpty()){
            List<RequirementMapVo> results =
                    BeanPlusUtil.toBeanList(leadingByIsNull, RequirementMapVo.class);
            RequirementViewResultVo resultVo= RequirementViewResultVo.builder()
                    .isgroup(true)
                    .groupCount(leadingByIsNull.size())
                    .groupName(GROUP_NAME).requirementMapVoList(results).build();
            resultVoList.add(resultVo);
        }
        return resultVoList;
    }

    /**
     * 根据优先级分组查询用户故事地图
     *
     * @param requirementList 根据条件查询的需求数据
     * @return List<RequirementViewResultVo> 需求数据
     * <AUTHOR>
     * @date 2022/09/13 14:59
     * @update lxr 2022/09/13 14:59
     * @since 1.0
     */
    private List<RequirementViewResultVo> getPriorityCodeGroupList(List<Requirement> requirementList){
        List<RequirementViewResultVo> resultVoList =new ArrayList<>();

        // 查询所有优先级信息
        List<Priority> priorityList = priorityMapper.selectList(Wraps.lbQ());
        // 把优先级按code分组
        Map<String, String> priorityCodeMap = priorityList.stream().collect(Collectors.toMap(Priority::getCode,Priority::getName));
        // 把需求信息按优先级分组
        Map<String, List<Requirement>> priorityGroupMap =
                requirementList.stream().collect(Collectors.groupingBy(Requirement::getPriorityCode));
        for (Map.Entry<String, List<Requirement>> priorityCodeEntry : priorityGroupMap.entrySet())
        {
            String priorityCode = priorityCodeEntry.getKey();
            List<Requirement> reqList = priorityCodeEntry.getValue();
            List<RequirementMapVo> results =
                    BeanPlusUtil.toBeanList(reqList, RequirementMapVo.class);
            if (priorityCode!=null)
            {
                RequirementViewResultVo resultVo= RequirementViewResultVo.builder()
                        .isgroup(true)
                        .groupCount(results.size())
                        .groupName(priorityCodeMap.get(priorityCode)!=null?priorityCodeMap.get(priorityCode):priorityCode).requirementMapVoList(results).build();
                resultVoList.add(resultVo);
            }
            else
            {
                RequirementViewResultVo resultVo= RequirementViewResultVo.builder()
                        .isgroup(true)
                        .groupCount(results.size())
                        .groupName(GROUP_NAME).requirementMapVoList(results).build();
                resultVoList.add(resultVo);
            }
        }
        return resultVoList;
    }

    /**
     * 根据状态分组查询用户故事地图
     *
     * @param requirementList 根据条件查询的需求数据
     * @return List<RequirementViewResultVo> 需求数据
     * <AUTHOR>
     * @date 2022/09/13 14:59
     * @update lxr 2022/09/13 14:59
     * @since 1.0
     */
    private List<RequirementViewResultVo> getStateCodeGroupList(List<Requirement> requirementList){
        List<RequirementViewResultVo> resultVoList =new ArrayList<>();

        // 查询有状态的阶段信息
        List<State> states = stateMapper.selectList(Wraps.lbQ());
        // 把状态按code分组
        Map<String, String> stateCodeMap = states.stream().collect(Collectors.toMap(State::getCode,State::getName));

        // 把需求信息按状态分组
        Map<String, List<Requirement>> stateCodeGroupMap =
                requirementList.stream().collect(Collectors.groupingBy(Requirement::getStateCode));
        for (Map.Entry<String, List<Requirement>> stateCodeEntry : stateCodeGroupMap.entrySet())
        {
            String stateCode = stateCodeEntry.getKey();
            List<Requirement> reqList = stateCodeEntry.getValue();
            List<RequirementMapVo> results =
                    BeanPlusUtil.toBeanList(reqList, RequirementMapVo.class);
            if (stateCode!=null)
            {
                RequirementViewResultVo resultVo= RequirementViewResultVo.builder()
                        .isgroup(true)
                        .groupCount(results.size())
                        .groupName(stateCodeMap.get(stateCode)!=null ? stateCodeMap.get(stateCode): stateCode).requirementMapVoList(results).build();
                resultVoList.add(resultVo);
            }
            else
            {
                RequirementViewResultVo resultVo= RequirementViewResultVo.builder()
                        .isgroup(true)
                        .groupCount(results.size())
                        .groupName(GROUP_NAME).requirementMapVoList(results).build();
                resultVoList.add(resultVo);
            }
        }
        return resultVoList;
    }

    @Override
    public IPage<IssueGroupVO> groupSelect(IPage<IssueGroupVO> page, String groupBy, LbqWrapper<Requirement> wrapper) {
        return this.baseMapper.groupSelect(page, groupBy, wrapper);
    }
    // 通用消息发送工具方法
    private void sendNotification(Long receiverId, Long businessId, String title, String content) {
        // 接收人校验：为空或为创建人则不发送
        if (receiverId == null || receiverId.equals(ContextUtil.getUserId())) {
            return;
        }

        // 构建消息对象并发送
        MsgDTO msgDTO = MsgDTO.buildNotify(MsgBizType.ISSUE_NOTIFY, businessId, title, content);
        msgDTO.setAuthorId(ContextUtil.getUserId());

        MsgSaveDTO msgSaveDTO = MsgSaveDTO.builder()
                .userIdList(Collections.singleton(receiverId))
                .msgDTO(msgDTO)
                .build();
        msgApi.save(msgSaveDTO);
    }

    // 构建用户信息字符串（带空值处理）
    private String buildUserString() {
        return StringUtil.isEmpty(ContextUtil.getUserName())
                ? "系统"
                : String.format("%s(%s)", ContextUtil.getUserName(), ContextUtil.getUserAccount());
    }

    // 构建跳转字符串
    private String buildJumpString(String code, String name, Long id, Long projectId,String typeCode) {
        return DetectionMessageContent.buildJumpString(code, name, id, TypeClassify.ISSUE, projectId,typeCode);
    }
}
