package com.jettech.jettong.alm.project.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.exception.BizException;
import com.jettech.jettong.alm.project.dao.*;
import com.jettech.jettong.alm.project.entity.*;
import com.jettech.jettong.alm.project.service.ProjectRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.jettech.jettong.alm.project.service.impl.ProjectMenuServiceImpl.PM_CODE;

/**
 * 项目角色信息业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 项目角色信息业务层
 * @projectName jettong
 * @package com.jettech.jettong.alm.project.service.impl
 * @className ProjectRoleServiceImpl
 * @date 2021-11-10
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class ProjectRoleServiceImpl extends SuperServiceImpl<ProjectRoleMapper, ProjectRole>
        implements ProjectRoleService
{

    private final ProjectRoleAuthorityMapper projectRoleAuthorityMapper;

    private final ProjectUserRoleMapper userRoleMapper;

    private final ProjectRoleProjectMapper projectRoleProjectMapper;
    private final ProjectMenuDicMapper projectMenuDicMapper;
    private final ProjectInfoMapper projectInfoMapper;

    @Override
    public ProjectRole saveProjectRole(ProjectRole projectRole, Long projectId)
    {
        if (check(projectRole.getCode(), projectRole.getTypeCode(), projectId))
        {
            throw BizException.validFail("标识重复");
        }
        super.save(projectRole);
        //项目私有角色
        if (projectId != null)
        {
            ProjectRoleProject build =
                    ProjectRoleProject.builder().projectId(projectId).projectRoleId(projectRole.getId())
                            .createdBy(ContextUtil.getUserId()).createTime(LocalDateTime.now()).build();
            projectRoleProjectMapper.insert(build);
        }
        return projectRole;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteProjectRoleByIds(List<Long> roleIds)
    {
        if (CollUtil.isEmpty(roleIds))
        {
            return;
        }
        List<ProjectRole> projectRoles = super.listByIds(roleIds);
        List<ProjectRole> readOnlyProjectIds =
                projectRoles.stream().filter(ProjectRole::getReadonly).collect(Collectors.toList());
        if (!readOnlyProjectIds.isEmpty())
        {
            throw BizException.validFail("内置项目角色不能删除");
        }
        List<Long> collect =
                projectRoles.stream().filter(i -> !i.getType()).map(ProjectRole::getId).collect(Collectors.toList());
        if (collect.size() != 0){
            //删除项目下引用的角色关联信息
            projectRoleProjectMapper.delete(Wraps.<ProjectRoleProject>lbQ().in(ProjectRoleProject::getProjectRoleId,roleIds));
        }

        // 删除 角色-菜单 关联关系
        projectRoleAuthorityMapper.delete(
                Wraps.<ProjectRoleAuthority>lbQ().in(ProjectRoleAuthority::getRoleId, roleIds));

        // 删除 角色-用户关系
        userRoleMapper.delete(Wraps.<ProjectUserRole>lbQ().in(ProjectUserRole::getRoleId, roleIds));

        // 删除角色
        super.removeByIds(roleIds);

    }

    @Override
    public void updateProjectRole(ProjectRole projectRole)
    {
        super.updateById(projectRole);
    }

    @Override
    public void insertOrUpdateProjectRoleAuthoritys(Map<Long,List<Long>>  roleMenusIds)
    {

        List<ProjectRoleAuthority> projectRoleAuthorities = new ArrayList<>();
        List<Long> roleIds = new ArrayList<>();
        for (Map.Entry<Long, List<Long>> entry : roleMenusIds.entrySet()) {
            Long roleId = entry.getKey();
            roleIds.add(roleId);
            List<Long> idList = entry.getValue();
            if(idList!=null){
                for (Long id : idList) {
                    ProjectRoleAuthority projectRoleAuthority = new ProjectRoleAuthority();
                    projectRoleAuthority.setRoleId(roleId);
                    projectRoleAuthority.setMenuId(id);
                    projectRoleAuthorities.add(projectRoleAuthority);
                }
            }
        }
        if(roleIds.size()>0){
            projectRoleAuthorityMapper.delete(Wraps.<ProjectRoleAuthority>lbQ().in(ProjectRoleAuthority::getRoleId,roleIds));
        }
        if(!projectRoleAuthorities.isEmpty()){
            projectRoleAuthorityMapper.insertBatchSomeColumn(projectRoleAuthorities);
        }
    }

    @Override
    public IPage<ProjectRole> findPage(IPage<ProjectRole> page, Map<String, Object> params)
    {
        return baseMapper.findPage(page, params);
    }

    @Override
    public List<ProjectRole> findMap(Map<String, Object> params)
    {
        return baseMapper.findMap(params);
    }

    @Override
    public boolean check(String code, String typeCode, Long projectId)
    {
        if (projectId == null)
        {
            return super.count(Wraps.<ProjectRole>lbQ()
                    .eq(ProjectRole::getCode, code)
                    .eq(ProjectRole::getTypeCode, typeCode)
                    .eq(ProjectRole::getType, true)) > 0;
        }
        else
        {
            List<Long> collect = projectRoleProjectMapper
                    .selectList(Wraps.<ProjectRoleProject>lbQ().eq(ProjectRoleProject::getProjectId, projectId))
                    .stream().map(ProjectRoleProject::getProjectRoleId).collect(Collectors.toList());
            if (collect.isEmpty())
            {
                return super.count(Wraps.<ProjectRole>lbQ()
                        .eq(ProjectRole::getCode, code)
                        .eq(ProjectRole::getTypeCode, typeCode)
                        .eq(ProjectRole::getType, true)) > 0;
            }
            return super.count(Wraps.<ProjectRole>lbQ()
                    .eq(ProjectRole::getCode, code)
                    .eq(ProjectRole::getTypeCode, typeCode)
                    .and(warp -> warp.eq(ProjectRole::getType, true).or().in(ProjectRole::getId, collect))) > 0;

        }

    }
    private void collectChildIds(Long parentId, Map<Long, List<ProjectMenuDic>> menuMap, Set<Long> idCollector) {
        if (!menuMap.containsKey(parentId))
        {
            // 终止条件：无子节点
            return;
        }

        for (ProjectMenuDic child : menuMap.get(parentId)) {
            // 添加当前子菜单ID
            idCollector.add(child.getId());
            // 递归子孙
            collectChildIds(child.getId(), menuMap, idCollector);
        }
    }

    @Override
    public List<Long> getPMUserIds(Long projectId)
    {
        //新增项目经理用户的id
        ProjectInfo projectInfo = projectInfoMapper.selectById(projectId);
        String pmRoleCode = projectInfo.getTypeCode() + PM_CODE;
        // 查询项目经理角色id
        ProjectRole projectRole = getOne(
                Wraps.<ProjectRole>lbQ().eq(ProjectRole::getCode, pmRoleCode).eq(ProjectRole::getReadonly, true)
                        .eq(ProjectRole::getType, true).eq(ProjectRole::getTypeCode, projectInfo.getTypeCode())
                        .last(" limit 1"));
        //查询用户信息
        return userRoleMapper.selectList(
                        Wraps.<ProjectUserRole>lbQ()
                                .eq(ProjectUserRole::getProjectId, projectId)
                                .eq(ProjectUserRole::getRoleId, projectRole.getId())
                )
                .stream().map(ProjectUserRole::getUserId).collect(Collectors.toList());
    }
}
