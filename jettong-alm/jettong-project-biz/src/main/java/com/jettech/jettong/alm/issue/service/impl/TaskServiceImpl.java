package com.jettech.jettong.alm.issue.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.repository.CacheOps;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.StringUtil;
import com.jettech.jettong.alm.issue.dao.StateMapper;
import com.jettech.jettong.alm.issue.dao.TaskMapper;
import com.jettech.jettong.alm.issue.dto.TaskCountQuery;
import com.jettech.jettong.alm.issue.dto.WorkItemDTO;
import com.jettech.jettong.alm.issue.entity.*;
import com.jettech.jettong.alm.issue.enumeration.ItemUserType;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.issue.service.TaskService;
import com.jettech.jettong.alm.issue.service.TaskUserService;
import com.jettech.jettong.alm.issue.service.TypeService;
import com.jettech.jettong.alm.issue.service.event.IssueUpdateEvent;
import com.jettech.jettong.alm.issue.vo.IssueTypeComponentResult;
import com.jettech.jettong.alm.issue.vo.TaskStateComponentResult;
import com.jettech.jettong.alm.project.dao.*;
import com.jettech.jettong.alm.project.entity.*;
import com.jettech.jettong.alm.project.service.ProjectRoleService;
import com.jettech.jettong.alm.project.service.ProjectWorkflowService;
import com.jettech.jettong.alm.project.service.ProjectWorkflowTransitionCheckService;
import com.jettech.jettong.alm.workflow.dao.WorkflowAuthorityMapper;
import com.jettech.jettong.alm.workflow.dao.WorkflowNodeMapper;
import com.jettech.jettong.alm.workflow.dao.WorkflowTransitionMapper;
import com.jettech.jettong.alm.workflow.entity.WorkflowAuthority;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;
import com.jettech.jettong.alm.workflow.entity.WorkflowTransition;
import com.jettech.jettong.alm.workflow.enumeration.WorkflowAuthorityType;
import com.jettech.jettong.alm.workflow.enumeration.WorkflowNodeType;
import com.jettech.jettong.base.api.FileApi;
import com.jettech.jettong.base.api.MsgApi;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.dto.msg.MsgDTO;
import com.jettech.jettong.base.dto.msg.MsgSaveDTO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.common.cache.alm.TypeCodePrefixCacheKeyBuilder;
import com.jettech.jettong.common.constant.MsgBizType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.jettech.jettong.common.constant.MsgConstant.*;

/**
 * 任务信息业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 任务信息业务层
 * @projectName jettong
 * @package com.jettech.jettong.alm.issue.service.impl
 * @className TaskServiceImpl
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class TaskServiceImpl extends SuperServiceImpl<TaskMapper, Task> implements TaskService
{
    private final CacheOps cacheOps;
    private final TypeService typeService;
    private final ProjectWorkflowService projectWorkflowService;
    private final ProjectWorkflowAuthorityMapper projectWorkflowAuthorityMapper;
    private final WorkflowAuthorityMapper workflowAuthorityMapper;
    private final WorkflowTransitionMapper workflowTransitionMapper;
    private final WorkflowNodeMapper workflowNodeMapper;
    private final ProjectUserRoleMapper projectUserRoleMapper;
    private final ProjectWorkflowTransitionHistoryMapper projectWorkflowTransitionHistoryMapper;
    private final StateMapper stateMapper;
    private final UserApi userApi;
    private final ProjectPlanMapper projectPlanMapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final FileApi fileApi;
    private final ProjectWorkflowTransitionCheckService transitionCheckService;
    private final ProjectRoleService projectRoleService;
    private final MsgApi msgApi;
    private final TaskUserService taskUserService;

    @Override
    public boolean saveBatch(Collection<Task> entityList, int batchSize) {
        boolean saveBatch = super.saveBatch(entityList, batchSize);
        // 推送 工作项改变事件
        entityList.forEach(e -> IssueUpdateEvent.sendEvent(TypeClassify.TASK, e.getId(), null, e));
        return saveBatch;
    }

    @Override
    public boolean save(Task task)
    {
        Type type = typeService.findByCode(task.getTypeCode());

        if (type == null)
        {
            throw BizException.validFail("获取事项类型失败,请联系管理员");
        }

        // 自动生成一个code
        task.setCode(typeService.getCode(type));

        // 查询任务的初始化状态
        WorkflowNode workflowNode = projectWorkflowService.findProjectFirstNode(task.getProjectId(), TypeClassify.TASK,
                task.getTypeCode());
        if (null == workflowNode)
        {
            throw BizException.validFail("获取该任务类型工作流失败，请联系管理员");
        }
        task.setStateCode(workflowNode.getStateCode());
        task.setRateProgress(workflowNode.getProgress());

        // 查询项目信息
        ProjectInfo projectInfo = projectInfoMapper.selectById(task.getProjectId());
        if (projectInfo == null)
        {
            throw BizException.validFail("创建失败,原因:获取项目信息失败");
        }
        task.setProgramId(projectInfo.getProgramId());

        boolean add = super.save(task);
        dealHandleBy(task);
        // 推送 工作项改变事件
        IssueUpdateEvent.sendEvent(TypeClassify.TASK, task.getId(), null, task);

        if (add)
        {
            // 修改附件
            List<File> files = task.getFiles();
            if (files != null && !files.isEmpty())
            {
                files.forEach(item -> item.setBizType(FileBizType.TASK_FILE_UPLOAD).setBizId(task.getId()));

                fileApi.updateBatchById(files);
            }

            // 给负责人发通知
            sendTaskNotify(
                    task.getLeadingBy(),
                    ContextUtil.getUserId(),
                    task,
                    null,  // 无旧任务
                    LEADER_BY,
                    TASK_CREATE_TEMPLATE,
                    TASK_CREATE_TITLE,
                    null,  // 无状态参数
                    null
            );

            // 给处理人发通知
            task.getHandleByList().forEach(handleBy ->sendTaskNotify(
                    handleBy,
                    ContextUtil.getUserId(),
                    task,
                    null,
                    HANDLE_BY,
                    TASK_CREATE_TEMPLATE,
                    TASK_CREATE_TITLE,
                    null,
                    null
            ));

        }
        return add;
    }

    /**
     * 获取任务的Code，从redis中获取
     *
     * @param type 工作项类型
     * @return String code
     * <AUTHOR>
     * @date 2021/11/12 9:42
     * @update zxy 2021/11/12 9:42
     * @since 1.0
     */
    private synchronized String getCode(Type type)
    {
        String prefix = type.getPrefix();
        CacheKey cacheKey = new TypeCodePrefixCacheKeyBuilder().key(prefix);
        Long codeNum = cacheOps.get(cacheKey, key -> {
            // 获取前缀相同的类型，防止相同前缀但不同类型的工作项 导致获取到值不是最大的bug
            Set<String> typeCodeSet = typeService.list(Wraps.<Type>lbQ().eq(Type::getPrefix, prefix))
                    .stream().map(Type::getCode).collect(Collectors.toSet());
            // 根据时间排序，获取最新一条数据的code
            String code = super.getObj(
                    Wraps.<Task>lbQ().select(Task::getCode)
                            .in(Task::getTypeCode, typeCodeSet)
                            .likeRight(Task::getCode, prefix)
                            .orderByDesc(Task::getCreateTime)
                            .last(" limit 1 "),
                    Convert::toStr);
            return StringUtil.getLongToStr(StringUtil.removePrefix(code, prefix));
        });
        codeNum++;
        cacheOps.set(cacheKey, codeNum);
        return prefix + codeNum;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<? extends Serializable> ids)
    {
        // 删除附件信息
        fileApi.removeByFileBizTypeAndBizIds(FileBizType.TASK_FILE_UPLOAD, ids);

        //删除状态流转历史
        projectWorkflowTransitionHistoryMapper.delete(Wraps.<ProjectWorkflowTransitionHistory>lbQ()
                .in(ProjectWorkflowTransitionHistory::getBizId, ids));

        boolean remove = super.removeByIds(ids);
        // 推送 工作项改变事件
        ids.forEach(id -> IssueUpdateEvent.sendEvent(TypeClassify.TASK, (Long) id, null, null));

        return remove;
    }

    @Override
    public boolean updateAllById(Task task)
    {
        Task oldTask = super.getById(task.getId());

        if (oldTask == null)
        {
            throw BizException.validFail("修改失败,原因:未查询到该任务");
        }

        List<File> oldFiles = fileApi.findByBizTypeAndBizId(FileBizType.TASK_FILE_UPLOAD, task.getId());

        List<Long> oldFileIds = oldFiles.stream().map(File::getId).collect(Collectors.toList());

        List<File> newFiles = task.getFiles();

        if (newFiles == null || newFiles.isEmpty())
        {
            if (!oldFileIds.isEmpty())
            {
                fileApi.removeByFileBizTypeAndBizIds(FileBizType.TASK_FILE_UPLOAD, oldFileIds);
            }
        }
        else
        {
            List<Long> newFileIds = newFiles.stream().map(File::getId).collect(Collectors.toList());

            if (!oldFileIds.isEmpty())
            {
                // 筛选出需要删除的数据
                List<Long> deleteFileIds =
                        oldFileIds.stream().filter(item -> !newFileIds.contains(item)).collect(Collectors.toList());
                if (!deleteFileIds.isEmpty())
                {
                    fileApi.removeByFileBizTypeAndBizIds(FileBizType.TASK_FILE_UPLOAD, deleteFileIds);
                }

            }
            newFiles.forEach(item -> item.setBizId(task.getId()).setBizType(FileBizType.TASK_FILE_UPLOAD));
            fileApi.updateBatchById(newFiles);
        }

        task.setCode(oldTask.getCode());
        task.setTypeCode(oldTask.getTypeCode());

        if (!task.getStateCode().equals(oldTask.getStateCode()))
        {
            transitionState(task.getId(), oldTask.getStateCode(), task.getStateCode());
        }
        else
        {
            task.setRateProgress(oldTask.getRateProgress());
            task.setEndTime(oldTask.getEndTime());
        }
        task.setCreateTime(oldTask.getCreateTime());
        task.setCreatedBy(oldTask.getCreatedBy());
        task.setUpdateTime(LocalDateTime.now());
        task.setUpdatedBy(ContextUtil.getUserId());
        dealHandleBy(task);
        boolean update = super.updateAllById(task);
        // 推送 工作项改变事件
        oldTask.setFiles(oldFiles);
        if (!task.getStateCode().equals(oldTask.getStateCode()))
        {
            IssueUpdateEvent.sendEvent(TypeClassify.TASK, task.getId(), oldTask, task);
        }


        // 给负责人发通知
        sendTaskNotify(
                task.getLeadingBy(),
                ContextUtil.getUserId(),
                task,
                oldTask,  // 传入旧任务（用于取code）
                LEADER_BY,
                TASK_UPDATE_TEMPLATE,
                TASK_UPDATE_TITLE,
                null,
                null
        );

        // 给处理人发通知
        task.getHandleByList().forEach(handleBy ->sendTaskNotify(
                task.getHandleBy(),
                ContextUtil.getUserId(),
                task,
                oldTask,
                HANDLE_BY,
                TASK_UPDATE_TEMPLATE,
                TASK_UPDATE_TITLE,
                null,
                null
        ));
        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void transitionState(Long taskId, String sourceStateCode, String targetStateCode)
    {
        // 查询当前任务节点id
        Task task = super.getById(taskId);
        if (null == task)
        {
            throw BizException.validFail("状态流转失败,原因:未查询到任务信息");
        }
        if (!task.getStateCode().equals(sourceStateCode))
        {
            throw BizException.validFail("状态流转失败,原因:任务状态已更新,请刷新重试");
        }

        // 查询任务使用的工作流
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(task.getProjectId(),
                        TypeClassify.TASK,
                        task.getTypeCode());
        if (null == projectWorkflow || null == projectWorkflow.getWorkflowId())
        {
            throw BizException.validFail("状态流转失败,原因:获取任务使用工作流失败");
        }

        // 获取最后一次流转记录
        ProjectWorkflowTransitionHistory projectWorkflowTransitionHistory =
                projectWorkflowTransitionHistoryMapper.selectOne(Wraps.<ProjectWorkflowTransitionHistory>lbQ()
                        .eq(ProjectWorkflowTransitionHistory::getBizId, taskId)
                        .eq(ProjectWorkflowTransitionHistory::getTypeCode, task.getTypeCode()).orderByDesc(
                                ProjectWorkflowTransitionHistory::getCreateTime).last(" limit 1"));

        Long sourceNodeId;
        LocalDateTime lastDateTime;
        // 如果最后一次流转记录为null，则使用任务的创建时间
        if (null == projectWorkflowTransitionHistory)
        {
            lastDateTime = task.getCreateTime();

            WorkflowNode workflowNode =
                    projectWorkflowService.findProjectFirstNode(task.getProjectId(), TypeClassify.TASK,
                            task.getTypeCode());
            if (workflowNode == null)
            {
                throw BizException.validFail("状态流转失败,原因:获取任务初始状态节点失败");
            }
            sourceNodeId = workflowNode.getId();
        }
        else
        {

            lastDateTime = projectWorkflowTransitionHistory.getCreateTime();
            sourceNodeId = projectWorkflowTransitionHistory.getTarget();
        }
        // 查询目标节点id
        WorkflowNode targetNode = workflowNodeMapper.selectOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, targetStateCode));
        if (null == targetNode)
        {
            throw BizException.validFail("状态流转失败,原因:获取任务流转到状态信息失败");
        }
        // 计算流转花费时间
        LocalDateTime now = LocalDateTime.now();
        long spendTime = ChronoUnit.MILLIS.between(lastDateTime, now);

        // 修改任务信息
        Task updateTask = Task.builder()
                .id(taskId)
                .stateCode(targetStateCode)
                .rateProgress(targetNode.getProgress())
                .build();
        if (WorkflowNodeType.END_NODE.eq(targetNode.getNodeType()))
        {
            updateTask.setEndTime(now);
            updateTask.setDelay(task.getPlanEtime() != null && now.isAfter(task.getPlanEtime()));
        }
        else if(WorkflowNodeType.INTERMEDIATE_NODE.eq(targetNode.getNodeType())&&task.getStartTime()==null)
        {
            updateTask.setStartTime(now);
        }
        super.updateById(updateTask);

        // 添加流转历史记录
        ProjectWorkflowTransitionHistory insertHistory = ProjectWorkflowTransitionHistory.builder()
                .bizId(taskId)
                .typeCode(task.getTypeCode())
                .source(sourceNodeId)
                .sourceStateCode(sourceStateCode)
                .target(targetNode.getId())
                .targetStateCode(targetStateCode)
                .spendTime(spendTime)
                .createTime(now)
                .systemFlow((ContextUtil.getUserId() == null || ContextUtil.getUserId() == 0L))
                .build();

        projectWorkflowTransitionHistoryMapper.insert(insertHistory);

        // 推送 工作项改变事件
        IssueUpdateEvent.sendEvent(TypeClassify.TASK, task.getId(),
                Bug.builder().id(task.getId()).stateCode(sourceStateCode).build(),
                Bug.builder().id(task.getId()).stateCode(targetStateCode).build()
        );
        List<String> codes = new ArrayList<>();
        codes.add(sourceStateCode);
        codes.add(targetStateCode);
        List<State> states = stateMapper.selectList(Wraps.<State>lbQ().in(State::getCode, codes));
        // 构建状态名称映射（code -> name）
        Map<String, String> stateCodeName = states.stream()
                .collect(Collectors.toMap(State::getCode, State::getName));
        String sourceStateName = stateCodeName.get(sourceStateCode);
        String targetStateName = stateCodeName.get(targetStateCode);



        // 给负责人发通知
        sendTaskNotify(
                task.getLeadingBy(),
                ContextUtil.getUserId(),
                task,
                null,
                LEADER_BY,
                TASK_TRANSFER_TEMPLATE,
                TASK_TRANSFER_TITLE,
                sourceStateName,
                targetStateName
        );

        // 给处理人发通知
        task.getHandleByList().forEach(handleBy ->sendTaskNotify(
                task.getHandleBy(),
                ContextUtil.getUserId(),
                task,
                null,
                HANDLE_BY,
                TASK_TRANSFER_TEMPLATE,
                TASK_TRANSFER_TITLE,
                sourceStateName,
                targetStateName
        ));

    }

    @Override
    public void calculationDelay()
    {
        baseMapper.calculationDelay();
    }

    @Override
    public List<WorkflowNode> findNextNode(Long taskId)
    {
        Task task = super.getById(taskId);
        if (null == task.getProjectId())
        {
            throw BizException.validFail("当前任务未关联项目，无法查询流转状态");
        }
        if (StrUtil.isEmpty(task.getTypeCode()))
        {
            throw BizException.validFail("当前任务没有任务类型，无法查询流转状态");
        }
        // 查询该任务使用的工作流

        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(task.getProjectId(),
                        TypeClassify.TASK,
                        task.getTypeCode());
        if (null == projectWorkflow || null == projectWorkflow.getWorkflowInfo())
        {
            throw BizException.validFail("当前任务未查询到工作流，无法查询可流转状态");
        }
        // 查询该任务的当前节点
        WorkflowNode thisWorkflowNode = workflowNodeMapper.selectOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, task.getStateCode()));

        if (null == thisWorkflowNode)
        {
            throw BizException.validFail("获取当前任务工作流状态节点失败");
        }

        // 查询可流转到的节点
        return getTargetNodeIds(task, projectWorkflow.getWorkflowId(), thisWorkflowNode.getId());
    }

    /**
     * 获取流转到的节点
     *
     * @param task 任务
     * @param workflowId 工作流id
     * @param thisNodeId 当前节点id
     * @return List<Long> 目标节点id
     * <AUTHOR>
     * @date 2021/11/15 13:01
     * @update zxy 2021/11/15 13:01
     * @since 1.0
     */
    private List<WorkflowNode> getTargetNodeIds(Task task, Long workflowId, Long thisNodeId)
    {
        List<WorkflowTransition> workflowTransitions = workflowTransitionMapper.selectList(
                Wraps.<WorkflowTransition>lbQ().eq(WorkflowTransition::getWorkflowId, workflowId)
                        .eq(WorkflowTransition::getSource, thisNodeId));

        if (workflowTransitions.isEmpty())
        {
            return Collections.emptyList();
        }
        boolean remove = true;
        List<Long> handleBy = taskUserService.findUserIdsByTaskId(task.getId());
        Iterator<WorkflowTransition> iterator = workflowTransitions.iterator();
        while (iterator.hasNext())
        {
            WorkflowTransition workflowTransition = iterator.next();
            // 查询流转权限
            List<ProjectWorkflowAuthority> projectWorkflowAuthorities = projectWorkflowAuthorityMapper.selectList(
                    Wraps.<ProjectWorkflowAuthority>lbQ().eq(ProjectWorkflowAuthority::getWorkflowId, workflowId)
                            .eq(ProjectWorkflowAuthority::getProjectId, task.getProjectId())
                            .eq(ProjectWorkflowAuthority::getTypeClassify, TypeClassify.TASK)
                            .eq(ProjectWorkflowAuthority::getTypeCode, task.getTypeCode())
                            .eq(ProjectWorkflowAuthority::getTransitionId, workflowTransition.getId()));

            List<WorkflowAuthority> workflowAuthorities;
            if (projectWorkflowAuthorities.isEmpty())
            {
                workflowAuthorities = workflowAuthorityMapper.selectList(
                        Wraps.<WorkflowAuthority>lbQ()
                                .eq(WorkflowAuthority::getTransitionId, workflowTransition.getId()));
            }
            else
            {
                workflowAuthorities = BeanPlusUtil.toBeanList(projectWorkflowAuthorities, WorkflowAuthority.class);
            }
            boolean authorityBreak = false;
            for (WorkflowAuthority workflowAuthority : workflowAuthorities)
            {
                switch (workflowAuthority.getType())
                {
                    case RESPONSIBLE:
                        if (ContextUtil.getUserId().equals(task.getLeadingBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case HANDLER:
                        if (handleBy.contains(ContextUtil.getUserId()));
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case USER:
                        if (ContextUtil.getUserId().equals(workflowAuthority.getUserOrRole()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case ROLE:
                        List<ProjectUserRole> userRoles =
                                projectUserRoleMapper.selectList(
                                        Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getRoleId)
                                                .eq(ProjectUserRole::getProjectId, task.getProjectId())
                                                .eq(ProjectUserRole::getUserId, ContextUtil.getUserId()));
                        Set<Long> roleIds =
                                userRoles.stream().map(ProjectUserRole::getRoleId).collect(Collectors.toSet());
                        if (roleIds.contains(workflowAuthority.getUserOrRole()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    default:
                        break;
                }
                if (authorityBreak)
                {
                    break;
                }
            }

            if (remove)
            {
                iterator.remove();
            }

        }

        if (workflowTransitions.isEmpty())
        {
            return Collections.emptyList();
        }
        return workflowTransitions.stream().map(transition -> {
            WorkflowNode workflowNode = workflowNodeMapper.selectById(transition.getTarget());

            List<ProjectWorkflowTransitionCheck> checkList = transitionCheckService.findCheckList(transition.getId(),
                    task.getProjectId(), TypeClassify.TASK, task.getTypeCode());
            workflowNode.getEchoMap().put("check", checkList);
            return workflowNode;
        }).collect(Collectors.toList());
    }

    @Override
    public IssueTypeComponentResult findTypeByProjectId(Long projectId)
    {
        // 根据项目id查询所有任务
        List<Task> tasks = baseMapper.selectList(Wraps.<Task>lbQ().eq(Task::getProjectId, projectId));

        // 查询所有状态
        List<State> states = stateMapper.selectList(Wraps.lbQ());

        // 按code和name分组
        Map<String, State> codeNameMap = states.stream().collect(Collectors.toMap(State::getCode, state -> state));

        Map<String, List<Task>> groupStateCodeTasks = tasks.stream().collect(Collectors.groupingBy(Task::getStateCode));

        List<IssueTypeComponentResult.Pie> data = new LinkedList<>();

        for (Map.Entry<String, List<Task>> entry : groupStateCodeTasks.entrySet())
        {
            data.add(IssueTypeComponentResult.Pie.builder()
                    .name(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getName() :
                            entry.getKey())
                    .color(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getColor() :
                            entry.getKey())
                    .code(entry.getKey())
                    .value(entry.getValue().size()).build());
        }

        return IssueTypeComponentResult.builder().count(tasks.size()).data(data).build();
    }

    @Override
    public TaskStateComponentResult findStateByProjectId(Long projectId)
    {
        // 根据项目id查询所有有执行人的任务
        List<Task> tasks =
                baseMapper.selectList(Wraps.<Task>lbQ().eq(Task::getProjectId, projectId).isNotNull(Task::getHandleBy));

        if (tasks.isEmpty()) {
            return TaskStateComponentResult.builder().build();
        }

        Set<String> stateCodes = tasks.stream().map(Task::getStateCode).collect(Collectors.toSet());

        // 查询所有任务状态
        // 获取项目任务工作流
       List<State> states = stateMapper.selectList(Wraps.<State>lbQ().in(State::getCode, stateCodes));

        // 获取项目成员
        Set<Serializable> handleBys =
                projectUserRoleMapper.selectList(Wraps.<ProjectUserRole>lbQ().eq(ProjectUserRole::getProjectId, projectId)).stream()
                        .map(ProjectUserRole::getUserId).collect(Collectors.toSet());

        // 查询任务人员信息
        Map<Serializable, Object> nameMap = userApi.findByIds(handleBys);
        List<User> userList = nameMap.values().stream()
                .map(item -> BeanPlusUtil.toBean(item, User.class))
                .collect(Collectors.toList());

        List<String> names = userList.stream().map(User::getName).collect(Collectors.toList());
        List<Long> ids = userList.stream().map(User::getId).collect(Collectors.toList());

        List<TaskStateComponentResult.Bar> data = new LinkedList<>();
        for (State state : states)
        {
            TaskStateComponentResult.Bar dataItem = new TaskStateComponentResult.Bar();

            dataItem.setName(state.getName());
            dataItem.setColor(state.getColor());
            dataItem.setCode(state.getCode());
            List<Integer> taskNumData = new LinkedList<>();
            for (User handleBy : userList)
            {
                String userId = String.valueOf(handleBy.getId());
                //筛选状态和处理人条数
                Integer count = (int) tasks.stream()
                        .filter(task -> Objects.equals(userId, String.valueOf(task.getHandleBy())))
                        .filter(task -> Objects.equals(task.getStateCode(), state.getCode()))
                        .count();
                taskNumData.add(count);
            }

            dataItem.setData(taskNumData);

            data.add(dataItem);
        }

        return TaskStateComponentResult.builder().id(ids).name(names).data(data).build();
    }

    @Override
    public IssueTypeComponentResult findTaskComponentByProductId(Long productId)
    {
        // 根据产品id查询所有任务
        List<Task> requirements = super.list(Wraps.<Task>lbQ().eq(Task::getProductId, productId));
// 获取项目任务工作流
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(null,
                        TypeClassify.TASK, null);
        if (projectWorkflow == null || projectWorkflow.getWorkflowId() == null)
        {
            throw BizException.validFail("初始化数据异常，请联系管理员");
        }
        List<WorkflowNode> workflowNodes = workflowNodeMapper.selectList(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId()));
        List<String> stateCodes = workflowNodes.stream().map(WorkflowNode::getStateCode).collect(Collectors.toList());
        List<State> states = stateMapper.selectList(Wraps.<State>lbQ().in(State::getCode, stateCodes));

        // 按code和name分组
        Map<String, State> codeNameMap = states.stream().collect(Collectors.toMap(State::getCode, state -> state));

        Map<String, List<Task>> groupStateCodeRequirements =
                requirements.stream().collect(Collectors.groupingBy(Task::getStateCode));

        List<IssueTypeComponentResult.Pie> data = new LinkedList<>();

        for (Map.Entry<String, List<Task>> entry : groupStateCodeRequirements.entrySet())
        {
            data.add(IssueTypeComponentResult.Pie.builder()
                    .name(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getName() :
                            entry.getKey())
                    .color(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getColor() :
                            entry.getKey())
                    .code(entry.getKey())
                    .value(entry.getValue().size()).build());
        }

        return IssueTypeComponentResult.builder().count(requirements.size()).data(data).build();
    }

    @Override
    public IssueTypeComponentResult findTaskComponentByPlanId(Long planId)
    {
        // 根据计划id查询所有任务
        List<Task> requirements = super.list(Wraps.<Task>lbQ().eq(Task::getPlanId, planId));

        ProjectPlan projectPlan = projectPlanMapper.selectById(planId);
        if (projectPlan == null || projectPlan.getProjectId() == null)
        {
            throw BizException.validFail("初始化数据异常，请联系管理员");
        }
        // 查询所有状态
        // 获取项目任务工作流
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(projectPlan.getProjectId(),
                        TypeClassify.TASK, null);
        if (projectWorkflow == null || projectWorkflow.getWorkflowId() == null)
        {
            throw BizException.validFail("初始化数据异常，请联系管理员");
        }
        List<WorkflowNode> workflowNodes = workflowNodeMapper.selectList(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId()));
        List<String> stateCodes = workflowNodes.stream().map(WorkflowNode::getStateCode).collect(Collectors.toList());
        List<State> states = stateMapper.selectList(Wraps.<State>lbQ().in(State::getCode, stateCodes));

        // 按code和name分组
        Map<String, State> codeNameMap = states.stream().collect(Collectors.toMap(State::getCode, state -> state));

        Map<String, List<Task>> groupStateCodeRequirements =
                requirements.stream().collect(Collectors.groupingBy(Task::getStateCode));

        List<IssueTypeComponentResult.Pie> data = new LinkedList<>();

        for (Map.Entry<String, List<Task>> entry : groupStateCodeRequirements.entrySet())
        {
            data.add(IssueTypeComponentResult.Pie.builder()
                    .name(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getName() :
                            entry.getKey())
                    .color(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getColor() :
                            entry.getKey())
                    .code(entry.getKey())
                    .value(entry.getValue().size()).build());
        }

        return IssueTypeComponentResult.builder().count(requirements.size()).data(data).build();
    }

    @Override
    public TaskStateComponentResult findStateByPlanId(Long planId)
    {
        // 根据计划id查询所有有执行人的任务
        List<Task> tasks =
                baseMapper.selectList(Wraps.<Task>lbQ().eq(Task::getPlanId, planId).isNotNull(Task::getHandleBy));

        ProjectPlan projectPlan = projectPlanMapper.selectById(planId);
        if (projectPlan == null || projectPlan.getProjectId() == null)
        {
            throw BizException.validFail("获取迭代信息失败");
        }
        // 查询所有状态
        // 获取项目任务工作流
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(projectPlan.getProjectId(),
                        TypeClassify.TASK, null);
        if (projectWorkflow == null || projectWorkflow.getWorkflowId() == null)
        {
            throw BizException.validFail("初始化数据异常，请联系管理员");
        }
        List<WorkflowNode> workflowNodes = workflowNodeMapper.selectList(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId()));
        Set<String> stateCodes = workflowNodes.stream().map(WorkflowNode::getStateCode).collect(Collectors.toSet());

        // 工作流可能不同，将所有的状态都加上
        stateCodes.addAll(tasks.stream().map(Task::getStateCode).collect(Collectors.toSet()));

        List<State> states = stateMapper.selectList(Wraps.<State>lbQ().in(State::getCode, stateCodes));

        // 获取成员
        Set<Serializable> handleBys = tasks.stream().map(Task::getHandleBy).collect(Collectors.toSet());

        // 查询任务人员信息
        Map<Serializable, Object> nameMap = userApi.findByIds(handleBys);
        List<String> name = new LinkedList<>();
        for (Map.Entry<Serializable, Object> entry : nameMap.entrySet())
        {
            User user = BeanPlusUtil.toBean(entry.getValue(), User.class);
            name.add(user.getName());
        }

        List<TaskStateComponentResult.Bar> data = new LinkedList<>();
        for (State state : states)
        {
            TaskStateComponentResult.Bar dataItem = new TaskStateComponentResult.Bar();

            dataItem.setName(state.getName());
            dataItem.setColor(state.getColor());
            dataItem.setCode(state.getCode());
            List<Integer> taskNumData = new LinkedList<>();
            for (Serializable handleBy : handleBys)
            {
                //筛选状态和处理人条数
                Integer count = (int) tasks.stream().filter(task -> handleBy.equals(task.getHandleBy()) &&
                        state.getCode().equals(task.getStateCode())).count();
                taskNumData.add(count);
            }

            dataItem.setData(taskNumData);

            data.add(dataItem);
        }

        return TaskStateComponentResult.builder().name(name).data(data).build();
    }

    @Override
    public Page<Task> findPageByQuery(IPage<Task> page, TaskCountQuery query)
    {
        return baseMapper.queryPageByQuery(page, query);
    }

    @Override
    public Page<WorkItemDTO> findHourPageByQuery(IPage<WorkItemDTO> page, TaskCountQuery query)
    {
        return baseMapper.queryHourPageByQuery(page, query);
    }

    /**
     * 获取具有流转权限角色的用户id
     * @param task
     * @return {@link List< Long>}
     * @throws
     * <AUTHOR>
     * @date 2025/8/7 10:50
     * @update sxh 2025/8/7 10:50
     * @since 1.0
     */
    @Override
    public List<Long> getWorkFlowUserIds(Task task) {
        List<Long> allUserIds = new ArrayList<>();
        // 查询该任务使用的工作流
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(task.getProjectId(),
                        TypeClassify.TASK,
                        task.getTypeCode());
        if (null == projectWorkflow || null == projectWorkflow.getWorkflowInfo()) {
            throw BizException.validFail("当前任务未查询到工作流，无法查询可流转状态");
        }
        // 查询该任务的当前节点
        WorkflowNode thisWorkflowNode = workflowNodeMapper.selectOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, task.getStateCode()));

        if (null == thisWorkflowNode) {
            throw BizException.validFail("获取当前任务工作流状态节点失败");
        }
        // 获取流转到的节点
        List<WorkflowTransition> workflowTransitions = workflowTransitionMapper.selectList(
                Wraps.<WorkflowTransition>lbQ().eq(WorkflowTransition::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowTransition::getSource, thisWorkflowNode.getId()));

        if (workflowTransitions.isEmpty()) {
            return Collections.emptyList();
        }
        Iterator<WorkflowTransition> iterator = workflowTransitions.iterator();
        while (iterator.hasNext()) {
            WorkflowTransition workflowTransition = iterator.next();
            // 查询流转权限
            List<ProjectWorkflowAuthority> projectWorkflowAuthorities = projectWorkflowAuthorityMapper.selectList(
                    Wraps.<ProjectWorkflowAuthority>lbQ().eq(ProjectWorkflowAuthority::getWorkflowId, projectWorkflow.getWorkflowId())
                            .eq(ProjectWorkflowAuthority::getProjectId, task.getProjectId())
                            .eq(ProjectWorkflowAuthority::getTypeClassify, TypeClassify.TASK)
                            .eq(ProjectWorkflowAuthority::getTypeCode, task.getTypeCode())
                            .eq(ProjectWorkflowAuthority::getTransitionId, workflowTransition.getId())
                            .eq(ProjectWorkflowAuthority::getType, WorkflowAuthorityType.ROLE));


            if (projectWorkflowAuthorities.size() != 0) {
                List<WorkflowAuthority> workflowAuthorities = BeanPlusUtil.toBeanList(projectWorkflowAuthorities, WorkflowAuthority.class);
                for(WorkflowAuthority workflowAuthority:workflowAuthorities){
                    List<ProjectUserRole> projectUserRoles =
                            projectUserRoleMapper.selectList(
                                    Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getUserId)
                                            .eq(ProjectUserRole::getProjectId, task.getProjectId())
                                            .eq(ProjectUserRole::getRoleId, workflowAuthority.getUserOrRole()));
                    List<Long> userIds =
                            projectUserRoles.stream().map(ProjectUserRole::getUserId).collect(Collectors.toList());
                    allUserIds.addAll(userIds);
                }

            }

            List<WorkflowAuthority> workflowAuthorities = workflowAuthorityMapper.selectList(
                    Wraps.<WorkflowAuthority>lbQ()
                            .eq(WorkflowAuthority::getTransitionId, workflowTransition.getId())
                            .eq(WorkflowAuthority::getType, WorkflowAuthorityType.ROLE));
            for(WorkflowAuthority workflowAuthority:workflowAuthorities){
                List<ProjectUserRole> projectUserRoles =
                        projectUserRoleMapper.selectList(
                                Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getUserId)
                                        .eq(ProjectUserRole::getProjectId, task.getProjectId())
                                        .eq(ProjectUserRole::getRoleId, workflowAuthority.getUserOrRole()));
                List<Long> userIds =
                        projectUserRoles.stream().map(ProjectUserRole::getUserId).collect(Collectors.toList());
                allUserIds.addAll(userIds);
            }

        }
        return allUserIds;
    }



    @Override
    public Task getById(Serializable id)
    {
        Task task = super.getById(id);
        if (task != null)
        {
            task.setFiles(fileApi.findByBizTypeAndBizId(FileBizType.TASK_FILE_UPLOAD, id));
        }
        taskUserService.actionHandleBy( task);
        return task;
    }

    public void dealHandleBy(Task task)
    {
        taskUserService.remove(Wraps.<TaskUser>lbQ().eq(TaskUser::getTaskId, task.getId()));
        if (CollUtil.isNotEmpty(task.getHandleByList())){
            if (task.getHandleByList().contains(ContextUtil.getUserId())){
                task.getHandleByList().remove(ContextUtil.getUserId());
                task.getHandleByList().add(0, ContextUtil.getUserId());
                task.setHandleBy(task.getHandleByList().get(0));
            }
            taskUserService.saveBatch(task.getHandleByList().stream().distinct().map(userId -> TaskUser.builder()
                    .taskId(task.getId())
                    .userId(userId)
                    .type(ItemUserType.HANDLE)
                    .build()).collect(Collectors.toList()));
        }else if (task.getHandleBy() != null){
            taskUserService.save(TaskUser.builder()
                    .taskId(task.getId())
                    .userId(task.getHandleBy())
                    .type(ItemUserType.HANDLE)
                    .build());
            task.setHandleByList(Arrays.asList(task.getHandleBy()));
        }
    }

    private void sendTaskNotify(
            Long targetUserId,
            Long operatorId,
            Task task,
            Task oldTask,
            String roleDesc,
            String contentTemplate,
            String title,
            String sourceStateName,
            String targetStateName) {

        // 校验：接收人不为空且不是操作人本人
        if (targetUserId == null || targetUserId.equals(operatorId)) {
            return;
        }

        // 1. 构建操作人字符串（处理用户名空的情况）
        String userString = buildUserString();

        // 2. 构建跳转字符串（变更场景用旧code，其他场景用新code）
        String code = (oldTask != null) ? oldTask.getCode() : task.getCode();
        String jumpString = DetectionMessageContent.buildJumpString(
                code,
                task.getName(),
                task.getId(),
                TypeClassify.TASK,
                task.getProjectId(),
                task.getTypeCode()
        );

        // 3. 构建通知内容（根据场景填充模板）
        String notifyContent;
        if (sourceStateName != null && targetStateName != null) {
            // 状态流转场景（需要源状态和目标状态）
            notifyContent = String.format(contentTemplate, userString, roleDesc, jumpString, sourceStateName, targetStateName);
        } else {
            // 创建/变更场景（无需状态参数）
            notifyContent = String.format(contentTemplate, userString, roleDesc, jumpString);
        }

        // 4. 构建消息对象并发送
        MsgDTO msgDTO = MsgDTO.buildNotify(MsgBizType.TASK_NOTIFY, task.getId(), title, notifyContent);
        msgDTO.setAuthorId(operatorId);

        Set<Long> userIds = Collections.singleton(targetUserId);
        MsgSaveDTO msgSaveDTO = MsgSaveDTO.builder()
                .userIdList(userIds)
                .msgDTO(msgDTO)
                .build();
        msgApi.save(msgSaveDTO);
    }

    /**
     * 构建操作人字符串（处理用户名为空的情况）
     */
    private String buildUserString() {
        if (StringUtil.isEmpty(ContextUtil.getUserName())) {
            return "系统";
        } else {
            return String.format("%s(%s)", ContextUtil.getUserName(), ContextUtil.getUserAccount());
        }
    }
}
