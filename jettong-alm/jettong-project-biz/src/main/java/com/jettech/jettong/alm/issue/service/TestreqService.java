package com.jettech.jettong.alm.issue.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.base.service.SuperService;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.model.LoadService;
import com.jettech.jettong.alm.issue.dto.TestreqCorrelatedIdeaDTO;
import com.jettech.jettong.alm.issue.entity.Testreq;
import com.jettech.jettong.alm.issue.vo.TestreqViewResultVo;
import com.jettech.jettong.alm.issue.vo.IssueGroupVO;
import com.jettech.jettong.alm.issue.vo.IssueTypeComponentResult;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;

import java.util.List;
import java.util.Map;

/**
 * 测试需求信息业务接口
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong1
 * @package com.jettech.jettong.alm.issue.dto
 * @className aa
 * @date 2025/7/7 18:38
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface TestreqService extends SuperService<Testreq>, LoadService {
    void transitionState(Long testreqId, String sourceStateCode, String targetStateCode);
    void calculationDelay();
    List<WorkflowNode> findNextNode(Long testreqId);
//    IssueTypeComponentResult findTestreqComponentByProductId(Long productId);
//    IssueTypeComponentResult findTestreqComponentByPlanId(Long planId);
//    Map<String, Object> showIssueTopology(Long testreqId);
//    Map<String, Object> showTestreqTopology(Long testreqId);
//    Map<String, Object> showPlanTopology(Long testreqId, List<String> issueCodeList);
//    Map<String, Object> showTestmTopology(Long testreqId, List<String> issueCodeList);
//    Map<String, Object> showDevelopmentTopology(Long testreqId, List<String> issueCodeList);
    List<Testreq> selectTestreqByIdeaIdOrTestreqId(Testreq testreq);

    /**
     * 查询具有流转权限角色的用户id
     * @param oldTestreq
     * @return {@link List< Long>}
     * @throws
     * <AUTHOR>
     * @date 2025/8/7 15:15
     * @update sxh 2025/8/7 15:15
     * @since 1.0
     */
    List<Long> getWorkFlowUserIds(Testreq oldTestreq);
//    void correlatedIdea(TestreqCorrelatedIdeaDTO model);
//    List<TestreqViewResultVo> getTestreqGroupByProductIdAndProductVersionId(Long productId, Long productVersionId, TestreqViewResultVo vo);

    /**
     * 根据项目id查询需求概览信息
     *
     * @param projectId 项目id
     * @return IssueTypeComponentResult 需求概览信息
     * <AUTHOR>
     * @date 2021/12/2 17:57
     * @update zxy 2021/12/2 17:57
     * @since 1.0
     */
    IssueTypeComponentResult findTypeByProjectId(Long projectId);
}
