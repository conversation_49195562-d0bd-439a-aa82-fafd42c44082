package com.jettech.jettong.alm.project.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.alm.issue.dto.StateTransitionDTO;
import com.jettech.jettong.alm.issue.entity.Type;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.issue.service.TypeService;
import com.jettech.jettong.alm.project.dao.ProjectWorkflowAuthorityMapper;
import com.jettech.jettong.alm.project.entity.*;
import com.jettech.jettong.alm.project.service.*;
import com.jettech.jettong.alm.workflow.dto.CountersignResultDTO;
import com.jettech.jettong.alm.workflow.dto.WorkflowCountersignRecordDTO;
import com.jettech.jettong.alm.workflow.dto.WorkflowItemDTO;
import com.jettech.jettong.alm.workflow.dto.WorkflowTransitionHistoryDTO;
import com.jettech.jettong.alm.workflow.entity.*;
import com.jettech.jettong.common.enumeration.CountersignResult;
import com.jettech.jettong.common.enumeration.CountersignStatus;
import com.jettech.jettong.alm.workflow.enumeration.WorkflowNodeType;
import com.jettech.jettong.alm.workflow.service.*;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 工作流处理逻辑实现类
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.alm.project.service.impl
 * @className WorkflowLogicImpl
 * @date 2025/9/9 15:35
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@AllArgsConstructor
@Component
public class WorkflowLogicImpl implements WorkflowLogic
{

    private final TypeService typeService;
    private final WorkflowInfoService workflowInfoService;
    private final WorkflowNodeService workflowNodeService;
    private final WorkflowTransitionService workflowTransitionService;
    private final ProjectWorkflowService projectWorkflowService;
    private final ProjectWorkflowAuthorityMapper projectWorkflowAuthorityMapper;
    private final WorkflowAuthorityService workflowAuthorityService;
    private final ProjectUserRoleService projectUserRoleService;
    private final ProjectWorkflowTransitionCheckService projectWorkflowTransitionCheckService;
    private final ProjectWorkflowTransitionHistoryService projectWorkflowTransitionHistoryService;
    private final WorkflowCountersignInstanceService workflowCountersignInstanceService;
    private final WorkflowCountersignRecordService workflowCountersignRecordService;

    @Override
    public String findStartStateCode(WorkflowItemDTO workItem)
    {
        Type type = typeService.findByCode(workItem.getTypeCode());
        ProjectWorkflow projectWorkflow = projectWorkflowService.getProjectWorkflow(workItem.getProjectId(),
                type.getClassify(), workItem.getTypeCode());
        if (null == projectWorkflow)
        {
            throw BizException.validFail("当前未查询到工作流，无法查询流转状态");
        }
        // 查询当前节点
        WorkflowNode workflowNode = workflowNodeService.getOne(Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowNode::getNodeType, WorkflowNodeType.START_NODE));
        if (workflowNode != null){
            return workflowNode.getStateCode();
        }
        return null;
    }

    public List<WorkflowNode> findNextNode(WorkflowItemDTO workItem)
    {
        Type type = typeService.findByCode(workItem.getTypeCode());
        ProjectWorkflow projectWorkflow = projectWorkflowService.getProjectWorkflow(workItem.getProjectId(),
                type.getClassify(), workItem.getTypeCode());
        if (null == projectWorkflow)
        {
            throw BizException.validFail("当前未查询到工作流，无法查询流转状态");
        }
        WorkflowInfo workflowInfo = workflowInfoService.getAllById(projectWorkflow.getWorkflowId());
        if (null == workflowInfo)
        {
            throw BizException.validFail("当前未查询到工作流，无法查询流转状态");
        }
        // 查询当前节点
        WorkflowNode thisWorkflowNode = workflowNodeService.getOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, workflowInfo.getId())
                        .eq(WorkflowNode::getStateCode, workItem.getStateCode()));

        if (null == thisWorkflowNode)
        {
            throw BizException.validFail("获取当前缺陷工作流状态节点失败");
        }

        // 查询可流转到的节点
        List<WorkflowTransition> workflowTransitions = workflowTransitionService.list(
                Wraps.<WorkflowTransition>lbQ().eq(WorkflowTransition::getWorkflowId, workflowInfo.getId()).eq(WorkflowTransition::getSource, thisWorkflowNode.getId()));

        if (workflowTransitions.isEmpty())
        {
            return Collections.emptyList();
        }
        //会签人员也是处理人
        List<Long> countersignUserIds = workflowCountersignRecordService.list(Wraps.<WorkflowCountersignRecord>lbQ().eq(WorkflowCountersignRecord::getNodeId, thisWorkflowNode.getId())
                        .eq(WorkflowCountersignRecord::getInstanceId, workItem.getInstanceId()).select(WorkflowCountersignRecord::getUserId))
                .stream().map(WorkflowCountersignRecord::getUserId).distinct().collect(Collectors.toList());

        Iterator<WorkflowTransition> iterator = workflowTransitions.iterator();
        while (iterator.hasNext())
        {
            WorkflowTransition workflowTransition = iterator.next();
            List<WorkflowAuthority> workflowAuthorities;

            // 查询流转权限
            if (workItem.getProjectId() != null){
                //优先项目权限
                List<ProjectWorkflowAuthority> projectWorkflowAuthorities = projectWorkflowAuthorityMapper.selectList(
                        Wraps.<ProjectWorkflowAuthority>lbQ().eq(ProjectWorkflowAuthority::getWorkflowId, workflowInfo.getId())
                                .eq(ProjectWorkflowAuthority::getProjectId, workItem.getProjectId())
                                .eq(ProjectWorkflowAuthority::getTypeClassify, workItem.getBizType())
                                .eq(ProjectWorkflowAuthority::getTypeCode, workItem.getTypeCode())
                                .eq(ProjectWorkflowAuthority::getTransitionId, workflowTransition.getId()));
                if (projectWorkflowAuthorities.isEmpty())
                {
                    workflowAuthorities = workflowAuthorityService.list(
                            Wraps.<WorkflowAuthority>lbQ().eq(WorkflowAuthority::getTransitionId, workflowTransition.getId()));
                }
                else
                {
                    workflowAuthorities = BeanPlusUtil.toBeanList(projectWorkflowAuthorities, WorkflowAuthority.class);
                }
            } else {
                workflowAuthorities = workflowAuthorityService.list(
                        Wraps.<WorkflowAuthority>lbQ().eq(WorkflowAuthority::getTransitionId, workflowTransition.getId()));
            }


            boolean remove = true;
            boolean authorityBreak = false;
            for (WorkflowAuthority workflowAuthority : workflowAuthorities)
            {
                switch (workflowAuthority.getType())
                {
                    case RESPONSIBLE:
                        if (ContextUtil.getUserId().equals(workItem.getLeadingBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case HANDLER:
                        if (ContextUtil.getUserId().equals(workItem.getHandleBy())||countersignUserIds.contains(ContextUtil.getUserId()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case CREATOR:
                        if (ContextUtil.getUserId().equals(workItem.getPutBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case USER:
                        if (ContextUtil.getUserId().equals(workflowAuthority.getUserOrRole()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case ROLE:
                        if (workItem.getProjectId()!=null){
                            List<ProjectUserRole> userRoles =
                                    projectUserRoleService.list(
                                            Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getRoleId)
                                                    .eq(ProjectUserRole::getProjectId, workItem.getProjectId())
                                                    .eq(ProjectUserRole::getUserId, ContextUtil.getUserId()));
                            Set<Long> roleIds =
                                    userRoles.stream().map(ProjectUserRole::getRoleId).collect(Collectors.toSet());
                            if (roleIds.contains(workflowAuthority.getUserOrRole()))
                            {
                                remove = false;
                                authorityBreak = true;
                            }
                            break;
                        }

                    default:
                        break;
                }
                if (authorityBreak)
                {
                    break;
                }
            }

            if (remove)
            {
                iterator.remove();
            }

        }

        if (workflowTransitions.isEmpty())
        {
            return Collections.emptyList();
        }

        return workflowTransitions.stream().map(transition -> {
            WorkflowNode workflowNode = workflowNodeService.getById(transition.getTarget());

            List<ProjectWorkflowTransitionCheck> checkList = projectWorkflowTransitionCheckService.findCheckList(transition.getId(),
                    workItem.getProjectId(), type.getClassify(), workItem.getTypeCode());
            workflowNode.getEchoMap().put("check", checkList);
            workflowNode.setType(transition.getType());
            return workflowNode;
        }).collect(Collectors.toList());
    }

    @Override
    public List<WorkflowCountersignRecord> findLastCountersignRecord(Long instanceId)
    {
        List result = new ArrayList<>();
        WorkflowCountersignRecord agree = workflowCountersignRecordService.getOne(
                Wraps.<WorkflowCountersignRecord>lbQ().eq(WorkflowCountersignRecord::getInstanceId, instanceId)
                        .eq(WorkflowCountersignRecord::getStatus, CountersignStatus.COMPLETED)
                        .eq(WorkflowCountersignRecord::getResult, CountersignResult.AGREE)
                        .orderByDesc(WorkflowCountersignRecord::getUpdateTime).last(" limit 1"));
        if (null != agree){
            result.add(agree);
        }
        WorkflowCountersignRecord reject = workflowCountersignRecordService.getOne(
                Wraps.<WorkflowCountersignRecord>lbQ().eq(WorkflowCountersignRecord::getInstanceId, instanceId)
                        .eq(WorkflowCountersignRecord::getStatus, CountersignStatus.COMPLETED)
                        .eq(WorkflowCountersignRecord::getResult, CountersignResult.REJECT)
                        .orderByDesc(WorkflowCountersignRecord::getUpdateTime).last(" limit 1"));
        if (null != reject){
            result.add(reject);
        }
        return result;
    }


    public List<WorkflowNode> findNextNodeByWorkflowIdAndLeadingBy(Long workFlowId, Long leadingBy, String sourceStateCode)
    {
        // 查询该缺陷的当前节点
        WorkflowNode thisWorkflowNode = workflowNodeService.getOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, workFlowId)
                        .eq(WorkflowNode::getStateCode, sourceStateCode));

        if (null == thisWorkflowNode)
        {
            throw BizException.validFail("获取当前工作流状态节点失败");
        }

        // 查询可流转到的节点
        return getTargetNodeIds(leadingBy, workFlowId, thisWorkflowNode.getId());
    }


    private List<WorkflowNode> getTargetNodeIds(Long leadingBy, Long workflowId, Long thisNodeId)
    {
        List<WorkflowTransition> workflowTransitions = workflowTransitionService.list(
                Wraps.<WorkflowTransition>lbQ().eq(WorkflowTransition::getWorkflowId, workflowId)
                        .eq(WorkflowTransition::getSource, thisNodeId));

        if (workflowTransitions.isEmpty())
        {
            return Collections.emptyList();
        }

        Iterator<WorkflowTransition> iterator = workflowTransitions.iterator();
        while (iterator.hasNext())
        {
            WorkflowTransition workflowTransition = iterator.next();
            // 默认只有负责人能流转
            if (!ContextUtil.getUserId().equals(leadingBy) && leadingBy != 0L)
            {
                iterator.remove();
            }
        }

        if (workflowTransitions.isEmpty())
        {
            return Collections.emptyList();
        }

        List<WorkflowNode> workflowNodes = workflowTransitions.stream().map(transition ->
        {
            WorkflowNode workflowNode = workflowNodeService.getById(transition.getTarget());
            return workflowNode;
        }).collect(Collectors.toList());
        SpringUtil.getBean(EchoService.class).action(workflowNodes);
        return workflowNodes;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean transitionState(StateTransitionDTO stateTransition)
    {

        if (stateTransition.getWorkflowId()==null){
            ProjectWorkflow projectWorkflow = projectWorkflowService.getProjectWorkflow(stateTransition.getProjectId(),
                    stateTransition.getBizType(), stateTransition.getTypeCode());
            if (null == projectWorkflow){
                throw BizException.validFail("事项类型不存在");
            }
            stateTransition.setWorkflowId(projectWorkflow.getWorkflowId());
        }

        // 获取最后一次流转记录
        ProjectWorkflowTransitionHistory projectWorkflowTransitionHistory =
                projectWorkflowTransitionHistoryService.getOne(Wraps.<ProjectWorkflowTransitionHistory>lbQ()
                        .eq(ProjectWorkflowTransitionHistory::getBizId, stateTransition.getBizId())
                        .eq(ProjectWorkflowTransitionHistory::getTypeCode, stateTransition.getTypeCode()).orderByDesc(
                                ProjectWorkflowTransitionHistory::getCreateTime).last(" limit 1"));

        Long sourceNodeId;
        LocalDateTime lastDateTime;
        // 如果最后一次流转记录为null，则使用的创建时间
        if (null == projectWorkflowTransitionHistory)
        {
            lastDateTime = stateTransition.getCreateTime();

            WorkflowNode workflowNode =
                    workflowNodeService.getOne(Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, stateTransition.getWorkflowId())
                            .eq(WorkflowNode::getNodeType, WorkflowNodeType.START_NODE));
            if (workflowNode == null)
            {
                throw BizException.validFail("状态流转失败,原因:获取初始状态节点失败");
            }
            sourceNodeId = workflowNode.getId();
        }
        else
        {
            lastDateTime = projectWorkflowTransitionHistory.getCreateTime();
            sourceNodeId = projectWorkflowTransitionHistory.getTarget();
        }
        WorkflowNode workflowNode = workflowNodeService.getById(sourceNodeId);
        //会签判断
        if (workflowNode!=null&&workflowNode.isNeedCountersign()&&projectWorkflowTransitionHistory!=null){
            if (stateTransition.isSkipCountersign()){
                //跳过会签。把未完成的更新状态
                workflowCountersignInstanceService.update(Wraps.<WorkflowCountersignInstance>lbU()
                        .eq(WorkflowCountersignInstance::getWorkflowId, workflowNode.getWorkflowId())
                        .eq(WorkflowCountersignInstance::getNodeId, workflowNode.getId())
                        .eq(WorkflowCountersignInstance::getHistoryId, projectWorkflowTransitionHistory.getId())
                        .ne(WorkflowCountersignInstance::getStatus, CountersignStatus.COMPLETED)
                        .set(WorkflowCountersignInstance::getStatus, CountersignStatus.CANCELED));
                workflowCountersignRecordService.update(Wraps.<WorkflowCountersignRecord>lbU()
                        .eq(WorkflowCountersignRecord::getWorkflowId, workflowNode.getWorkflowId())
                        .eq(WorkflowCountersignRecord::getNodeId, workflowNode.getId())
                        .eq(WorkflowCountersignRecord::getHistoryId, projectWorkflowTransitionHistory.getId())
                        .ne(WorkflowCountersignRecord::getStatus, CountersignStatus.COMPLETED)
                        .set(WorkflowCountersignRecord::getStatus, CountersignStatus.CANCELED));
            }else {
                long count = workflowCountersignInstanceService.count(Wraps.<WorkflowCountersignInstance>lbQ()
                        .eq(WorkflowCountersignInstance::getWorkflowId, workflowNode.getWorkflowId())
                        .eq(WorkflowCountersignInstance::getNodeId, workflowNode.getId())
                        .eq(WorkflowCountersignInstance::getHistoryId, projectWorkflowTransitionHistory.getId())
                        .eq(WorkflowCountersignInstance::getStatus, CountersignStatus.PENDING));
                if (count > 0){
                    throw BizException.validFail("状态流转失败,原因:待会签");
                }
            }

        }
        // 查询目标节点id
        WorkflowNode targetNode = workflowNodeService.getOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, stateTransition.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, stateTransition.getTargetStateCode()));
        if (null == targetNode)
        {
            throw BizException.validFail("状态流转失败,原因:获取目标状态信息失败");
        }
        // 计算流转花费时间
        LocalDateTime now = LocalDateTime.now();
        long spendTime = ChronoUnit.MILLIS.between(lastDateTime, now);
        // 添加流转历史记录
        ProjectWorkflowTransitionHistory insertHistory = ProjectWorkflowTransitionHistory.builder().bizId(stateTransition.getBizId())
                .typeCode(stateTransition.getTypeCode())
                .source(sourceNodeId)
                .sourceStateCode(stateTransition.getSourceStateCode())
                .target(targetNode.getId())
                .targetStateCode(stateTransition.getTargetStateCode())
                .spendTime(spendTime)
                .createTime(now)
                .build();
        projectWorkflowTransitionHistoryService.save(insertHistory);
        if (!targetNode.isNeedCountersign()){
            return Boolean.TRUE;
        }
        //需要会签
        workflowCountersignInstanceService.startCountersign(stateTransition,targetNode,insertHistory.getId());
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    public CountersignResultDTO submitCountersign(WorkflowCountersignRecordDTO dto){

        StateTransitionDTO bean = BeanPlusUtil.toBean(dto, StateTransitionDTO.class);
        bean.setSourceStateCode(dto.getStateCode());
        if (dto.getInstanceId()==null){
            //普通状态流转
            transitionState(bean);
            return CountersignResultDTO.builder().completed(Boolean.TRUE).targetStateCode(dto.getTargetStateCode()).build();
        }
        //提交会签
        CountersignResultDTO resultDTO = workflowCountersignInstanceService.submitCountersign(dto);
        if (!resultDTO.isCompleted()){
            return resultDTO;
        }
        //找出最近的流转记录
        WorkflowCountersignRecord record = workflowCountersignRecordService.getOne(
                Wraps.<WorkflowCountersignRecord>lbQ().eq(WorkflowCountersignRecord::getInstanceId, resultDTO.getInstanceId())
                        .eq(WorkflowCountersignRecord::getResult, resultDTO.getFinalResult())
                        .orderByDesc(WorkflowCountersignRecord::getUpdateTime).last(" limit 1"));
        bean.setTargetStateCode(record.getTargetStateCode());
        bean.setUserIds(StrUtil.split(record.getCountersignParticipantIds(),StrUtil.COMMA).stream().map(item -> Long.valueOf(item)).collect(Collectors.toList()));
        bean.setBizId(record.getBizId());
        bean.setWorkflowId(record.getWorkflowId());
        bean.setBizType(TypeClassify.get(record.getBizType()));
        //流转状态
        transitionState(bean);
        resultDTO.setTargetStateCode(bean.getTargetStateCode());
        return resultDTO;
    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> set)
    {
        if (CollUtil.isEmpty(set))
        {
            return Collections.emptyMap();
        }
        //userid—bizType-bizId 查询会签记录
        //找到所有userId
        return set.stream().map(item ->
        {
            String[] split = item.toString().split(StrUtil.DASHED);
            return workflowCountersignRecordService.getOne(Wraps.<WorkflowCountersignRecord>lbQ().eq(WorkflowCountersignRecord::getUserId, split[0])
                    .eq(WorkflowCountersignRecord::getBizType, split[1]).eq(WorkflowCountersignRecord::getBizId, split[2]).eq(WorkflowCountersignRecord::getStatus, CountersignStatus.PENDING));
        }).filter(Objects::nonNull).collect(Collectors.toMap(item -> (item.getUserId() + StrUtil.DASHED + item.getBizType() + StrUtil.DASHED + item.getBizId()), item -> item));

    }

    //会签历史
    public List<WorkflowTransitionHistoryDTO> countersignHistory(WorkflowItemDTO dto){
        List<ProjectWorkflowTransitionHistory> historyList =
                projectWorkflowTransitionHistoryService.list(Wraps.<ProjectWorkflowTransitionHistory>lbQ()
                        .eq(ProjectWorkflowTransitionHistory::getBizId, dto.getId())
                        .eq(ProjectWorkflowTransitionHistory::getTypeCode, dto.getTypeCode())
                        .orderByAsc(SuperEntity::getCreateTime));
        if (CollUtil.isEmpty(historyList)){
            return Collections.emptyList();
        }
        //伪造一个开始节点记录
        WorkflowTransitionHistoryDTO starNode = BeanUtil.toBean(historyList.get(0), WorkflowTransitionHistoryDTO.class);
        starNode.setId(0l);
        starNode.setTarget(starNode.getSource());
        starNode.setTargetStateCode(starNode.getSourceStateCode());
        starNode.setSource(null);
        starNode.setSourceStateCode(null);
        starNode.setCreateTime(starNode.getCreateTime().minus(starNode.getSpendTime(), ChronoUnit.MILLIS));
        starNode.setSpendTime(0L);
        List<WorkflowTransitionHistoryDTO> historyDTOList = BeanUtil.copyToList(historyList, WorkflowTransitionHistoryDTO.class);
        historyDTOList.add(0, starNode);
        List<WorkflowCountersignRecord> recordList = workflowCountersignRecordService.list(Wraps.<WorkflowCountersignRecord>lbQ()
                        .eq(WorkflowCountersignRecord::getStatus, CountersignStatus.COMPLETED)
                        .in(WorkflowCountersignRecord::getHistoryId, historyList.stream().map(item -> item.getId()).collect(Collectors.toList())));
        SpringUtil.getBean(EchoService.class).action(recordList);
        Map<Long, List<WorkflowCountersignRecord>> recordMap =recordList.stream().collect(Collectors.groupingBy(item -> item.getHistoryId()));
        //下一个的createdBy给前一个
        for (int i = 0; i < historyDTOList.size() - 1; i++) {
            historyDTOList.get(i).setCreatedBy(historyDTOList.get(i + 1).getCreatedBy());
        }
        //最后一个置空
        historyDTOList.get(historyDTOList.size() - 1).setCreatedBy(null);
        historyDTOList.forEach(item ->item.setCountersignRecordList(recordMap.get(item.getId())));
        return historyDTOList;
    }

    @Override
    public String findEndStateCode(WorkflowItemDTO workItem)
    {
        Type type = typeService.findByCode(workItem.getTypeCode());
        ProjectWorkflow projectWorkflow = projectWorkflowService.getProjectWorkflow(workItem.getProjectId(),
                type.getClassify(), workItem.getTypeCode());
        if (null == projectWorkflow)
        {
            throw BizException.validFail("当前未查询到工作流，无法查询流转状态");
        }
        // 查询当前节点
        WorkflowNode workflowNode = workflowNodeService.getOne(Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId())
                .eq(WorkflowNode::getNodeType, WorkflowNodeType.END_NODE));
        if (workflowNode != null){
            return workflowNode.getStateCode();
        }
        return null;
    }
}
