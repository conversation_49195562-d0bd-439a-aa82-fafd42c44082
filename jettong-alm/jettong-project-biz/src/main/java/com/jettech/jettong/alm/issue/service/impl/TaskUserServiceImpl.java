package com.jettech.jettong.alm.issue.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.utils.SpringUtils;
import com.jettech.jettong.alm.issue.dao.TaskUserMapper;
import com.jettech.jettong.alm.issue.entity.Task;
import com.jettech.jettong.alm.issue.entity.TaskUser;
import com.jettech.jettong.alm.issue.enumeration.ItemUserType;
import com.jettech.jettong.alm.issue.service.TaskUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.jettech.jettong.alm.issue.entity.TaskUser.HANDLE_BY_LIST;

/**
 * 任务和人员关联业务层
 * <AUTHOR>
 * @version 1.0
 * @description 任务和人员关联业务层
 * @projectName jettong
 * @package com.jettech.jettong.workflow.service.impl
 * @className TaskUserServiceImpl
 * @date 2025-09-15
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
public class TaskUserServiceImpl extends SuperServiceImpl<TaskUserMapper, TaskUser> implements TaskUserService
{


    @Override
    public List<Long> findUserIdsByTaskId(Long taskId)
    {
        return  list(Wraps.<TaskUser>lbQ().eq(TaskUser::getTaskId, taskId).eq(TaskUser::getType, ItemUserType.HANDLE)).stream().map(TaskUser::getUserId).collect(Collectors.toList());
    }

    @Override
    public List<TaskUser> actionHandleBy(Task task)
    {
        return actionHandleBy(Arrays.asList(task)).get(task.getId());
    }

    @Override
    public Map<Long, List<TaskUser>> actionHandleBy(List<Task> tasks)
    {
        List<Long> taskIds = tasks.stream().map(Task::getId).collect(Collectors.toList());
        List<TaskUser> taskUserList = list(Wraps.<TaskUser>lbQ().eq(TaskUser::getType, ItemUserType.HANDLE).in(TaskUser::getTaskId, taskIds));
        SpringUtils.getBean(EchoService.class).action(taskUserList);
        Map<Long, List<TaskUser>> taskUserMap = taskUserList.stream().collect((Collectors.groupingBy(TaskUser::getTaskId)));
        tasks.forEach(task ->
        {
            task.setHandleByList(TaskUser.getUserIds(taskUserMap.get(task.getId())));
            task.getEchoMap().put(HANDLE_BY_LIST, TaskUser.getUserInfos(taskUserMap.get(task.getId())));
        });
        return taskUserMap;
    }

    @Override
    public void actionHandleById(List<Task> tasks)
    {
        List<Long> taskIds = tasks.stream().map(Task::getId).collect(Collectors.toList());
        List<TaskUser> taskUserList = list(Wraps.<TaskUser>lbQ().eq(TaskUser::getType, ItemUserType.HANDLE).in(TaskUser::getTaskId, taskIds));
        Map<Long, List<Long>> taskUserMap = taskUserList.stream().collect(Collectors.groupingBy(TaskUser::getTaskId,
                Collectors.mapping(TaskUser::getUserId, Collectors.toList())));
        tasks.forEach(task ->
            task.setHandleByList(taskUserMap.get(task.getId()))
        );
    }
}
