package com.jettech.jettong.alm.issue.dao;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.alm.issue.entity.Testreq;
import org.springframework.stereotype.Repository;

import java.util.Set;

/**
 * 测试需求信息Mapper接口
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong1
 * @package com.jettech.jettong.alm.issue.dto
 * @className aa
 * @date 2025/7/7 18:38
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface TestreqMapper extends SuperMapper<Testreq> {
    int calculationDelay();
//    int setCustomFieldNull(@Param("customFields") Set<String> customFields);
//    IPage<IssueGroupVO> groupSelect(IPage<IssueGroupVO> page,
//                                    @Param("groupBy") String groupBy,
//                                    @Param(Constants.WRAPPER) LbqWrapper<Testreq> wrapper);
}
