package com.jettech.jettong.alm.issue.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.CollHelper;
import com.jettech.basic.utils.StringUtil;
import com.jettech.jettong.alm.issue.dao.IssueTestreqProductMapper;
import com.jettech.jettong.alm.issue.dao.StateMapper;
import com.jettech.jettong.alm.issue.dao.TestreqMapper;
import com.jettech.jettong.alm.issue.entity.IssueTestreqProduct;
import com.jettech.jettong.alm.issue.entity.State;
import com.jettech.jettong.alm.issue.entity.Testreq;
import com.jettech.jettong.alm.issue.entity.Type;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.issue.service.TestreqService;
import com.jettech.jettong.alm.issue.service.TypeService;
import com.jettech.jettong.alm.issue.service.event.IssueUpdateEvent;
import com.jettech.jettong.alm.issue.vo.IssueTypeComponentResult;
import com.jettech.jettong.alm.project.dao.ProjectInfoMapper;
import com.jettech.jettong.alm.project.dao.ProjectUserRoleMapper;
import com.jettech.jettong.alm.project.dao.ProjectWorkflowAuthorityMapper;
import com.jettech.jettong.alm.project.dao.ProjectWorkflowTransitionHistoryMapper;
import com.jettech.jettong.alm.project.entity.*;
import com.jettech.jettong.alm.project.service.ProjectRoleService;
import com.jettech.jettong.alm.project.service.ProjectWorkflowService;
import com.jettech.jettong.alm.project.service.ProjectWorkflowTransitionCheckService;
import com.jettech.jettong.alm.workflow.dao.WorkflowAuthorityMapper;
import com.jettech.jettong.alm.workflow.dao.WorkflowNodeMapper;
import com.jettech.jettong.alm.workflow.dao.WorkflowTransitionMapper;
import com.jettech.jettong.alm.workflow.entity.WorkflowAuthority;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;
import com.jettech.jettong.alm.workflow.entity.WorkflowTransition;
import com.jettech.jettong.alm.workflow.enumeration.WorkflowAuthorityType;
import com.jettech.jettong.alm.workflow.enumeration.WorkflowNodeType;
import com.jettech.jettong.base.api.FileApi;
import com.jettech.jettong.base.api.MsgApi;
import com.jettech.jettong.base.dto.msg.MsgDTO;
import com.jettech.jettong.base.dto.msg.MsgSaveDTO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.common.constant.MsgBizType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong1
 * @package com.jettech.jettong.alm.issue.dto
 * @className aa
 * @date 2025/7/7 18:38
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class TestreqServiceImpl extends SuperServiceImpl<TestreqMapper, Testreq> implements TestreqService {

    private final static String ADD_KEY = "ADD";

    private final TypeService typeService;
    private final ProjectWorkflowService projectWorkflowService;
    private final WorkflowAuthorityMapper workflowAuthorityMapper;
    private final ProjectWorkflowAuthorityMapper projectWorkflowAuthorityMapper;
    private final WorkflowTransitionMapper workflowTransitionMapper;
    private final WorkflowNodeMapper workflowNodeMapper;
    private final ProjectUserRoleMapper projectUserRoleMapper;
    private final ProjectWorkflowTransitionHistoryMapper projectWorkflowTransitionHistoryMapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final FileApi fileApi;
    private final IssueTestreqProductMapper testreqProductMapper;
    private final ProjectWorkflowTransitionCheckService projectWorkflowTransitionCheckService;
    private final ProjectRoleService projectRoleService;
    private final StateMapper stateMapper;
    private final String creatorInfo = buildUserString();
    private final MsgApi msgApi;

    @Override
    public boolean saveBatch(Collection<Testreq> entityList, int batchSize) {
        boolean saveBatch = super.saveBatch(entityList, batchSize);

        // 推送 工作项改变事件
        entityList.forEach(e -> IssueUpdateEvent.sendEvent(TypeClassify.TESTREQ, e.getId(), null, e));
        return saveBatch;
    }
    @Override
    public boolean save(Testreq testreq)
    {
        Type type = typeService.findByCode(testreq.getTypeCode());

        if (type == null)
        {
            throw BizException.validFail("获取事项类型失败,请联系管理员");
        }

        // 自动生成一个code
        testreq.setCode(typeService.getCode(type));

        // 查询需求的初始化状态
        WorkflowNode workflowNode =
                projectWorkflowService.findProjectFirstNode(testreq.getProjectId(), TypeClassify.TESTREQ,
                        testreq.getTypeCode());
        if (null == workflowNode)
        {
            throw BizException.validFail("获取该测试需求类型工作流失败，请联系管理员");
        }
        testreq.setStateCode(workflowNode.getStateCode());
        testreq.setRateProgress(workflowNode.getProgress());

        // 查询项目信息
        if (testreq.getProjectId() != null)
        {
            ProjectInfo projectInfo = projectInfoMapper.selectById(testreq.getProjectId());
            if (projectInfo == null)
            {
                throw BizException.validFail("创建失败,原因:获取项目信息失败");
            }

            testreq.setProgramId(projectInfo.getProgramId());
        }
        boolean add = super.save(testreq);

        // 推送 工作项改变事件
        IssueUpdateEvent.sendEvent(TypeClassify.TESTREQ, testreq.getId(), null, testreq);

        if (add)
        {
            // 修改附件
            List<File> files = testreq.getFiles();
            if (files != null && !files.isEmpty())
            {
                files.forEach(item -> item.setBizType(FileBizType.TESTREQ_FILE_UPLOAD).setBizId(testreq.getId()));

                fileApi.updateBatchById(files);
            }

        }
        // 场景1：测试需求创建时发送通知
        String creatorInfo = buildUserString();
        String createJumpStr = buildJumpString(testreq.getCode(), testreq.getName(), testreq.getId(), testreq.getProjectId(),testreq.getTypeCode());

        // 给负责人发送通知
        sendNotification(
                testreq.getLeadingBy(),
                testreq.getId(),
                "测试需求需求创建通知",
                String.format("%s创建了一个您负责的测试需求【%s】", creatorInfo, createJumpStr)
        );

        // 给处理人发送通知
        sendNotification(
                testreq.getHandleBy(),
                testreq.getId(),
                "测试需求创建通知",
                String.format("%s创建了一个您处理的测试需求【%s】", creatorInfo, createJumpStr)
        );
        return add;
    }

    @Override
    public void transitionState(Long testreqId, String sourceStateCode, String targetStateCode) {
        // TODO: 实现测试需求状态流转逻辑
        // 查询当前需求节点id
        Testreq testreq = super.getById(testreqId);
        if (null == testreq)
        {
            throw BizException.validFail("状态流转失败,原因:未查询到测试需求信息");
        }
        if (!testreq.getStateCode().equals(sourceStateCode))
        {
            throw BizException.validFail("状态流转失败,原因:测试需求状态已更新,请刷新重试");
        }

        // 查询需求使用的工作流
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(testreq.getProjectId(),
                        TypeClassify.TESTREQ,
                        testreq.getTypeCode());
        if (null == projectWorkflow || null == projectWorkflow.getWorkflowInfo())
        {
            throw BizException.validFail("状态流转失败,原因:获取测试需求使用工作流失败");
        }

        // 获取最后一次流转记录
        ProjectWorkflowTransitionHistory projectWorkflowTransitionHistory =
                projectWorkflowTransitionHistoryMapper.selectOne(Wraps.<ProjectWorkflowTransitionHistory>lbQ()
                        .eq(ProjectWorkflowTransitionHistory::getBizId, testreq.getId())
                        .eq(ProjectWorkflowTransitionHistory::getTypeCode, testreq.getTypeCode()).orderByDesc(
                                ProjectWorkflowTransitionHistory::getCreateTime).last(" limit 1"));

        Long sourceNodeId;
        LocalDateTime lastDateTime;
        // 如果最后一次流转记录为null，则使用需求的创建时间
        if (null == projectWorkflowTransitionHistory)
        {
            lastDateTime = testreq.getCreateTime();

            WorkflowNode workflowNode =
                    projectWorkflowService.findProjectFirstNode(testreq.getProjectId(), TypeClassify.TESTREQ,
                            testreq.getTypeCode());
            if (workflowNode == null)
            {
                throw BizException.validFail("状态流转失败,原因:获取需求初始状态节点失败");
            }
            sourceNodeId = workflowNode.getId();
        }
        else
        {

            lastDateTime = projectWorkflowTransitionHistory.getCreateTime();
            sourceNodeId = projectWorkflowTransitionHistory.getTarget();
        }
        // 查询目标节点id
        WorkflowNode targetNode = workflowNodeMapper.selectOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, targetStateCode));
        if (null == targetNode)
        {
            throw BizException.validFail("状态流转失败,原因:获取需求流转到状态信息失败");
        }
        // 计算流转花费时间
        LocalDateTime now = LocalDateTime.now();
        long spendTime = ChronoUnit.MILLIS.between(lastDateTime, now);

        // 修改需求信息
        Testreq updateTestreq = Testreq.builder()
                .id(testreqId)
                .stateCode(targetStateCode)
                .rateProgress(targetNode.getProgress())
                .build();
        if (WorkflowNodeType.END_NODE.eq(targetNode.getNodeType()))
        {
            updateTestreq.setEndTime(now);
            updateTestreq.setDelay(testreq.getPlanEtime() != null && now.isAfter(testreq.getPlanEtime()));
        }
        else if(WorkflowNodeType.INTERMEDIATE_NODE.eq(targetNode.getNodeType()) && testreq.getStartTime()==null)
        {
            updateTestreq.setStartTime(now);
        }

        baseMapper.updateById(updateTestreq);

        // 添加流转历史记录
        ProjectWorkflowTransitionHistory insertHistory = ProjectWorkflowTransitionHistory.builder()
                .bizId(testreqId)
                .typeCode(testreq.getTypeCode())
                .source(sourceNodeId)
                .sourceStateCode(sourceStateCode)
                .target(targetNode.getId())
                .targetStateCode(targetStateCode)
                .spendTime(spendTime)
                .createTime(now)
                .systemFlow((ContextUtil.getUserId() == null || ContextUtil.getUserId() == 0L))
                .build();

        projectWorkflowTransitionHistoryMapper.insert(insertHistory);

        // 推送 工作项改变事件
        IssueUpdateEvent.sendEvent(TypeClassify.TESTREQ, testreq.getId(),
                Testreq.builder().id(testreq.getId()).stateCode(sourceStateCode).build(),
                Testreq.builder().id(testreq.getId()).stateCode(targetStateCode).build()
        );
        // 场景3：测试需求状态流转时发送通知
// 获取状态信息
        List<String> stateCodes = Arrays.asList(sourceStateCode, targetStateCode);
        List<State> states = stateMapper.selectList(Wraps.<State>lbQ().in(State::getCode, stateCodes));
        Map<String, String> stateNameMap = states.stream()
                .collect(Collectors.toMap(State::getCode, State::getName, (k1, k2) -> k1));

        String stateJumpStr = buildJumpString(testreq.getCode(), testreq.getName(), testreq.getId(), testreq.getProjectId(),testreq.getTypeCode());
        String sourceState = stateNameMap.get(sourceStateCode);
        String targetState = stateNameMap.get(targetStateCode);

// 给负责人发送通知
        sendNotification(
                testreq.getLeadingBy(),
                testreq.getId(),
                "测试需求状态流转通知",
                String.format("%s将您负责的测试需求【%s】由【%s】流转到【%s】", creatorInfo, stateJumpStr, sourceState, targetState)
        );

// 给处理人发送通知
        sendNotification(
                testreq.getHandleBy(),
                testreq.getId(),
                "测试需求状态流转通知",
                String.format("%s将您处理的测试需求【%s】由【%s】流转到【%s】", creatorInfo, stateJumpStr, sourceState, targetState)
        );

    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<? extends Serializable> ids)
    {
        // 删除附件信息
        fileApi.removeByFileBizTypeAndBizIds(FileBizType.TESTREQ_FILE_UPLOAD, ids);

        //删除状态流转历史
        projectWorkflowTransitionHistoryMapper.delete(Wraps.<ProjectWorkflowTransitionHistory>lbQ()
                .in(ProjectWorkflowTransitionHistory::getBizId, ids));

        boolean remove = super.removeByIds(ids);
        // 推送 工作项改变事件
        ids.forEach(id -> IssueUpdateEvent.sendEvent(TypeClassify.TESTREQ, (Long) id, null, null));

        return remove;
    }
    @Override
    public boolean updateAllById(Testreq testreq)
    {
        Testreq oldTestreq = super.getById(testreq.getId());

        if (oldTestreq == null)
        {
            throw BizException.validFail("修改失败,原因:未查询到该测试需求");
        }

        List<File> oldFiles = fileApi.findByBizTypeAndBizId(FileBizType.TESTREQ_FILE_UPLOAD, testreq.getId());

        List<Long> oldFileIds = oldFiles.stream().map(File::getId).collect(Collectors.toList());

        List<File> newFiles = testreq.getFiles();

        if (newFiles == null || newFiles.isEmpty())
        {
            if (!oldFileIds.isEmpty())
            {
                fileApi.removeByFileBizTypeAndBizIds(FileBizType.TESTREQ_FILE_UPLOAD, oldFileIds);
            }
        }
        else
        {
            List<Long> newFileIds = newFiles.stream().map(File::getId).collect(Collectors.toList());

            if (!oldFileIds.isEmpty())
            {
                // 筛选出需要删除的数据
                List<Long> deleteFileIds =
                        oldFileIds.stream().filter(item -> !newFileIds.contains(item)).collect(Collectors.toList());
                if (!deleteFileIds.isEmpty())
                {
                    fileApi.removeByFileBizTypeAndBizIds(FileBizType.TESTREQ_FILE_UPLOAD, deleteFileIds);
                }

            }
            newFiles.forEach(item -> item.setBizId(testreq.getId()).setBizType(FileBizType.TESTREQ_FILE_UPLOAD));
            fileApi.updateBatchById(newFiles);
        }

        testreq.setCode(oldTestreq.getCode());
        testreq.setTypeCode(oldTestreq.getTypeCode());
        if (!testreq.getStateCode().equals(oldTestreq.getStateCode()))
        {
            transitionState(testreq.getId(), oldTestreq.getStateCode(), testreq.getStateCode());
        }
        else
        {
            testreq.setRateProgress(oldTestreq.getRateProgress());
            testreq.setEndTime(oldTestreq.getEndTime());
        }

        testreq.setCreateTime(oldTestreq.getCreateTime());
        testreq.setCreatedBy(oldTestreq.getCreatedBy());
        testreq.setUpdateTime(LocalDateTime.now());
        testreq.setUpdatedBy(ContextUtil.getUserId());
        boolean update = super.updateAllById(testreq);

        // 推送 工作项改变事件
        oldTestreq.setFiles(oldFiles);
        if (!testreq.getStateCode().equals(oldTestreq.getStateCode()))
        {
            IssueUpdateEvent.sendEvent(TypeClassify.TESTREQ, testreq.getId(), oldTestreq, testreq);
        }

// 场景2：测试需求变更时发送通知
        String updateJumpStr = buildJumpString(oldTestreq.getCode(), testreq.getName(), testreq.getId(), testreq.getProjectId(),testreq.getTypeCode());

// 给负责人发送通知
        sendNotification(
                testreq.getLeadingBy(),
                testreq.getId(),
                "测试需求变更通知",
                String.format("%s变更了一个您负责的测试需求【%s】", creatorInfo, updateJumpStr)
        );

// 给处理人发送通知
        sendNotification(
                testreq.getHandleBy(),
                testreq.getId(),
                "测试需求变更通知",
                String.format("%s变更了一个您处理的测试需求【%s】", creatorInfo, updateJumpStr)
        );
        return update;
    }
    @Override
    public void calculationDelay() {
        // TODO: 实现测试需求延期计算逻辑
        {
            baseMapper.calculationDelay();
        }
    }

    @Override
    public List<WorkflowNode> findNextNode(Long testreqId)
    {
        Testreq testreq = super.getById(testreqId);
        if (StrUtil.isEmpty(testreq.getTypeCode()))
        {
            throw BizException.validFail("当前需求没有需求类型，无法查询流转状态");
        }
        // 查询该需求使用的工作流
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(testreq.getProjectId(),
                        TypeClassify.TESTREQ, testreq.getTypeCode());

        if (null == projectWorkflow)
        {
            throw BizException.validFail("当前需求未查询到工作流，无法查询可流转状态");
        }
        // 查询该需求的当前节点
        WorkflowNode thisWorkflowNode = workflowNodeMapper.selectOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, testreq.getStateCode()));

        if (null == thisWorkflowNode)
        {
            throw BizException.validFail("获取当前需求工作流状态节点失败");
        }

        // 查询可流转到的节点
        return getTargetNodeIds(testreq, projectWorkflow.getWorkflowId(), thisWorkflowNode.getId());
    }
    private List<WorkflowNode> getTargetNodeIds(Testreq testreq, Long workflowId, Long thisNodeId)
    {

        List<WorkflowTransition> workflowTransitions = workflowTransitionMapper.selectList(
                Wraps.<WorkflowTransition>lbQ().eq(WorkflowTransition::getWorkflowId, workflowId)
                        .eq(WorkflowTransition::getSource, thisNodeId));

        if (workflowTransitions.isEmpty())
        {
            return Collections.emptyList();
        }
        boolean remove = true;
        Iterator<WorkflowTransition> iterator = workflowTransitions.iterator();
        while (iterator.hasNext())
        {
            WorkflowTransition workflowTransition = iterator.next();
            // 查询流转权限
            List<ProjectWorkflowAuthority> projectWorkflowAuthorities = projectWorkflowAuthorityMapper.selectList(
                    Wraps.<ProjectWorkflowAuthority>lbQ().eq(ProjectWorkflowAuthority::getWorkflowId, workflowId)
                            .eq(ProjectWorkflowAuthority::getProjectId, testreq.getProjectId())
                            .eq(ProjectWorkflowAuthority::getTypeClassify, TypeClassify.TESTREQ)
                            .eq(ProjectWorkflowAuthority::getTypeCode, testreq.getTypeCode())
                            .eq(ProjectWorkflowAuthority::getTransitionId, workflowTransition.getId()));

            List<WorkflowAuthority> workflowAuthorities;
            if (projectWorkflowAuthorities.isEmpty())
            {
                workflowAuthorities = workflowAuthorityMapper.selectList(
                        Wraps.<WorkflowAuthority>lbQ()
                                .eq(WorkflowAuthority::getTransitionId, workflowTransition.getId()));
            }
            else
            {
                workflowAuthorities = BeanPlusUtil.toBeanList(projectWorkflowAuthorities, WorkflowAuthority.class);
            }
            boolean authorityBreak = false;
            for (WorkflowAuthority workflowAuthority : workflowAuthorities)
            {
                switch (workflowAuthority.getType())
                {
                    case RESPONSIBLE:
                        if (ContextUtil.getUserId().equals(testreq.getLeadingBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case HANDLER:
                        if (ContextUtil.getUserId().equals(testreq.getHandleBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case CREATOR:
                        if (ContextUtil.getUserId().equals(testreq.getPutBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case USER:
                        if (ContextUtil.getUserId().equals(workflowAuthority.getUserOrRole()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case ROLE:
                        List<ProjectUserRole> userRoles =
                                projectUserRoleMapper.selectList(
                                        Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getRoleId)
                                                .eq(ProjectUserRole::getProjectId, testreq.getProjectId())
                                                .eq(ProjectUserRole::getUserId, ContextUtil.getUserId()));
                        Set<Long> roleIds =
                                userRoles.stream().map(ProjectUserRole::getRoleId).collect(Collectors.toSet());
                        if (roleIds.contains(workflowAuthority.getUserOrRole()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    default:
                        break;
                }
                if (authorityBreak)
                {
                    break;
                }
            }

            if (remove)
            {
                iterator.remove();
            }

        }
        if (workflowTransitions.isEmpty())
        {
            return Collections.emptyList();
        }

        return workflowTransitions.stream().map(transition -> {
            WorkflowNode workflowNode = workflowNodeMapper.selectById(transition.getTarget());

            List<ProjectWorkflowTransitionCheck> checkList = projectWorkflowTransitionCheckService.findCheckList(transition.getId(),
                    testreq.getProjectId(), TypeClassify.TESTREQ, testreq.getTypeCode());
            workflowNode.getEchoMap().put("check", checkList);
            return workflowNode;
        }).collect(Collectors.toList());
    }

    @Override
    public IssueTypeComponentResult findTypeByProjectId(Long projectId)
    {
        // 根据项目id查询所有任务
        List<Testreq> bugs =
                baseMapper.selectList(Wraps.<Testreq>lbQ().eq(Testreq::getProjectId, projectId));

        // 查询所有状态
        List<State> states = stateMapper.selectList(Wraps.lbQ());

        // 按code和name分组
        Map<String, State> codeNameMap = states.stream().collect(Collectors.toMap(State::getCode, state -> state));

        Map<String, List<Testreq>> groupStateCodeTestreq =
                bugs.stream().collect(Collectors.groupingBy(Testreq::getStateCode));

        List<IssueTypeComponentResult.Pie> data = new LinkedList<>();

        for (Map.Entry<String, List<Testreq>> entry : groupStateCodeTestreq.entrySet())
        {
            data.add(IssueTypeComponentResult.Pie.builder()
                    .name(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getName() :
                            entry.getKey())
                    .color(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getColor() :
                            entry.getKey())
                    .code(entry.getKey())
                    .value(entry.getValue().size()).build());
        }

        return IssueTypeComponentResult.builder().count(bugs.size()).data(data).build();
    }
    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return CollHelper.uniqueIndex(super.listByIds(ids), Testreq::getId, testreq -> testreq.setDescription(null));
    }

    @Override
    public List<Testreq> selectTestreqByIdeaIdOrTestreqId(Testreq testreq)
    {
        List<Testreq> list = new ArrayList<>();
        if (testreq.getId() != null)
        {
            list = super.list(Wraps.<Testreq>lbQ().eq(Testreq::getParentId, testreq.getId()));
        }
        List<Long> idList = list.stream().map(Testreq::getId).collect(Collectors.toList());

        if (idList.isEmpty())
        {
            return list;
        }
        //判断是否可以需要查询
        boolean isFor = true;
        List<Long> testreqIds = idList;
        while (isFor)
        {
            List<Testreq> testreqList =
                    super.list(Wraps.<Testreq>lbQ().in(Testreq::getParentId, testreqIds));
            if (!testreqList.isEmpty())
            {
                testreqList.removeIf(testreq1 -> idList.contains(testreq1.getId()));
                testreqIds = testreqList.stream().map(Testreq::getId).collect(Collectors.toList());
                list.addAll(testreqList);
            }
            else
            {
                isFor = false;
            }
        }
        return list;
    }

    /**
     * 查询具有流转权限角色的用户id
     * @param oldTestreq
     * @return {@link List< Long>}
     * @throws
     * <AUTHOR>
     * @date 2025/8/7 15:15
     * @update sxh 2025/8/7 15:15
     * @since 1.0
     */
    @Override
    public List<Long> getWorkFlowUserIds(Testreq oldTestreq) {
        List<Long> allUserIds = new ArrayList<>();
        // 查询该任务使用的工作流
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(oldTestreq.getProjectId(),
                        TypeClassify.TESTREQ,
                        oldTestreq.getTypeCode());
        if (null == projectWorkflow || null == projectWorkflow.getWorkflowInfo()) {
            throw BizException.validFail("当前任务未查询到工作流，无法查询可流转状态");
        }
        // 查询该任务的当前节点
        WorkflowNode thisWorkflowNode = workflowNodeMapper.selectOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, oldTestreq.getStateCode()));

        if (null == thisWorkflowNode) {
            throw BizException.validFail("获取当前任务工作流状态节点失败");
        }
        // 获取流转到的节点
        List<WorkflowTransition> workflowTransitions = workflowTransitionMapper.selectList(
                Wraps.<WorkflowTransition>lbQ().eq(WorkflowTransition::getWorkflowId, projectWorkflow.getWorkflowId())
                        .eq(WorkflowTransition::getSource, thisWorkflowNode.getId()));

        if (workflowTransitions.isEmpty()) {
            return Collections.emptyList();
        }
        Iterator<WorkflowTransition> iterator = workflowTransitions.iterator();
        while (iterator.hasNext()) {
            WorkflowTransition workflowTransition = iterator.next();
            // 查询流转权限
            List<ProjectWorkflowAuthority> projectWorkflowAuthorities = projectWorkflowAuthorityMapper.selectList(
                    Wraps.<ProjectWorkflowAuthority>lbQ().eq(ProjectWorkflowAuthority::getWorkflowId, projectWorkflow.getWorkflowId())
                            .eq(ProjectWorkflowAuthority::getProjectId, oldTestreq.getProjectId())
                            .eq(ProjectWorkflowAuthority::getTypeClassify, TypeClassify.TESTREQ)
                            .eq(ProjectWorkflowAuthority::getTypeCode, oldTestreq.getTypeCode())
                            .eq(ProjectWorkflowAuthority::getTransitionId, workflowTransition.getId())
                            .eq(ProjectWorkflowAuthority::getType, WorkflowAuthorityType.ROLE));


            if (projectWorkflowAuthorities.size() != 0) {
                List<WorkflowAuthority> workflowAuthorities = BeanPlusUtil.toBeanList(projectWorkflowAuthorities, WorkflowAuthority.class);
                for(WorkflowAuthority workflowAuthority:workflowAuthorities){
                    List<ProjectUserRole> projectUserRoles =
                            projectUserRoleMapper.selectList(
                                    Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getUserId)
                                            .eq(ProjectUserRole::getProjectId, oldTestreq.getProjectId())
                                            .eq(ProjectUserRole::getRoleId, workflowAuthority.getUserOrRole()));
                    List<Long> userIds =
                            projectUserRoles.stream().map(ProjectUserRole::getUserId).collect(Collectors.toList());
                    allUserIds.addAll(userIds);
                }

            }

            List<WorkflowAuthority> workflowAuthorities = workflowAuthorityMapper.selectList(
                    Wraps.<WorkflowAuthority>lbQ()
                            .eq(WorkflowAuthority::getTransitionId, workflowTransition.getId())
                            .eq(WorkflowAuthority::getType, WorkflowAuthorityType.ROLE));
            for(WorkflowAuthority workflowAuthority:workflowAuthorities){
                List<ProjectUserRole> projectUserRoles =
                        projectUserRoleMapper.selectList(
                                Wraps.<ProjectUserRole>lbQ().select(ProjectUserRole::getUserId)
                                        .eq(ProjectUserRole::getProjectId, oldTestreq.getProjectId())
                                        .eq(ProjectUserRole::getRoleId, workflowAuthority.getUserOrRole()));
                List<Long> userIds =
                        projectUserRoles.stream().map(ProjectUserRole::getUserId).collect(Collectors.toList());
                allUserIds.addAll(userIds);
            }

        }
        if (oldTestreq.getProjectId()==null){
            return allUserIds;
        }
        //新增项目经理用户的id
        ProjectInfo projectInfo = projectInfoMapper.selectById(oldTestreq.getProjectId());
        String pmRoleCode = projectInfo.getTypeCode() + "_PM";
        // 查询项目经理角色id
        ProjectRole projectRole = projectRoleService.getOne(
                Wraps.<ProjectRole>lbQ().eq(ProjectRole::getCode, pmRoleCode).eq(ProjectRole::getReadonly, true)
                        .eq(ProjectRole::getType, true).eq(ProjectRole::getTypeCode, projectInfo.getTypeCode())
                        .last(" limit 1"));
        //查询用户信息
        List<Long> pmUserIds = projectUserRoleMapper.selectList(
                        Wraps.<ProjectUserRole>lbQ()
                                .eq(ProjectUserRole::getProjectId, oldTestreq.getProjectId())
                                .eq(ProjectUserRole::getRoleId, projectRole.getId())
                ).stream()
                .map(ProjectUserRole::getUserId)
                .collect(Collectors.toList());
        allUserIds.addAll(pmUserIds);
        return allUserIds;
    }

    @Override
    public Testreq getById(Serializable id)
    {
        Testreq testreq = super.getById(id);
        if (testreq != null)
        {
            List<IssueTestreqProduct> productList = testreqProductMapper.selectList(Wraps.<IssueTestreqProduct>lbQ().eq(IssueTestreqProduct::getTestreqId, id));
            if (CollUtil.isNotEmpty( productList))
            {
                testreq.setAssistProductIds(productList.stream().map(IssueTestreqProduct::getProductId).collect(Collectors.toList()));
            }

            testreq.setFiles(fileApi.findByBizTypeAndBizId(FileBizType.TESTREQ_FILE_UPLOAD, id));
        }

        return testreq;
    }
    // 通用消息发送工具方法
    private void sendNotification(Long receiverId, Long businessId, String title, String content) {
        // 接收人校验：为空或为创建人则不发送
        if (receiverId == null || receiverId.equals(ContextUtil.getUserId())) {
            return;
        }

        // 构建消息对象并发送
        MsgDTO msgDTO = MsgDTO.buildNotify(MsgBizType.ISSUE_NOTIFY, businessId, title, content);
        msgDTO.setAuthorId(ContextUtil.getUserId());

        MsgSaveDTO msgSaveDTO = MsgSaveDTO.builder()
                .userIdList(Collections.singleton(receiverId))
                .msgDTO(msgDTO)
                .build();
        msgApi.save(msgSaveDTO);
    }

    // 构建用户信息字符串（带空值处理）
    private String buildUserString() {
        return StringUtil.isEmpty(ContextUtil.getUserName())
                ? "系统"
                : String.format("%s(%s)", ContextUtil.getUserName(), ContextUtil.getUserAccount());
    }

    // 构建跳转字符串
    private String buildJumpString(String code, String name, Long id, Long projectId,String typeCode) {
        return DetectionMessageContent.buildJumpString(code, name, id, TypeClassify.TESTREQ, projectId,typeCode);
    }
}
