package com.jettech.jettong.alm.issue.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;

import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.repository.CacheOps;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.CollHelper;
import com.jettech.basic.utils.StringUtil;
import com.jettech.jettong.alm.issue.dao.IdeaMapper;
import com.jettech.jettong.alm.issue.dao.IssueItemRelationMapper;
import com.jettech.jettong.alm.issue.dao.RequirementMapper;
import com.jettech.jettong.alm.issue.dto.IdeaExportQuery;
import com.jettech.jettong.alm.issue.entity.*;
import com.jettech.jettong.alm.issue.enumeration.IssueRelationType;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.issue.service.IdeaService;
import com.jettech.jettong.alm.issue.service.IssueIdeaProductRequirementService;
import com.jettech.jettong.alm.issue.service.TypeService;
import com.jettech.jettong.alm.issue.service.event.IssueUpdateEvent;
import com.jettech.jettong.alm.issue.vo.IdeaTraceViewBaseInfoResult;
import com.jettech.jettong.alm.issue.vo.IdeaTraceViewResult;
import com.jettech.jettong.alm.project.dao.ProjectInfoMapper;
import com.jettech.jettong.alm.project.dao.ProjectWorkflowAuthorityMapper;
import com.jettech.jettong.alm.project.dao.ProjectWorkflowTransitionHistoryMapper;
import com.jettech.jettong.alm.project.entity.*;
import com.jettech.jettong.alm.project.service.ProjectWorkflowService;
import com.jettech.jettong.alm.project.service.ProjectWorkflowTransitionCheckService;
import com.jettech.jettong.alm.workflow.dao.WorkflowAuthorityMapper;
import com.jettech.jettong.alm.workflow.dao.WorkflowNodeMapper;
import com.jettech.jettong.alm.workflow.dao.WorkflowTransitionMapper;
import com.jettech.jettong.alm.workflow.entity.WorkflowAuthority;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;
import com.jettech.jettong.alm.workflow.entity.WorkflowTransition;
import com.jettech.jettong.alm.workflow.enumeration.WorkflowNodeType;
import com.jettech.jettong.base.api.FileApi;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.rbac.user.UserRole;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.common.cache.alm.TypeCodePrefixCacheKeyBuilder;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户需求信息业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户需求信息业务层
 * @projectName jettong
 * @package com.jettech.jettong.alm.service.impl
 * @className IdeaServiceImpl
 * @date 2021-11-22
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class IdeaServiceImpl extends SuperServiceImpl<IdeaMapper, Idea> implements IdeaService
{
    private final UserApi userApi;
    private final CacheOps cacheOps;
    private final TypeService typeService;
    private final WorkflowNodeMapper workflowNodeMapper;
    private final ProjectWorkflowAuthorityMapper projectWorkflowAuthorityMapper;
    private final WorkflowAuthorityMapper workflowAuthorityMapper;
    private final WorkflowTransitionMapper workflowTransitionMapper;
    private final ProjectWorkflowTransitionHistoryMapper projectWorkflowTransitionHistoryMapper;
    private final FileApi fileApi;
    private final RequirementMapper requirementMapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final IssueItemRelationMapper issueItemRelationMapper;
    private final ProjectWorkflowService projectWorkflowService;
    private final IssueIdeaProductRequirementService ideaRequirementService;
    private final ProjectWorkflowTransitionCheckService transitionCheckService;


    @Override
    public boolean saveBatch(Collection<Idea> entityList, int batchSize) {
        boolean saveBatch = super.saveBatch(entityList, batchSize);
        entityList.forEach(e -> IssueUpdateEvent.sendEvent(TypeClassify.IDEA, e.getId(), null, e));
        return saveBatch;
    }

    @Override
    public boolean save(Idea idea)
    {
        Type type = typeService.findByCode(idea.getTypeCode());
        if (type == null)
        {
            throw BizException.validFail("获取事项类型失败,请联系管理员");
        }
        // 自动生成一个code
        idea.setCode(typeService.getCode(type));
        // 查询意向的初始化状态
        WorkflowNode workflowNode = projectWorkflowService.findFirstNode(TypeClassify.IDEA, idea.getTypeCode());
        if (null == workflowNode)
        {
            throw BizException.validFail("获取该事项类型工作流失败，请联系管理员");
        }
        idea.setStateCode(workflowNode.getStateCode());
        boolean add = super.save(idea);

        // 发送工作项改动事件
        IssueUpdateEvent.sendEvent(TypeClassify.IDEA, idea.getId(), null, idea);

        if (add)
        {
            // 修改附件
            List<File> files = idea.getFiles();
            if (files != null && !files.isEmpty())
            {
                files.forEach(item -> item.setBizType(FileBizType.IDEA_FILE_UPLOAD).setBizId(idea.getId()));

                fileApi.updateBatchById(files);
            }
        }
        return add;
    }

    /**
     * 获取需求的Code，从redis中获取
     *
     * @param type 工作项类型
     * @return String code
     * <AUTHOR>
     * @date 2021/11/22 19:42
     * @update lxr 2021/11/22 19:42
     * @since 1.0
     */
    private synchronized String getCode(Type type)
    {
        String prefix = type.getPrefix();
        CacheKey cacheKey = new TypeCodePrefixCacheKeyBuilder().key(prefix);
        Long codeNum = cacheOps.get(cacheKey, key -> {
            // 获取前缀相同的类型，防止相同前缀但不同类型的工作项 导致获取到值不是最大的bug
            Set<String> typeCodeSet = typeService.list(Wraps.<Type>lbQ().eq(Type::getPrefix, prefix))
                    .stream().map(Type::getCode).collect(Collectors.toSet());
            // 根据时间排序，获取最新一条数据的code
            String code = super.getObj(
                    Wraps.<Idea>lbQ().select(Idea::getCode)
                            .in(Idea::getTypeCode, typeCodeSet)
                            .likeRight(Idea::getCode, prefix)
                            .orderByDesc(Idea::getCreateTime)
                            .last(" limit 1 "),
                    Convert::toStr);
            return StringUtil.getLongToStr(StringUtil.removePrefix(code, prefix));
        });
        codeNum++;
        cacheOps.set(cacheKey, codeNum);
        return prefix + codeNum;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<? extends Serializable> ids)
    {
        // 删除附件信息
        fileApi.removeByFileBizTypeAndBizIds(FileBizType.IDEA_FILE_UPLOAD, ids);

        //删除状态流转历史
        projectWorkflowTransitionHistoryMapper.delete(Wraps.<ProjectWorkflowTransitionHistory>lbQ()
                .in(ProjectWorkflowTransitionHistory::getBizId, ids));

        boolean remove = super.removeByIds(ids);

        // 发送工作项改动事件
        ids.forEach(id -> IssueUpdateEvent.sendEvent(TypeClassify.IDEA, (Long) id, null, null));

        return remove;
    }

    @Override
    public boolean updateAllById(Idea idea)
    {
        Idea oldIdea = super.getById(idea.getId());

        if (oldIdea == null)
        {
            throw BizException.validFail("修改失败,原因:未查询到该用户需求");
        }

        List<File> oldFiles = fileApi.findByBizTypeAndBizId(FileBizType.IDEA_FILE_UPLOAD, idea.getId());

        List<Long> oldFileIds = oldFiles.stream().map(File::getId).collect(Collectors.toList());

        List<File> newFiles = idea.getFiles();

        if (newFiles == null || newFiles.isEmpty())
        {
            if (!oldFileIds.isEmpty())
            {
                fileApi.removeByFileBizTypeAndBizIds(FileBizType.IDEA_FILE_UPLOAD, oldFileIds);
            }
        }
        else
        {
            List<Long> newFileIds = newFiles.stream().map(File::getId).collect(Collectors.toList());

            if (!oldFileIds.isEmpty())
            {
                // 筛选出需要删除的数据
                List<Long> deleteFileIds =
                        oldFileIds.stream().filter(item -> !newFileIds.contains(item)).collect(Collectors.toList());
                if (!deleteFileIds.isEmpty())
                {
                    fileApi.removeByFileBizTypeAndBizIds(FileBizType.IDEA_FILE_UPLOAD, deleteFileIds);
                }

            }
            newFiles.forEach(item -> item.setBizId(idea.getId()).setBizType(FileBizType.IDEA_FILE_UPLOAD));
            fileApi.updateBatchById(newFiles);
        }

        idea.setCode(oldIdea.getCode());
        idea.setTypeCode(oldIdea.getTypeCode());
        idea.setActualTime(oldIdea.getActualTime());
        idea.setCreateTime(oldIdea.getCreateTime());
        idea.setCreatedBy(oldIdea.getCreatedBy());
        idea.setUpdateTime(LocalDateTime.now());
        idea.setUpdatedBy(ContextUtil.getUserId());
        boolean updateAllById = super.updateAllById(idea);

        // 推送工作项变更事件
        oldIdea.setFiles(oldFiles);
        IssueUpdateEvent.sendEvent(TypeClassify.IDEA, idea.getId(), oldIdea, idea);

        return updateAllById;
    }

    @Override
    public void transitionState(Long ideaId, String sourceStateCode, String targetStateCode)
    {
        // 查询当前需求节点id
        Idea idea = super.getById(ideaId);
        if (null == idea)
        {
            throw BizException.validFail("状态流转失败,原因:未查询到用户需求信息");
        }
        if (!idea.getStateCode().equals(sourceStateCode))
        {
            throw BizException.validFail("状态流转失败,原因:用户需求状态已更新,请刷新重试");
        }

        // 查询需求使用的工作流
        ProjectWorkflow workflow =
                projectWorkflowService.getWorkflow(TypeClassify.IDEA, idea.getTypeCode());
        if (null == workflow || null == workflow.getWorkflowId())
        {
            throw BizException.validFail("状态流转失败,原因:获取用户需求使用工作流失败");
        }

        // 获取最后一次流转记录
        ProjectWorkflowTransitionHistory workflowTransitionHistory =
                projectWorkflowTransitionHistoryMapper.selectOne(Wraps.<ProjectWorkflowTransitionHistory>lbQ()
                        .eq(ProjectWorkflowTransitionHistory::getBizId, ideaId)
                        .eq(ProjectWorkflowTransitionHistory::getTypeCode, idea.getTypeCode()).orderByDesc(
                                ProjectWorkflowTransitionHistory::getCreateTime).last(" limit 1"));

        Long sourceNodeId;
        LocalDateTime lastDateTime;
        // 如果最后一次流转记录为null，则使用需求的创建时间
        if (null == workflowTransitionHistory)
        {
            lastDateTime = idea.getCreateTime();

            WorkflowNode workflowNode =
                    projectWorkflowService.findFirstNode(TypeClassify.IDEA, idea.getTypeCode());
            if (workflowNode == null)
            {
                throw BizException.validFail("状态流转失败,原因:获取用户需求初始状态节点失败");
            }
            sourceNodeId = workflowNode.getId();
        }
        else
        {

            lastDateTime = workflowTransitionHistory.getCreateTime();
            sourceNodeId = workflowTransitionHistory.getTarget();
        }
        // 查询目标节点id
        WorkflowNode targetNode = workflowNodeMapper.selectOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, workflow.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, targetStateCode));
        if (null == targetNode)
        {
            throw BizException.validFail("状态流转失败,原因:获取用户需求流转到状态信息失败");
        }
        // 计算流转花费时间
        LocalDateTime now = LocalDateTime.now();
        long spendTime = ChronoUnit.MILLIS.between(lastDateTime, now);

        // 修改需求信息
        Idea updateIdea = Idea.builder()
                .id(ideaId)
                .stateCode(targetStateCode)
                .build();
        if (WorkflowNodeType.END_NODE.eq(targetNode.getNodeType()))
        {
            updateIdea.setActualTime(now);
        }
        super.updateById(updateIdea);

        // 添加流转历史记录
        ProjectWorkflowTransitionHistory insertHistory = ProjectWorkflowTransitionHistory.builder().bizId(ideaId)
                .typeCode(idea.getTypeCode())
                .source(sourceNodeId)
                .sourceStateCode(sourceStateCode)
                .target(targetNode.getId())
                .targetStateCode(targetStateCode)
                .spendTime(spendTime)
                .createTime(now)
                .build();

        projectWorkflowTransitionHistoryMapper.insert(insertHistory);

        // 推送工作项变更事件
        IssueUpdateEvent.sendEvent(TypeClassify.IDEA, idea.getId(),
                Bug.builder().id(idea.getId()).stateCode(sourceStateCode).build(),
                Bug.builder().id(idea.getId()).stateCode(targetStateCode).build()
        );

    }

    @Override
    public List<WorkflowNode> findNextNode(Long ideaId)
    {
        Idea idea = super.getById(ideaId);
        if (StrUtil.isEmpty(idea.getTypeCode()))
        {
            throw BizException.validFail("当前用户需求没有需求类型，无法查询流转状态");
        }
        // 查询该需求使用的工作流
        ProjectWorkflow workflow =
                projectWorkflowService.getWorkflow(TypeClassify.IDEA, idea.getTypeCode());
        if (null == workflow || null == workflow.getWorkflowId())
        {
            throw BizException.validFail("当前用户需求未查询到工作流，无法查询流转状态");
        }
        // 查询该需求的当前节点
        WorkflowNode thisWorkflowNode = workflowNodeMapper.selectOne(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, workflow.getWorkflowId())
                        .eq(WorkflowNode::getStateCode, idea.getStateCode()));

        if (null == thisWorkflowNode)
        {
            throw BizException.validFail("获取当用户需求工作流状态节点失败");
        }
        // 查询可流转到的节点
        return getTargetNodeIds(idea, workflow.getWorkflowId(), thisWorkflowNode.getId());
    }

    @Override
    public List<Map<String, Object>> findIdeaExportQuery(IdeaExportQuery model)
    {
        return baseMapper.findIdeaExportQuery(model);
    }

    @Override
    public IdeaTraceViewResult getTraceViewByIdeaId(Long ideaId)
    {
        // 查询用户需求信息
        Idea idea = baseMapper.selectById(ideaId);
        if (null == idea)
        {
            throw BizException.validFail("获取信息失败，请返回列表页面重试");
        }

        IdeaTraceViewResult ideaTraceViewResult = IdeaTraceViewResult.builder().build();

        IdeaTraceViewResult.IdeaTraceView ideaTraceView =
                BeanPlusUtil.toBean(idea, IdeaTraceViewResult.IdeaTraceView.class);

        ideaTraceViewResult.setIdea(ideaTraceView);

        // 查询用户需求直接关联的需求
        List<Requirement> requirements = ideaRequirementService.getRequirementList(ideaId);
//                requirementMapper.selectList(
//                        Wraps.<Requirement>lbQ().isNull(Requirement::getParentId).eq(Requirement::getIdeaId, ideaId));

        List<IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView> requirementTraceViews =
                BeanPlusUtil.toBeanList(requirements,
                        IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView.class);

        // 查询需求关联的需求信息
        // 获取需求id
        Set<Long> requestIds = requirementTraceViews.stream().map(SuperEntity::getId).collect(Collectors.toSet());
        // 查询关联需求信息
        Map<Long, List<IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView.RelationRequirement>>
                relationRequirementMap =
                getRelationRequirement(requestIds);
        requirementTraceViews.forEach(item ->
        {
            if (relationRequirementMap.containsKey(item.getId()))
            {
                item.setRelation(relationRequirementMap.get(item.getId()));
            }
        });

        // 查询用户需求关联的项目
        // 获取所有项目id，包含null
        Set<Long> hasNullProjectIds = requirements.stream().map(Requirement::getProjectId).collect(Collectors.toSet());

        Set<Long> projectIds = hasNullProjectIds.stream().filter(Objects::nonNull).collect(Collectors.toSet());

        // 将需求放到项目下
        // 先按项目id分组
        Map<Long, List<IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView>>
                groupRequirementTraceViewMap =
                requirementTraceViews.stream().filter(item -> null != item.getProjectId())
                        .collect(Collectors.groupingBy(Requirement::getProjectId));

        List<ProjectInfo> projectInfos = new ArrayList<>();
        if (!projectIds.isEmpty())
        {
            projectInfos =
                    projectInfoMapper.selectList(Wraps.<ProjectInfo>lbQ().in(SuperEntity::getId, projectIds));
        }
        // 当项目id中有null时，添加一个未分配项目
        if (hasNullProjectIds.size() != projectIds.size())
        {
            projectInfos.add(ProjectInfo.builder().build());
            groupRequirementTraceViewMap.put(null,
                    requirementTraceViews.stream().filter(item -> null == item.getProjectId())
                            .collect(Collectors.toList()));
        }

        List<IdeaTraceViewResult.IdeaTraceView.ProjectTraceView> projectTraceViews =
                BeanPlusUtil.toBeanList(projectInfos, IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.class);

        ideaTraceView.setChildren(projectTraceViews);

        // 所有需求id，用于处理死循环问题
        Set<Long> allRequirementIds = new HashSet<>();

        projectTraceViews.forEach(item -> item.setChildren(
                getChildrenRequirementTraceView(groupRequirementTraceViewMap.get(item.getId()), allRequirementIds)));

        return ideaTraceViewResult;
    }

    /**
     * 获取需求下的子需求信息
     *
     * @param requirementTraceViews 需求信息
     * @param allRequirementIds 所有需求id，用于处理死循环问题
     * @return {@link List< IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView>} 需求信息
     * <AUTHOR>
     * @date 2022/6/13 10:48
     * @update 2022/6/13 10:48
     * @since 1.0
     */
    private List<IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView> getChildrenRequirementTraceView(
            List<IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView> requirementTraceViews,
            Set<Long> allRequirementIds)
    {
        Set<Long> requirementIds =
                requirementTraceViews.stream().filter(item -> !allRequirementIds.contains(item.getId()))
                        .map(SuperEntity::getId).collect(Collectors.toSet());

        if (requirementIds.isEmpty())
        {
            return requirementTraceViews;
        }

        allRequirementIds.addAll(requirementIds);

        List<Requirement> requirements =
                requirementMapper.selectList(Wraps.<Requirement>lbQ().in(Requirement::getParentId, requirementIds));

        // 查询关联需求信息
        Map<Long, List<IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView.RelationRequirement>>
                relationRequirementMap =
                getRelationRequirement(requirements.stream().map(SuperEntity::getId).collect(Collectors.toSet()));

        List<IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView> childRequirementTraceViews =
                BeanPlusUtil.toBeanList(requirements,
                        IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView.class);

        Map<Long, List<IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView>>
                groupRequirementTraceViewMap =
                childRequirementTraceViews.stream().collect(Collectors.groupingBy(Requirement::getParentId));

        requirementTraceViews.forEach(item ->
        {
            if (groupRequirementTraceViewMap.containsKey(item.getId()))
            {
                item.setChildren(getChildrenRequirementTraceView(groupRequirementTraceViewMap.get(item.getId()),
                        allRequirementIds));
            }
            if (relationRequirementMap.containsKey(item.getId()))
            {
                item.setRelation(relationRequirementMap.get(item.getId()));
            }
        });

        return requirementTraceViews;
    }

    @Override
    public IdeaTraceViewBaseInfoResult getTraceViewBaseInfoByIdeaId(Long ideaId)
    {
        Idea idea = baseMapper.selectById(ideaId);
        if (null == idea)
        {
            throw BizException.validFail("获取信息失败，请返回列表页面重试");
        }

        IdeaTraceViewBaseInfoResult result = BeanPlusUtil.toBean(idea, IdeaTraceViewBaseInfoResult.class);

        // 获取用户需求的所有子工作项

        // 查询用户需求直接关联的需求
        List<Requirement> requirements = ideaRequirementService.getRequirementList(ideaId);
//                requirementMapper.selectList(
//                        Wraps.<Requirement>lbQ().isNull(Requirement::getParentId).eq(Requirement::getIdeaId, ideaId));

        List<Requirement> childRequirements = getChildRequirement(requirements, new HashSet<>());

        long finishNum = childRequirements.stream().filter(item -> item.getRateProgress() == 100).count();

        result.setAllNum((long) childRequirements.size());
        result.setFinishNum(finishNum);

        return result;
    }

    /**
     * 递归查询所有子需求项
     *
     * @param requirements 父需求信息
     * @param allRequirementIds 所有需求id
     * @return {@link List< Requirement>} 需求项
     * <AUTHOR>
     * @date 2022/6/14 17:12
     * @update 2022/6/14 17:12
     * @since 1.0
     */
    private List<Requirement> getChildRequirement(List<Requirement> requirements, Set<Long> allRequirementIds)
    {
        Set<Long> requirementIds =
                requirements.stream().filter(item -> !allRequirementIds.contains(item.getId()))
                        .map(SuperEntity::getId).collect(Collectors.toSet());

        if (requirementIds.isEmpty())
        {
            return requirements;
        }

        allRequirementIds.addAll(requirementIds);

        List<Requirement> childRequirements =
                requirementMapper.selectList(Wraps.<Requirement>lbQ().in(Requirement::getParentId, requirementIds));

        if (!childRequirements.isEmpty())
        {
            requirements.addAll(getChildRequirement(childRequirements, allRequirementIds));
        }
        return requirements;
    }

    /**
     * 根据需求id查询需求关联的需求信息
     *
     * @param requirementIds 需求id
     * @return {@link Map<Long, List<IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView.RelationRequirement>>} 需求关联的需求信息
     * <AUTHOR>
     * @date 2022/6/13 13:57
     * @update 2022/6/13 13:57
     * @since 1.0
     */
    private Map<Long,
            List<IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView.RelationRequirement>> getRelationRequirement(
            Set<Long> requirementIds)
    {
        Map<Long, List<IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView.RelationRequirement>>
                result =
                Maps.newHashMapWithExpectedSize(requirementIds.size());

        // 查询关联的需求id(依赖关系)
        List<IssueItemRelation> depend = issueItemRelationMapper.selectList(
                Wraps.<IssueItemRelation>lbQ()
                        .eq(IssueItemRelation::getRelationType, IssueRelationType.DEPEND)
                        .in(IssueItemRelation::getFromId, requirementIds));

        if (!depend.isEmpty())
        {
            // 先分组
            Map<Long, List<Long>> dependIdsGroupByFormId = depend.stream().collect(
                    Collectors.groupingBy(IssueItemRelation::getFromId, HashMap::new,
                            Collectors.mapping(IssueItemRelation::getToId, Collectors.toList())));

            // 查询依赖需求信息
            List<Requirement> dependRequirements =
                    requirementMapper.selectBatchIds(depend.stream().map(IssueItemRelation::getToId).collect(
                            Collectors.toSet()));

            List<IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView.RelationRequirement>
                    dependRelationRequirements
                    = BeanPlusUtil.toBeanList(dependRequirements,
                    IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView.RelationRequirement.class);
            dependRelationRequirements.forEach(item -> item.setRelationType(IssueRelationType.DEPEND));

            dependIdsGroupByFormId.forEach((k, v) ->
                    result.put(k, dependRelationRequirements.stream().filter(item -> v.contains(item.getId()))
                            .collect(Collectors.toList()))
            );

        }
        // 查询关联的需求id(影响关系)
        List<IssueItemRelation> affect = issueItemRelationMapper.selectList(
                Wraps.<IssueItemRelation>lbQ()
                        .eq(IssueItemRelation::getRelationType, IssueRelationType.AFFECT)
                        .in(IssueItemRelation::getToId, requirementIds));

        if (!affect.isEmpty())
        {
            // 先分组
            Map<Long, List<Long>> dependIdsGroupByToId = affect.stream().collect(
                    Collectors.groupingBy(IssueItemRelation::getToId, HashMap::new,
                            Collectors.mapping(IssueItemRelation::getFromId, Collectors.toList())));

            // 查询影响需求信息
            List<Requirement> affectRequirements =
                    requirementMapper.selectBatchIds(affect.stream().map(IssueItemRelation::getFromId).collect(
                            Collectors.toSet()));

            List<IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView.RelationRequirement>
                    affectRelationRequirements
                    = BeanPlusUtil.toBeanList(affectRequirements,
                    IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView.RelationRequirement.class);
            affectRelationRequirements.forEach(item -> item.setRelationType(IssueRelationType.AFFECT));

            dependIdsGroupByToId.forEach((k, v) ->
            {
                if (result.containsKey(k))
                {
                    List<IdeaTraceViewResult.IdeaTraceView.ProjectTraceView.RequirementTraceView.RelationRequirement>
                            relationRequirements = result.get(k);
                    relationRequirements.addAll(
                            affectRelationRequirements.stream().filter(item -> v.contains(item.getId()))
                                    .collect(Collectors.toList()));
                    result.put(k, relationRequirements);
                }
                else
                {
                    result.put(k, affectRelationRequirements.stream().filter(item -> v.contains(item.getId()))
                            .collect(Collectors.toList()));
                }
            });
        }
        return result;
    }

    /**
     * 获取流转到的节点
     *
     * @param idea 用户需求
     * @param workflowId 工作流id
     * @param thisNodeId 当前节点id
     * @return List<Long> 目标节点id
     * <AUTHOR>
     * @date 2021/11/23 10:43
     * @update lxr 2021/11/23 10:43
     * @since 1.0
     */
    private List<WorkflowNode> getTargetNodeIds(Idea idea, Long workflowId, Long thisNodeId)
    {
        List<WorkflowTransition> workflowTransitions = workflowTransitionMapper.selectList(
                Wraps.<WorkflowTransition>lbQ().eq(WorkflowTransition::getWorkflowId, workflowId)
                        .eq(WorkflowTransition::getSource, thisNodeId));

        if (workflowTransitions.isEmpty())
        {
            return Collections.emptyList();
        }
        boolean remove = true;
        Iterator<WorkflowTransition> iterator = workflowTransitions.iterator();
        while (iterator.hasNext())
        {
            WorkflowTransition workflowTransition = iterator.next();
            // 查询流转权限
            List<ProjectWorkflowAuthority> projectWorkflowAuthorities = projectWorkflowAuthorityMapper.selectList(
                    Wraps.<ProjectWorkflowAuthority>lbQ().eq(ProjectWorkflowAuthority::getWorkflowId, workflowId)
                            .eq(ProjectWorkflowAuthority::getTypeClassify, TypeClassify.IDEA)
                            .eq(ProjectWorkflowAuthority::getTypeCode, idea.getTypeCode())
                            .eq(ProjectWorkflowAuthority::getTransitionId, workflowTransition.getId()));

            List<WorkflowAuthority> workflowAuthorities;
            if (projectWorkflowAuthorities.isEmpty())
            {
                workflowAuthorities = workflowAuthorityMapper.selectList(
                        Wraps.<WorkflowAuthority>lbQ()
                                .eq(WorkflowAuthority::getTransitionId, workflowTransition.getId()));
            }
            else
            {
                workflowAuthorities = BeanPlusUtil.toBeanList(projectWorkflowAuthorities, WorkflowAuthority.class);
            }
            boolean authorityBreak = false;
            for (WorkflowAuthority workflowAuthority : workflowAuthorities)
            {
                switch (workflowAuthority.getType())
                {
                    case HANDLER:
                        if (ContextUtil.getUserId().equals(idea.getHandleBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case RESPONSIBLE:
                        if (ContextUtil.getUserId().equals(idea.getLeadingBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case CREATOR:
                        if (ContextUtil.getUserId().equals(idea.getPutBy()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case USER:
                        if (ContextUtil.getUserId().equals(workflowAuthority.getUserOrRole()))
                        {
                            remove = false;
                            authorityBreak = true;
                        }
                        break;
                    case ROLE:
                        Long userId = ContextUtil.getUserId();
                        User user = userApi.findUserById(userId);
                        List<UserRole> userRoles = user.getUserRoles();
                        for (UserRole userRole : userRoles)
                        {
                            if (userRole.getRoleId().equals(workflowAuthority.getUserOrRole()))
                            {
                                remove = false;
                                authorityBreak = true;
                                break;
                            }
                        }
                        break;
                    default:
                        break;
                }
                if (authorityBreak)
                {
                    break;
                }
            }

            if (remove)
            {
                iterator.remove();
            }

        }
        if (workflowTransitions.isEmpty())
        {
            return Collections.emptyList();
        }
        return workflowTransitions.stream().map(transition -> {
            WorkflowNode workflowNode = workflowNodeMapper.selectById(transition.getTarget());

            List<ProjectWorkflowTransitionCheck> checkList = transitionCheckService.findCheckList(transition.getId(),
                    null, TypeClassify.IDEA, idea.getTypeCode());
            workflowNode.getEchoMap().put("check", checkList);
            return workflowNode;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        return CollHelper.uniqueIndex(super.listByIds(ids), Idea::getId, idea -> {
            idea.setDescription(null);
            return idea;
        });
    }

    @Override
    public Idea getById(Serializable id)
    {
        Idea idea = super.getById(id);
        if (idea != null)
        {
            idea.setFiles(fileApi.findByBizTypeAndBizId(FileBizType.IDEA_FILE_UPLOAD, id));
        }

        return idea;
    }
}
