package com.jettech.jettong.alm.project.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.base.service.SuperService;
import com.jettech.jettong.alm.project.entity.ProjectRole;

import java.util.List;
import java.util.Map;

/**
 * 项目角色信息业务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 项目角色信息业务接口
 * @projectName jettong
 * @package com.jettech.jettong.alm.project.service
 * @className ProjectRoleService
 * @date 2021-11-10
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface ProjectRoleService extends SuperService<ProjectRole>
{

    /**
     * 保存项目角色信息
     *
     * @param projectRole   项目角色信息
     * @return 项目角色信息
     * <AUTHOR>
     * @date 2021/11/10 11:48
     * @update zxy 2021/11/10 11:48
     * @since 1.0
     */
    ProjectRole saveProjectRole(ProjectRole projectRole, Long projectId);

    /**
     * 删除项目角色信息
     *
     * @param ids 项目角色id
     * <AUTHOR>
     * @date 2021/11/10 11:48
     * @update zxy 2021/11/10 11:48
     * @since 1.0
     */
    void deleteProjectRoleByIds(List<Long> ids);

    /**
     * 修改项目角色信息
     *
     * @param projectRole 项目角色信息
     * <AUTHOR>
     * @date 2021/11/10 11:57
     * @update zxy 2021/11/10 11:57
     * @since 1.0
     */
    void updateProjectRole(ProjectRole projectRole);
    /**
     * 角色菜单权限
     *
     * @param roleMenusIds 角色菜单权限
     * <AUTHOR>
     * @date 2021/11/10 11:57
     * @update zxy 2021/11/10 11:57
     * @since 1.0
     */
    void insertOrUpdateProjectRoleAuthoritys(Map<Long,List<Long>> roleMenusIds);

    /**
     * 分页查询项目角色信息
     * @param page 分页信息
     * @param params 查询条件，支持的查询条件有
     *        code:角色编号，模糊查询
     *        name:角色名称，模糊查询
     *        projectId:项目id，必填
     *        state:状态
     * @return {@link IPage< ProjectRole>} 项目角色信息
     * <AUTHOR>
     * @date 2022/6/14 15:36
     * @update 2022/6/14 15:36
     * @since 1.0
     */
    IPage<ProjectRole> findPage(IPage<ProjectRole> page, Map<String, Object> params);

    /**
     * 根据条件查询项目角色信息
     * @param params 查询条件，支持的查询条件有
     *        code:角色编号，模糊查询
     *        name:角色名称，模糊查询
     *        projectId:项目id，必填
     *        state:状态
     * @return {@link List< ProjectRole>}
     * <AUTHOR>
     * @date 2022/6/14 15:54
     * @update 2022/6/14 15:54
     * @since 1.0
     */
    List<ProjectRole> findMap(Map<String, Object> params);

    /**
     * 检查角色编号是否可用
     *
     * @param code 角色编号
     * @param typeCode 项目类型
     * @param projectId 项目id
     * @return boolean 是否可用
     * <AUTHOR>
     * @date 2021/11/10 11:57
     * @update zxy 2021/11/10 11:57
     * @since 1.0
     */
    boolean check(String code, String typeCode, Long projectId);

    /**
     * 获取PM角色的用户id
     * @param projectId
     * @return {@link List< Long>}
     * @throws
     * <AUTHOR>
     * @date 2025/9/15 12:00
     * @update ly 2025/9/15 12:00
     * @since 1.0
     */
    List<Long> getPMUserIds(Long projectId);
}
