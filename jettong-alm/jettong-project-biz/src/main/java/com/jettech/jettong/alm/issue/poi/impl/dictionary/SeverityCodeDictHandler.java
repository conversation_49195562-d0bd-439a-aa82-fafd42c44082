package com.jettech.jettong.alm.issue.poi.impl.dictionary;


import cn.hutool.core.collection.CollUtil;
import com.jettech.jettong.alm.issue.poi.AbstractSingleFieldDictHandler;
import com.jettech.jettong.base.api.DictionaryApi;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.common.constant.DictionaryType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.alm.issue.poi.impl
 * @className DictionaryFieldDictHandler
 * @date 2025/9/11 17:37
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
@RequiredArgsConstructor
public class SeverityCodeDictHandler extends AbstractSingleFieldDictHandler
{

    private static final Set<String> dictList = CollUtil.newHashSet("severityCode");

    private final DictionaryApi dictionaryApi;

    @Override
    public Supplier<Map<String, String>> dataSupplier() {
        //驼峰转下划线大写
        return () -> dictionaryApi.query(Dictionary.builder().type(DictionaryType.SEVERITY_CODE).build()).stream().collect(
                Collectors.toMap(
                        item -> String.valueOf(item.getCode()),
                        item -> String.format("%s(%s)", item.getName(), item.getCode())
                ));

    }

    @Override
    protected Set<String> getDictList() {
        return dictList;
    }

}