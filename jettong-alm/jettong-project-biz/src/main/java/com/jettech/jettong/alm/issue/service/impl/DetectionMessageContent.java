package com.jettech.jettong.alm.issue.service.impl;

import com.jettech.basic.base.BaseEnum;
import com.jettech.basic.exception.ArgumentException;
import com.jettech.basic.utils.SpringUtils;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.project.service.ProjectInfoService;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 评论通知用户用户的内容
 *
 * <AUTHOR>
 * @version 1.0
 * @description 评论通知用户用户的内容
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.issue.service.impl
 * @className DetectionMessageContent
 * @date 2022/4/21 11:28
 * @copyright 2022 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
public class DetectionMessageContent {

    private final ACTION action;
    private final TYPE type;

    public static DetectionMessageContent create(ACTION action, TYPE type) {
        if (action == null || type == null) {
            throw new ArgumentException("action and type can not be empty");
        }
        return new DetectionMessageContent(action, type);
    }


    /**
     * 构建前端用来跳转的特殊字符串
     * 格式为：[#{资源code}|{资源ID}|{资源名称}|{资源类型}|{projectCode}|{projectTypeCode}|{projectId}]
     *
     * @param code      资源code
     * @param name      资源name
     * @param id        资源id
     * @param type      资源type
     * @param projectId 项目id，可为空
     * @return  拼接后的字符串
     */
    public static String buildJumpString(String code, String name, Long id, TypeClassify type, Long projectId,String itemTypeCode) {

        StringBuilder sb = new StringBuilder();
        sb.append("[#").append(code);
        sb.append("|").append(id);
        sb.append("|").append(name);
        sb.append("|").append(type.getCode());
        sb.append("|").append(itemTypeCode);

        Optional.ofNullable(projectId)
                .map(pId -> SpringUtils.getBean(ProjectInfoService.class).findById(pId))
                .ifPresent(projectInfo -> {
                    sb.append("|").append(projectInfo.getCode());
                    sb.append("|").append(projectInfo.getTypeCode());
                    sb.append("|").append(projectInfo.getId());
                });

        sb.append("]");
        return sb.toString();
    }

    private DetectionMessageContent(ACTION action, TYPE type) {
        this.action = action;
        this.type = type;
    }

    public String title() {
        return type.title();
    }

    public String formatBody(String username, TypeClassify typeClassify, String sourceName) {
        return String.format(type.getBody(action), username, typeClassify.getDesc(), sourceName);
    }

    @Getter
    public enum TYPE {
        /**
         * 相关人员的通知
         */
        COMMENTED("评论提醒",
                "%1$s在您负责的%2$s【%3$s】中发表了评论",
                "%1$s更新了在您负责的%2$s【%3$s】中的评论",
                "%1$s删除了在您负责的%2$s【%3$s】中的评论"),
        /**
         * 被回复人员的通知
         */
        REPLIED("回复提醒",
                "%1$s在%2$s【%3$s】中回复了您的评论",
                "%1$s更新了在%2$s【%3$s】中的对您的回复",
                "%1$s删除了在%2$s【%3$s】中的对您的回复"
        ),
        /**
         * 被@人员的通知
         */
        MENTIONED("评论提醒",
                " %1$s 在%2$s【%3$s】的评论中提到了你",
                "%1$s更新了在%2$s【%3$s】中的评论",
                "%1$s删除了在%2$s【%3$s】中的评论"
        );

        private final String title;
        private final String addBody;
        private final String editBody;
        private final String deleteBody;
        private final Map<ACTION, String> actionMap = new HashMap<>();

        TYPE(String title, String addBody, String editBody, String deleteBody) {
            this.title = title;
            this.addBody = addBody;
            this.editBody = editBody;
            this.deleteBody = deleteBody;

            actionMap.put(ACTION.ADD, addBody);
            actionMap.put(ACTION.EDIT, editBody);
            actionMap.put(ACTION.DELETE, deleteBody);
        }

        public String getBody(ACTION action) {
            return actionMap.get(action);
        }

        public String title() {
            return title;
        }
    }

    public enum ACTION implements BaseEnum {
        /**
         * 新增评论（回复）操作
         */
        ADD("新增"),
        /**
         * 编辑评论（回复）操作
         */
        EDIT("编辑"),
        /**
         * 删除评论（回复）操作
         */
        DELETE("删除");

        private final String desc;

        ACTION(String desc) {
            this.desc = desc;
        }

        @Override
        public String getDesc() {
            return desc;
        }
    }

}
