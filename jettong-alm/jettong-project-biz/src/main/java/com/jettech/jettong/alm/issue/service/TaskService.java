package com.jettech.jettong.alm.issue.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jettech.basic.base.service.SuperService;
import com.jettech.jettong.alm.issue.dto.TaskCountQuery;
import com.jettech.jettong.alm.issue.dto.WorkItemDTO;
import com.jettech.jettong.alm.issue.entity.Task;
import com.jettech.jettong.alm.issue.vo.TaskStateComponentResult;
import com.jettech.jettong.alm.issue.vo.IssueTypeComponentResult;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;

import java.util.List;

/**
 * 任务信息业务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 任务信息业务接口
 * @projectName jettong
 * @package com.jettech.jettong.alm.issue.service
 * @className TaskService
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface TaskService extends SuperService<Task>
{

    /**
     * 任务状态流转
     *
     * @param taskId 任务id
     * @param sourceStateCode 源状态code
     * @param targetStateCode 目标状态code
     * <AUTHOR>
     * @date 2021/11/15 15:29
     * @update zxy 2021/11/15 15:29
     * @since 1.0
     */
    void transitionState(Long taskId, String sourceStateCode, String targetStateCode);

    /**
     * 重新计算任务是否延期
     *
     * <AUTHOR>
     * @date 2021/11/12 10:26
     * @update zxy 2021/11/12 10:26
     * @since 1.0
     */
    void calculationDelay();

    /**
     * 根据任务id获取当前登录人可流转到的状态
     *
     * @param taskId 任务id
     * @return List<WorkflowNode>
     * <AUTHOR>
     * @date 2021/11/15 11:22
     * @update zxy 2021/11/15 11:22
     * @since 1.0
     */
    List<WorkflowNode> findNextNode(Long taskId);


    /**
     * 根据项目id查询任务概览信息
     *
     * @param projectId 项目id
     * @return IssueTypeComponentResult 任务概览信息
     * <AUTHOR>
     * @date 2021/12/2 17:57
     * @update zxy 2021/12/2 17:57
     * @since 1.0
     */
    IssueTypeComponentResult findTypeByProjectId(Long projectId);

    /**
     * 根据项目id查询任务状态人员分布信息
     *
     * @param projectId 项目id
     * @return TaskStateComponentResult 任务状态人员分布信息
     * <AUTHOR>
     * @date 2021/12/2 17:57
     * @update zxy 2021/12/2 17:57
     * @since 1.0
     */
    TaskStateComponentResult findStateByProjectId(Long projectId);

    /**
     * 根据产品id查询任务概览信息
     *
     * @param productId 产品id
     * @return IssueTypeComponentResult 任务概览信息
     * <AUTHOR>
     * @date 2021/12/03 16:27
     * @update lxr 2021/12/03 16:27
     * @since 1.0
     */
    IssueTypeComponentResult findTaskComponentByProductId(Long productId);

    /**
     * 根据计划id查询任务概览信息
     *
     * @param planId 计划id
     * @return IssueTypeComponentResult 任务概览信息
     * <AUTHOR>
     * @date 2021/12/04 16:27
     * @update lxr 2021/12/04 16:27
     * @since 1.0
     */
    IssueTypeComponentResult findTaskComponentByPlanId(Long planId);

    /**
     * 根据计划id查询任务状态人员分布信息
     *
     * @param planId 计划id
     * @return TaskStateComponentResult 任务状态人员分布信息
     * <AUTHOR>
     * @date 2021/12/04 15:20
     * @update lxr 2021/12/04 15:20
     * @since 1.0
     */
    TaskStateComponentResult findStateByPlanId(Long planId);

    Page<Task> findPageByQuery(IPage<Task> page, TaskCountQuery query);

    Page<WorkItemDTO> findHourPageByQuery(IPage<WorkItemDTO> page, TaskCountQuery query);

    /**
     * 获取具有流转权限角色的用户id
     * @param task
     * @return {@link List< Long>}
     * @throws
     * <AUTHOR>
     * @date 2025/8/7 10:49
     * @update sxh 2025/8/7 10:49
     * @since 1.0
     */
    List<Long> getWorkFlowUserIds(Task task);

    /**
     * 处理任务处理人
     * @param task
     * @return
     * @throws
     * <AUTHOR>
     * @date 2025/9/15 20:06
     * @update  2025/9/15 20:06
     * @since 1.0
     */
    void dealHandleBy(Task task);

}
