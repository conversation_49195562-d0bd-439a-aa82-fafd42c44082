<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jettong-alm</artifactId>
        <groupId>com.jettech.jettong</groupId>
        <version>develop</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>jettong-project-biz</artifactId>
    <name>${project.artifactId}</name>
    <description>项目协同-业务模块</description>

    <dependencies>
        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-workflow-biz</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-kanban-biz</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-alm-entity</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-base-api</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-tm-base-api</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-product-api</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-testm-api</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-cmdb-api</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-wiki-api</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-boot-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-databases</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-cache-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-view-starter</artifactId>
            <version>${jettong-util.version}</version>
        </dependency>

        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-echo-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jettech.basic</groupId>
            <artifactId>jettong-job-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-tm-base-biz</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.jettech.jettong</groupId>
            <artifactId>jettong-workinghours-biz</artifactId>
            <version>${jettong-project.version}</version>
        </dependency>

        <dependency>
            <groupId>eu.bitwalker</groupId>
            <artifactId>UserAgentUtils</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
            <scope>compile</scope>
        </dependency>

    </dependencies>

</project>
