package com.jettech.jettong.wiki.service.document.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.TreeUtil;
import com.jettech.jettong.alm.api.WorkflowApi;
import com.jettech.jettong.common.util.auth.enumeration.AuthType;
import com.jettech.jettong.wiki.dao.document.*;
import com.jettech.jettong.wiki.document.dto.*;
import com.jettech.jettong.wiki.document.entity.*;
import com.jettech.jettong.wiki.document.enumeration.*;
import com.jettech.jettong.wiki.service.document.CatalogService;
import com.jettech.jettong.wiki.template.dto.TemplateSaveBizDTO;
import com.jettech.jettong.wiki.utils.HeadingTextReplacer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 知识库文档目录业务层
 * <AUTHOR>
 * @version 1.0
 * @description 知识库文档目录业务层
 * @projectName jettong
 * @package com.jettech.jettong.wiki.document.service.impl
 * @className CatalogServiceImpl
 * @date 2025-07-18
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
@RefreshScope
public class CatalogServiceImpl extends SuperServiceImpl<CatalogMapper, Catalog> implements CatalogService
{


    private final DocumentMapper documentMapper;
    private final DocumentAuthorityMapper documentAuthorityMapper;
    private final DocumentRoleMapper documentRoleMapper;
    private final DocumentRecycleBinMapper  documentRecycleBinMapper;
    private final DocumentVersionMapper documentVersionMapper;
    private final WorkflowApi workflowApi;
    private final List<DocumentComponentTaskHandlerImpl> documentComponentTaskHandlers;
    private final List<DocumentComponentModuleFunctionHandlerImpl> documentComponentModuleFunctionHandlers;

    @Value("${wiki.catalog.workflowId:8}")
    private Long workFlowId;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(Catalog entity){
        if (StrUtil.isEmpty(entity.getStateCode())){
            String startStateCode = workflowApi.getStartStateCode(workFlowId);
            if (startStateCode == null){
                throw BizException.validFail("获取工作流失败，请联系管理员");
            }
            entity.setStateCode(startStateCode);
        }
        //文档目录下不能新增
        if(entity.getParentId() != null&&entity.getParentId() != 0){
            Catalog parent = this.getById(entity.getParentId());
            if(parent.getType() == CatalogLevelType.DOCUMENT){
                throw new BizException("文档目录下不能新增");
            }
            entity.setParentIdPath(parent.getParentIdPath(), parent.getId());
        }
        super.save(entity);
        if (StrUtil.isNotEmpty(entity.getContent())){
            //新加文档
            Document document = Document.builder().catalogId(entity.getId())
                    .content(entity.getContent())
                    .version(1)
                    .sort(1)
                    .build();
            documentMapper.insert( document);
        }
        return Boolean.TRUE;
    }


    @Override
    public boolean updateById(Catalog entity){
        //对比name有没有变化
        if (!entity.getName().equals(super.getById(entity.getId()).getName())){
            //修改文档标题
            Document document = documentMapper.selectOne(
                    Wraps.<Document>lbQ().eq(Document::getCatalogId, entity.getId()).orderByAsc(Document::getSort)
                            .last("limit 1"));
            if (document != null){
                document.setContent(HeadingTextReplacer.replaceFirstHeadingText(document.getContent(), entity.getName()));
                documentMapper.updateById(document);
            }
        }
        return super.updateById(entity);
    }

    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList){
        List<Catalog> catalogs = listByIds(idList);
        if (CollUtil.isEmpty(catalogs)){
            return Boolean.TRUE;
        }
        //保存回收站
        List<DocumentRecycleBin> documentRecycleBins = BeanUtil.copyToList(catalogs, DocumentRecycleBin.class);
        documentRecycleBinMapper.insertBatchSomeColumn(documentRecycleBins);
        return super.removeByIds(idList);
    }

    @Override
    public List<Document> getDocumentsByCatalogId(Long catalogId)
    {
        Catalog catalog = getById(catalogId);
        if (catalog == null){
            throw new BizException("目录不存在");
        }
        //如果不是目录 返回直属文档
        if (catalog.getType() != CatalogLevelType.CATALOG){
            List<Document> documentList =
                    documentMapper.selectList(Wraps.<Document>lbQ().eq(Document::getCatalogId, catalogId));
            documentList.forEach(document -> dealDocument( document));
            return documentList;
        }
        List<Catalog> catalogList = list(Wraps.<Catalog>lbQ().eq(Catalog::getSpaceId, catalog.getSpaceId())
                .eq(catalog.getBizId() != null, Catalog::getBizId, catalog.getBizId())
                .eq(catalog.getBizType() != null, Catalog::getBizType, catalog.getBizType())
                .gt(Catalog::getLevel, catalog.getLevel()));
        catalogList.add( catalog);
        Catalog catalogs = TreeUtil.buildTree(catalogList).stream().filter(c->c.getId().compareTo(catalogId)==0).findFirst().get();
        List<Long> ids = new ArrayList<>();
        traverseNode(catalogs, node -> ids.add(node.getId()));
        List<Document> documentList = documentMapper.selectList(Wraps.<Document>lbQ().in(Document::getCatalogId, ids));
        documentList.forEach(document -> dealDocument( document));
        Map<Long, List<Document>> documentMap = documentList.stream().collect(Collectors.groupingBy(Document::getCatalogId));
        List<Document> documents = new LinkedList<>();
        //按照目录顺序组装文档
        traverseNodeAddDocument(catalogs, documentMap, documents);
        return documents;
    }

    @Override
    public List<Document> getAllDocumentsByBiz(CatalogQuery query)
    {
        List<Catalog> catalogList = list(Wraps.<Catalog>lbQ().eq(Catalog::getSpaceId, query.getSpaceId())
                .eq(Catalog::getBizId, query.getBizId())
                .eq(Catalog::getBizType, query.getBizType()));
        if (CollUtil.isEmpty(catalogList)){
            return null;
        }
        List<Catalog> catalogs = TreeUtil.buildTree(catalogList);
        List<Long> ids = new ArrayList<>();
        catalogs.forEach(catalog -> {
            traverseNode(catalog, node -> ids.add(node.getId()));
        });
        List<Document> documentList = documentMapper.selectList(Wraps.<Document>lbQ().in(Document::getCatalogId, ids));
        documentList.forEach(document -> dealDocument( document));
        Map<Long, List<Document>> documentMap = documentList.stream().collect(Collectors.groupingBy(Document::getCatalogId));
        List<Document> documents = new LinkedList<>();
        //按照目录顺序组装文档
        catalogs.forEach(catalog -> {
            traverseNodeAddDocument(catalog, documentMap, documents);
        });
        return documents;
    }

    @Override
    public List<Document> getDocumentHistoryByCatalogId(Long catalogId)
    {
        List<DocumentVersion> documentVersions = documentVersionMapper.selectList(
                Wraps.<DocumentVersion>lbQ().eq(DocumentVersion::getCatalogId, catalogId)
                        .orderByDesc(DocumentVersion::getVersion));
        List<Document> documents = documentVersions.stream().map(
                documentVersion -> {
                    Document document = BeanUtil.toBean(documentVersion, Document.class);
                    document.setId(documentVersion.getDocumentId());
                    return document;
                }
        ).collect(Collectors.toList());
        documents.forEach(document -> dealDocument( document));
        return documents;
    }

    public void dealDocument(Document document){
        if (!JSONUtil.isTypeJSON(document.getContent())){
            return;
        }
        List<JSONObject> list = JSONUtil.toList(document.getContent(), JSONObject.class);
        if (CollUtil.isEmpty(list)){
            return ;
        }

        // 使用ListIterator直接修改原始集合
        ListIterator<JSONObject> iterator = list.listIterator();
        while (iterator.hasNext()) {
            JSONObject item = iterator.next();
            if ("customTable".equals(item.get("type"))) {
                DocumentCustomTable customTable = BeanUtil.toBean(item, DocumentCustomTable.class);
                Optional<DocumentComponentTaskHandlerImpl> handlerOpt = documentComponentTaskHandlers.stream()
                        .filter(handler -> handler.getComponentName().equals(customTable.getAttrs().getTableType())).findFirst();
                if (handlerOpt.isPresent()){
                    handlerOpt.get().assemble(customTable);
                    iterator.set(BeanUtil.toBean(customTable, JSONObject.class));
                }
            }else  if ("tradeTable".equals(item.get("type"))) {
                DocumentCustomTable tradeTable = BeanUtil.toBean(item, DocumentCustomTable.class);
                Optional<DocumentComponentModuleFunctionHandlerImpl> handlerOpt = documentComponentModuleFunctionHandlers.stream()
                        .filter(handler -> handler.getComponentName().equals(tradeTable.getAttrs().getTableType())).findFirst();
                if (handlerOpt.isPresent()){
                    handlerOpt.get().assemble(tradeTable);
                    iterator.set(BeanUtil.toBean(tradeTable, JSONObject.class));
                }
            }
        }
        document.setContent(JSONUtil.toJsonStr(list));
    }

    private static void traverseNode(Catalog node, Consumer<Catalog> action) {
        action.accept(node);
        if (node.getChildren() != null) {
            node.getChildren().forEach(child -> traverseNode(child, action));
        }
    }

    private static void traverseNodeAddDocument(Catalog node, Map<Long, List<Document>> documentMap,List<Document> documents) {
        if (documentMap.get(node.getId())!=null){
            documents.addAll(documentMap.get(node.getId()));
        }
        if (node.getChildren() != null) {
            node.getChildren().forEach(child -> traverseNodeAddDocument(child, documentMap, documents));
        }
    }


    @Override
    public List<Catalog> getCatalogsByBiz(CatalogQuery query)
    {
        List<Catalog> catalogs = this.list(Wraps.<Catalog>lbQ().eq(Catalog::getSpaceId, query.getSpaceId()).eq(Catalog::getBizId, query.getBizId()).eq(Catalog::getBizType, query.getBizType()));
        if (CollUtil.isEmpty(catalogs)){
            return catalogs;
        }
        SpringUtil.getBean(EchoService.class).action(catalogs);
        List<Long> catalogIds = catalogs.stream().map(SuperEntity::getId).collect(Collectors.toList());
        //构建目录树
        List<Catalog> tree = TreeUtil.buildTree(catalogs);
        List<DocumentAuthority> documentAuthorities = documentAuthorityMapper.selectList(
                Wraps.<DocumentAuthority>lbQ().eq(DocumentAuthority::getAuthId, query.getUserId())
                        .eq(DocumentAuthority::getAuthType, AuthType.USER)
                        .in(DocumentAuthority::getLevelId, catalogIds)
                        .eq(DocumentAuthority::getLevelType, DocumentAuthorityLevelType.CATALOG));
        Map<Long, List<DocumentAuthority>> authorityMap = documentAuthorities.stream().collect(Collectors.groupingBy(DocumentAuthority::getLevelId));
        Map<Long, Integer> documentRoleMap = documentRoleMapper.selectList(Wraps.<DocumentRole>lbQ())
                .stream().collect(Collectors.toMap(DocumentRole::getId, DocumentRole::getPermissions));
        Integer maxPermission = Collections.max(documentRoleMap.values());
        //递归树 赋值权限
        List<Catalog> resultTree = new ArrayList<>();
        for (Catalog root : tree) {
            // 处理树节点并过滤
            Catalog processed = processTreeNode(
                    root,
                    authorityMap,
                    documentRoleMap,
                    maxPermission,
                    query.getUserId(),
                    null
            );
            if (processed != null) {
                resultTree.add(processed);
            }
        }

        return resultTree;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveBatch(List<CatalogSaveDTO> catalogs)
    {
        String startStateCode = workflowApi.getStartStateCode(workFlowId);
        if (startStateCode == null){
            throw BizException.validFail("获取工作流失败，请联系管理员");
        }
        catalogs.forEach(dto -> dto.setStateCode(startStateCode));
        for (CatalogSaveDTO rootNode : catalogs) {
            saveCatalogNode(rootNode, null);
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLeadingBy(List<CatalogUpdateLeadingByDTO> updateDTO)
    {
        for (CatalogUpdateLeadingByDTO dto : updateDTO) {
            update(Wraps.<Catalog>lbU().eq(Catalog::getId, dto.getId()).set(Catalog::getLeadingBy, dto.getLeadingBy()));
        }
        return Boolean.TRUE;
    }

    @Override
    public boolean updateCollaborator(List<CatalogUpdateRoleDTO> updateDTO)
    {
        updateDTO.stream().filter(CatalogUpdateRoleDTO::getRemove).forEach(dto -> {
            documentAuthorityMapper.delete(Wraps.<DocumentAuthority>lbQ().eq(DocumentAuthority::getLevelId, dto.getId())
                    .eq(DocumentAuthority::getLevelType, DocumentAuthorityLevelType.CATALOG)
                    .eq(DocumentAuthority::getAuthType, DocumentAuthorityAuthType.USER)
                    .eq(DocumentAuthority::getAuthId, dto.getUserId()));
        });
        Map<DocumentRoleType, Long> roleMap = documentRoleMapper.selectList(null).stream()
                .collect(Collectors.toMap(DocumentRole::getCode, DocumentRole::getId));
        updateDTO.stream().filter(dto -> !dto.getRemove()).forEach(dto -> {
            DocumentAuthority documentAuthority = DocumentAuthority.builder()
                    .levelId(dto.getId())
                    .levelType(DocumentAuthorityLevelType.CATALOG)
                    .authId(dto.getUserId())
                    .authType(DocumentAuthorityAuthType.USER)
                    .roleId(roleMap.get(dto.getRole()))
                    .build();
            documentAuthorityMapper.insert(documentAuthority);
        });
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSort(CatalogUpdateDTO sortDTO)
    {
        //找出比当前排序大的
        List<Catalog> catalogs = list(Wraps.<Catalog>lbQ().select(Catalog::getId).ge(Catalog::getSort, sortDTO.getSort()).eq(Catalog::getBizType,sortDTO.getBizType())
                .eq(Catalog::getBizId,sortDTO.getBizId()).eq(Catalog::getParentId,sortDTO.getParentId()));
        update(Wraps.<Catalog>lbU().eq(Catalog::getId, sortDTO.getId()).set(Catalog::getSort, sortDTO.getSort()));
        if (CollUtil.isEmpty(catalogs)){
            return;
        }
        Integer sort = sortDTO.getSort();
        //排序+1
        for (Catalog catalog : catalogs)
        {
            catalog.setSort(++sort);
        }
        updateBatchById(catalogs);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void useTemplate(TemplateSaveBizDTO templateSaveDTO)
    {
        List<Catalog> catalogs = list(Wraps.<Catalog>lbQ().eq(Catalog::getBizId, templateSaveDTO.getId())
                        .eq(Catalog::getBizType, CatalogBizType.TEMPLATE));
        if (CollUtil.isEmpty(catalogs))
        {
            throw new BizException("模板内容不能为空");
        }

        String startStateCode = workflowApi.getStartStateCode(workFlowId);
        if (startStateCode == null){
            throw BizException.validFail("获取工作流失败，请联系管理员");
        }
        Map<Long, List<Document>> documentMap = documentMapper.selectList(
                        Wraps.<Document>lbQ().in(Document::getCatalogId,
                                catalogs.stream().map(SuperEntity::getId).collect(Collectors.toList())))
                .stream().collect(Collectors.groupingBy(Document::getCatalogId));
        Map<Long,Long> relationMap = new HashMap<>();
        catalogs.forEach(catalog -> {
            Long id = catalog.getId();
            catalog.setBizType(templateSaveDTO.getBizType());
            catalog.setBizId(templateSaveDTO.getBizId());
            catalog.setId(null);
            catalog.setStateCode(startStateCode);
            catalog.setLeadingBy(ContextUtil.getUserId());
            catalog.setCreateTime(LocalDateTime.now());
            catalog.setUpdateTime(LocalDateTime.now());
            catalog.setCreatedBy(ContextUtil.getUserId());
            catalog.setUpdatedBy(ContextUtil.getUserId());
            save(catalog);
            relationMap.put(id,catalog.getId());
            List<Document> documents = documentMap.get(id);
            if (CollUtil.isNotEmpty( documents)){
                documents.forEach(document -> {
                    document.setCatalogId(catalog.getId());
                    document.setVersion(1);
                    document.setId(null);
                    document.setCreateTime(LocalDateTime.now());
                    document.setUpdateTime(LocalDateTime.now());
                    document.setCreatedBy(ContextUtil.getUserId());
                    document.setUpdatedBy(ContextUtil.getUserId());
                    documentMapper.insert(document);
                    dealDocument( document);
                });
            }
        });
        //处理父子关系
        catalogs.forEach(catalog -> {
            if (catalog.getParentId() != null){
                catalog.setParentId(relationMap.get(catalog.getParentId()));
            }
        });
        List<Catalog> tree = TreeUtil.buildTree(catalogs);
        tree.forEach(catalog -> buildParentPath(catalog,null));
        this.updateBatchById(catalogs);
    }

    private void buildParentPath(Catalog catalog,Catalog parent) {
        if (parent != null){
            catalog.setParentIdPath(parent.getParentIdPath(),parent.getId());
        }
        if (CollUtil.isNotEmpty(catalog.getChildren())){
            catalog.getChildren().forEach(child -> buildParentPath(child,catalog));
        }

    }

    private void saveCatalogNode(CatalogSaveDTO dto, Catalog parent) {
        // 1. 转换DTO为实体
        Catalog entity = BeanUtil.toBean(dto, Catalog.class);

        // 2. 设置父级关联
        if (parent != null) {
            entity.setParentId(parent.getId());
            entity.setLevel(parent.getLevel() + 1); // 层级+1
            entity.setParentIdPath(parent.getParentIdPath(), parent.getId());
        } else {
            // 根节点处理
            entity.setLevel(1); // 根节点层级为1
        }

        // 3. 保存当前节点
        if (entity.getId() != null) {
            // 更新操作
            this.updateById(entity);
        } else {
            // 新增操作
            this.save(entity);
        }
        // 4. 递归保存子节点
        if (dto.getChildren() != null && !dto.getChildren().isEmpty()) {
            for (CatalogSaveDTO child : dto.getChildren()) {
                saveCatalogNode(child, entity);
            }
        }
    }


    // ========== 增强的递归处理方法 ==========
    private Catalog processTreeNode(Catalog node,
            Map<Long, List<DocumentAuthority>> authorityMap,
            Map<Long, Integer> roleMap,
            Integer maxPermission,
            Long currentUserId,
            Integer parentPermission
    ) {
        // 检查当前用户是否是负责人
        if (node.getLeadingBy()!=null&&node.getLeadingBy().compareTo(currentUserId)==0) {
            // 负责人权限最高优先级
            node.setPermissions(maxPermission);
        } else {
            // 普通权限处理
            List<DocumentAuthority> nodeAuthorities = authorityMap.get(node.getId());
            if (CollUtil.isNotEmpty(nodeAuthorities)) {
                // 处理直接权限
                int permissions = Collections.max(nodeAuthorities.stream().map(DocumentAuthority::getRoleId).map(roleId -> roleMap.get(roleId))
                                .collect(Collectors.toList()));

                node.setPermissions(parentPermission==null?Math.max(parentPermission, permissions): permissions);
            } else if (parentPermission != null) {
                // 继承父节点权限
                node.setPermissions(parentPermission);
            }
            // 否则保持无权限状态
        }

        // 准备传递给子节点的权限信息
        Integer currentPermission = node.getPermissions();
        // 递归处理子节点
        if (CollUtil.isNotEmpty(node.getChildren())) {
            List<Catalog> visibleChildren = new ArrayList<>();
            for (Catalog child : node.getChildren()) {
                Catalog processedChild = processTreeNode(
                        child,
                        authorityMap,
                        roleMap,
                        maxPermission,
                        currentUserId,
                        currentPermission         // 传递当前节点权限作为子节点的父权限
                );
                visibleChildren.add(processedChild);
            }
            node.setChildren(visibleChildren);
        }

        return node;
    }


}
