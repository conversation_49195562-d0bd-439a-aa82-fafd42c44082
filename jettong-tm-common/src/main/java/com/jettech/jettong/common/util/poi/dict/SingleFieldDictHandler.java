package com.jettech.jettong.common.util.poi.dict;

import cn.afterturn.easypoi.handler.inter.IExcelDictHandler;

import java.util.Map;
import java.util.function.Supplier;

/**
 * 单个字段处理类
 * CompositeRouteLocator
 * <AUTHOR>
 * @version 1.0
 * @description 单个字段处理类
 * @projectName jettong-tm
 * @package com.jettech.jettong.alm.controller.poi.issue.linked
 * @className SingleFieldDictHandler
 * @date 2022/9/22 下午5:52
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface SingleFieldDictHandler extends IExcelDictHandler {

    /**
     * 初始数据的生产器，一般多为api或查询数据库，不建议后续多次调用
     *
     * @return 初始数据
     */
    Supplier<Map<String, String>> dataSupplier();

    /**
     * 当前类是否能处理对应的dict
     *
     * @param dict 标识
     * @return  是否能处理
     */
    boolean enable(String dict);

    /**
     * 获取下拉框数据
     *
     * @param projectId
     * @return
     */
    Map<String, String[]> dropMap(Long projectId);

    /**
     * 校验输入的值能否映射数据库中的值
     *
     * @param name
     * @param val  输入的值
     * @return 是否能映射
     */
    boolean verify(String name, String val);

    /**
     * 清理线程、缓存
     */
    void clear();

}
