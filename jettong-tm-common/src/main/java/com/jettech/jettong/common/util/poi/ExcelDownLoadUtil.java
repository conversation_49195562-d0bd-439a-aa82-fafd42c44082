package com.jettech.jettong.common.util.poi;

import cn.hutool.core.io.IoUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Map;

import static org.apache.poi.ss.usermodel.Font.COLOR_RED;

/**
 * 导出Excl文件公共类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 导出Excl文件公共类
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.common.util.poi
 * @className ExcelDownLoadUtil
 * @date 2021/12/1 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class ExcelDownLoadUtil
{

    /**
     * 导出文件
     *
     * @param response HttpServletResponse
     * @param workbook workbook
     * @param fileName 文件名称
     * <AUTHOR>
     * @date 2021/12/1 9:43
     * @update zxy 2021/12/1 9:43
     * @since 1.0
     */
    public static void export(HttpServletResponse response, Workbook workbook, String fileName) throws IOException
    {
        response.setHeader("Content-Disposition",
                "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setContentType("application/vnd.ms-excel;charset=UTF-8");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        try(BufferedOutputStream bufferedOutPut = new BufferedOutputStream(response.getOutputStream()))
        {
            workbook.write(bufferedOutPut);
            bufferedOutPut.flush();
        }
        finally
        {
            IoUtil.close(workbook);
        }
    }
    /**
     * 设置下拉数据
     *
     * @param workbook
     * @param dropDownMap
     * @return
     */
    public static Workbook makeDropDownMap(Workbook workbook, Map<Integer, Map<Integer, String[]>> dropDownMap,int firstRow) {
        CellStyle allCellStyle = workbook.createCellStyle();
        DataFormat format = workbook.createDataFormat();
        allCellStyle.setDataFormat(format.getFormat("@"));
        for (Map.Entry<Integer, Map<Integer, String[]>> sheetEntry : dropDownMap.entrySet()) {
            int sheetIndex = sheetEntry.getKey();
            // 设置列的默认格式
            Sheet sheet = workbook.getSheetAt(sheetIndex);
            for (int i = 0; i < 200; i++) {
                sheet.setDefaultColumnStyle(i, allCellStyle);
            }

            for (Map.Entry<Integer, String[]> dropDownEntry : sheetEntry.getValue().entrySet()) {
                ExcelExportPlusUtil.addDropDownList(workbook, sheet, dropDownEntry.getValue(), firstRow, 65535,
                        dropDownEntry.getKey());
            }
            Row row = sheet.getRow(0);
            // 设置title单元格行高
            row.setHeightInPoints(60);
            CellStyle cellStyle = row.getCell(0).getCellStyle();
            // 设置title单元格\n强制换行
            cellStyle.setWrapText(true);
            Font font = workbook.createFont();
            font.setBold(true);
            font.setColor(COLOR_RED);
            cellStyle.setFont(font);
            // 设置title单元格居左
            cellStyle.setAlignment(HorizontalAlignment.LEFT);


        }
        return workbook;
    }
    /***
     * 筛选显示列
     * @param originalWorkbook
     * @param columnIndexes
     * @return
     */
    public static Workbook exportSpecificColumns(Workbook originalWorkbook, int... columnIndexes) {
        // 创建新工作簿
        SXSSFWorkbook newWorkbook = new SXSSFWorkbook(100);
        Sheet newSheet = newWorkbook.createSheet();
        // 创建样式
        CellStyle centerStyle = newWorkbook.createCellStyle();
        centerStyle.setAlignment(HorizontalAlignment.CENTER);
        centerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 创建第一行并合并六个单元格
        Row newRow = newSheet.createRow(0);
        newSheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 5));
        newRow.createCell(0).setCellValue("1、导入模板不同项目不能通用\n2、带*的列必须填写\n3、测试步骤类型为文本类型时，直接填入内容；当测试步骤类型为条目时，测试步骤输入如下格式：1,xxxx,xxxx;2,xxxx,xxxx;");
        // 设置title单元格行高
        newRow.setHeightInPoints(47);
        CellStyle cellStyle = newRow.getCell(0).getCellStyle();
        // 设置title单元格\n强制换行
        cellStyle.setWrapText(true);
        // 获取原始数据
        Sheet originalSheet = originalWorkbook.getSheetAt(0);
        // 处理每行数据
        for (int i = 1; i <= originalSheet.getLastRowNum(); i++) {
            Row originalRow = originalSheet.getRow(i);
            newRow = newSheet.createRow(i+1);
            newRow.setHeightInPoints(47);
            // 只复制指定列
            for (int j = 0; j < columnIndexes.length; j++) {
                newSheet.setColumnWidth(j, 20 * 256);
                Cell originalCell = originalRow.getCell(columnIndexes[j]);
                Cell newCell = newRow.createCell(j);
                if (originalCell != null) {
                    newCell.setCellValue(originalCell.getStringCellValue());
                    newCell.setCellStyle(centerStyle);
                }
            }
        }
        Sheet sheet = newWorkbook.getSheetAt(0); // 获取Sheet
        removeEmptyRow(sheet, 1); // 删除第二行空白行（行索引从0开始）
        return newWorkbook;
    }

    /**
     * 删除空白行
     * @param sheet
     * @param rowIndex
     */
    public static void removeEmptyRow(Sheet sheet, int rowIndex) {
        Row row = sheet.getRow(1); // 第二行（索引从0开始）
        if (row != null) { // 检查行是否存在
            sheet.removeRow(row);
        }
    }
}
