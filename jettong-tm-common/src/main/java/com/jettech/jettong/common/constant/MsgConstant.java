package com.jettech.jettong.common.constant;


/**
 * <AUTHOR>
 * @version 1.0
 * @description 消息常量
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.common.constant
 * @className MsgConstant
 * @date 2025/9/18 9:58
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface MsgConstant
{
    //"您负责的"
    String LEADER_BY = "您负责的";

    //您处理的
    String HANDLE_BY = "您处理的";


    // 内容模板："%s创建了一个%s任务【%s】"
    String TASK_CREATE_TEMPLATE = "%s创建了一个%s任务【%s】";

    // 内容模板："%s变更了一个%s任务【%s】"
    String TASK_UPDATE_TEMPLATE = "%s变更了一个%s任务【%s】";

    // 内容模板："%s将%s任务【%s】由【%s】流转到【%s】"
    String TASK_TRANSFER_TEMPLATE = "%s将%s任务【%s】由【%s】流转到【%s】";

    //"任务创建通知"
    String TASK_CREATE_TITLE = "任务创建通知";

    //"任务变更通知"
    String TASK_UPDATE_TITLE = "任务变更通知";

   //"任务状态流转通知"
    String TASK_TRANSFER_TITLE = "任务状态流转通知";

}
