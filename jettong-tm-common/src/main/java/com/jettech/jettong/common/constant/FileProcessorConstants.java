package com.jettech.jettong.common.constant;

/**
 * @ClassName FileImportProcessorConstants
 * @Description 记录文件导入处理类型常量
 * <AUTHOR>
 * @Date 2025/9/13 12:30
 */
public class FileProcessorConstants
{
    /**
     * 测试分析导入
     */
    public static final String FUNCTION_POINT_IMPORT = "FUNCTION-POINT-IMPORT";

    /**
     * 用例库测试用例导入
     */
    public static final String LIB_TEST_CASE_IMPORT = "LIB-TEST-CASE-IMPORT";

    /**
     * 测试过程用例导入
     */
    public static final String TEST_PRODUCT_CASE_IMPORT = "TEST-PRODUCT-CASE-IMPORT";

}
