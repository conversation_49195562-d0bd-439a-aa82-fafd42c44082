package com.jettech.jettong.common.enumeration;

import com.jettech.basic.base.BaseEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;


/**
 * 会签记录表实体注释中生成的类型枚举
 * <AUTHOR>
 * @version 1.0
 * @description 会签记录表实体注释中生成的类型枚举
 * @projectName jettong
 * @package com.jettech.jettong.workflow.enumeration
 * @className CountersignResult
 * @date 2025-09-08
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CountersignResult", description = "最终结果 #CountersignResult{AGREE:通过;REJECT:驳回-枚举")
public enum CountersignResult implements BaseEnum {

    /**
     * AGREE="通过"
     */
    AGREE("通过"),
    /**
     * REJECT="驳回"
     */
    REJECT("不通过"),
    ;

    @ApiModelProperty(value = "描述")
    private String desc;


    /**
     * 根据当前枚举的name匹配
     */
    public static CountersignResult match(String val, CountersignResult def)
    {
        return Stream.of(values()).parallel().filter(item -> item.name().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static CountersignResult get(String val)
    {
        return match(val, null);
    }

    public boolean eq(CountersignResult val)
    {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "AGREE,REJECT", example = "AGREE")
    public String getCode()
    {
        return this.name();
    }

}
