<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.testm.dao.TestSchemeMapper">

    <select id="findDeliverablePage" resultType="com.jettech.jettong.testm.dto.Deliverable">


        select * from (-- PROJECT_FILE_UPLOAD
        SELECT
        f.id as fileId,
        f.original_file_name as fileName,
        f.create_time,
        pi.name AS bizName,
        f.biz_type,
        f.biz_id,
        pi.id as project_id
        FROM sys_file f
        JOIN project_info pi ON f.biz_id = pi.id
        WHERE f.biz_type = 'PROJECT_FILE_UPLOAD'

        UNION ALL

        -- TESTREQ_FILE_UPLOAD
        SELECT
        f.id as fileId,
        f.original_file_name as fileName,
        f.create_time,
        CONCAT(it.code, '-', it.name) AS bizName,
        f.biz_type,
        f.biz_id,
        it.project_id
        FROM sys_file f
        JOIN issue_testreq it ON f.biz_id = it.id
        WHERE f.biz_type = 'TESTREQ_FILE_UPLOAD'

        UNION ALL

        -- ISSUE_FILE_UPLOAD
        SELECT
        f.id as fileId,
        f.original_file_name as fileName,
        f.create_time,
        CONCAT(ir.code, '-', ir.name) AS bizName,
        f.biz_type,
        f.biz_id,
        ir.project_id
        FROM sys_file f
        JOIN issue_requirement ir ON f.biz_id = ir.id
        WHERE f.biz_type = 'ISSUE_FILE_UPLOAD'

        UNION ALL

        -- PLAN_FILE_UPLOAD
        SELECT
        f.id as fileId,
        f.original_file_name as fileName,
        f.create_time,
        pp.name AS bizName,
        f.biz_type,
        f.biz_id,
        pp.project_id
        FROM sys_file f
        JOIN project_plan pp ON f.biz_id = pp.id
        WHERE f.biz_type = 'PLAN_FILE_UPLOAD'

        UNION ALL

        -- TASK_FILE_UPLOAD
        SELECT
        f.id as fileId,
        f.original_file_name as fileName,
        f.create_time,
        CONCAT(pt.code, '-', pt.name) AS bizName,
        f.biz_type,
        f.biz_id,
        pt.project_id
        FROM sys_file f
        JOIN issue_task pt ON f.biz_id = pt.id
        WHERE f.biz_type = 'TASK_FILE_UPLOAD'

        UNION ALL

        -- BUG_FILE_UPLOAD
        SELECT
        f.id as fileId,
        f.original_file_name as fileName,
        f.create_time,
        CONCAT(ib.code, '-', ib.name) AS bizName,
        f.biz_type,
        f.biz_id,
        ib.project_id
        FROM sys_file f
        JOIN issue_bug ib ON f.biz_id = ib.id
        WHERE f.biz_type = 'BUG_FILE_UPLOAD'

        UNION ALL

        -- TEST_PRODUCT_CASE_FILE_UPLOAD
        SELECT
        f.id as fileId,
        f.original_file_name as fileName,
        f.create_time,
        CONCAT(tpc.case_key, '-', tpc.name) AS bizName,
        f.biz_type,
        f.biz_id,
        tpc.project_id
        FROM sys_file f
        JOIN test_product_case tpc ON f.biz_id = tpc.id
        WHERE f.biz_type = 'TEST_PRODUCT_CASE_FILE_UPLOAD'

        UNION ALL
        select null as fileId, name as fileName, create_time,
        name as bizName,
        'TEST_SCHEME' as biz_type,id as biz_id,
        project_id from test_scheme

        UNION ALL
        select null as fileId, name as fileName, create_time,
        name as bizName,
        'TEST_INFORM' as biz_type,id as biz_id,
        project_id from test_inform) AS deliverable
        where 1=1
        <if test="model.bizId != null">
            and deliverable.biz_id = #{model.bizId}
        </if>
        <if test="model.projectId != null">
            and deliverable.project_id = #{model.projectId}
        </if>
        <if test="model.bizType != null and model.bizType != ''">
            and deliverable.biz_type = #{model.bizType}
        </if>
        <if test="model.fileName != null and model.fileName != ''">
            and deliverable.fileName like concat('%',#{model.fileName},'%')
        </if>
        <if test="model.bizName != null and model.bizName != ''">
            and deliverable.bizName like concat('%',#{model.bizName},'%')
        </if>
        order by deliverable.create_time desc
    </select>

</mapper>
