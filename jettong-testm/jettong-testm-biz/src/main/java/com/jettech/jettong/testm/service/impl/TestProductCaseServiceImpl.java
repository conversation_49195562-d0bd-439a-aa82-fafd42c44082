package com.jettech.jettong.testm.service.impl;

import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.SpringUtils;
import com.jettech.jettong.alm.api.TaskApi;
import com.jettech.jettong.alm.issue.entity.Task;
import com.jettech.jettong.common.cache.alm.TypeCodePrefixCacheKeyBuilder;
import com.jettech.jettong.common.enumeration.ProductCaseRevisionOperationTypeEnum;
import com.jettech.jettong.common.enumeration.ProductCaseSourceTypeEnum;
import com.jettech.jettong.product.api.ProductModuleFunctionApi;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.dto.TestProductCaseDTO;
import com.jettech.jettong.testm.dto.TestProductCaseImportTaskSaveDTO;
import com.jettech.jettong.testm.entity.*;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.jettong.common.constant.FileProcessorConstants;
import com.jettech.basic.fileimport.model.ImportProgressEvent;
import com.jettech.basic.fileimport.model.ImportResult;
import com.jettech.basic.fileimport.model.ImportStatus;
import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.multipart.MultipartFile;
import com.jettech.basic.cache.repository.CacheOps;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.StringUtil;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.base.api.FileApi;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.testm.dao.TestProductCaseMapper;
import com.jettech.jettong.testm.dto.TestProductCaseUpdateDTO;
import com.jettech.jettong.testm.dto.TestCaseImportResultDTO;

import com.jettech.jettong.testm.service.*;
import com.jettech.jettong.testm.poi.dict.TestProductCaseDictHandler;
import com.jettech.jettong.testm.vo.TestCaseLibraryAndCaseNumber;
import com.jettech.jettong.common.util.poi.ExcelExportPlusUtil;
import org.apache.poi.ss.usermodel.*;

import java.util.HashMap;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.poi.ss.usermodel.Font.COLOR_RED;

import com.jettech.jettong.testm.vo.TestProductCaseNumberOfTreeVo;
import com.jettech.jettong.testm.vo.TestSummaryComponent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 业务层
 * @projectName jettong
 * @package com.jettech.jettong.testm.service.impl
 * @className TestProductCaseServiceImpl
 * @date 2022-03-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class TestProductCaseServiceImpl extends SuperServiceImpl<TestProductCaseMapper, TestProductCase>
        implements TestProductCaseService
{

    private final CacheOps cacheOps;

    public static final String PREFIX = "PC-";

    private final TestProductCaseHistoryService testProductCaseHistoryService;

    private final UserApi userApi;
    public final FileApi fileApi;
    public final ProductModuleFunctionApi moduleFunctionApi;

    private final TestProductCaseMapper testProductCaseMapper;

    private final TestOverviewService testOverviewService;

    private final TestProductCaseRegistryService testProductCaseRegistryService;

    private final TestProductCaseRevisionService testProductCaseRevisionService;
    private final TaskApi taskApi;
    private final TestProductCaseDictHandler testProductCaseDictHandler;
    private final TestRequirementFunctionPointsService testRequirementFunctionPointsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTestCase(TestProductCase testCase)
    {

        String sysCode = moduleFunctionApi.findProductInfoCodeByFunctionId(testCase.getModuleFunctionId());

        String caseKey = getProductCaseKey(sysCode);
        testCase.setCaseKey(caseKey);
        testCase.setId(UidGeneratorUtil.getId());
        testCase.setCreateTime(LocalDateTime.now());
        testCase.setCreatedBy(ContextUtil.getUserId());
        testCase.setUpdateTime(LocalDateTime.now());
        testCase.setUpdatedBy(ContextUtil.getUserId());
        testCase.setCreateTime(LocalDateTime.now());

        // 保存文件
        List<File> files = testCase.getFiles();
        if (CollUtil.isNotEmpty(files))
        {

            files.forEach(
                    item -> item.setBizType(FileBizType.TEST_PRODUCT_CASE_FILE_UPLOAD).setBizId(testCase.getId()));
            fileApi.updateBatchById(files);
        }

        //保存意图附件
        List<File> intentFiles = testCase.getIntentFiles();
        if (CollUtil.isNotEmpty(intentFiles))
        {

            intentFiles.forEach(item -> item.setBizType(FileBizType.TEST_PRODUCT_CASE_INTENT_FILE_UPLOAD)
                    .setBizId(testCase.getId()));
            fileApi.updateBatchById(intentFiles);
        }

        //保存前置条件附件
        List<File> prerequisiteFiles = testCase.getPrerequisiteFiles();
        if (CollUtil.isNotEmpty(prerequisiteFiles))
        {

            prerequisiteFiles.forEach(item -> item.setBizType(FileBizType.TEST_PRODUCT_CASE_PREREQUISITE_FILE_UPLOAD)
                    .setBizId(testCase.getId()));
            fileApi.updateBatchById(prerequisiteFiles);
        }

        //保存测试步骤附件
        List<File> testStepFiles = testCase.getTestStepFiles();
        if (CollUtil.isNotEmpty(testStepFiles))
        {

            testStepFiles.forEach(item -> item.setBizType(FileBizType.TEST_PRODUCT_CASE_TESTSTEP_FILE_UPLOAD)
                    .setBizId(testCase.getId()));
            fileApi.updateBatchById(testStepFiles);
        }

        super.save(testCase);

        // 维护registry和revision表
        createRevision(testCase, 1, ProductCaseRevisionOperationTypeEnum.ADD, "创建用例");
        updateRegistry(testCase);
    }

    @Override
    public TestProductCase copyCaseById(Long item, Long treeId)
    {
        TestProductCase byId = baseMapper.selectById(item);
        Long oldId = byId.getId();

        byId.setId(UidGeneratorUtil.getId());
        byId.setTreeId(treeId);
        byId.setCreatedBy(ContextUtil.getUserId());
        // 根据时间排序，获取最新一条数据
//        TestProductCase testCase =
//                super.getOne(Wraps.<TestProductCase>lbQ().orderByDesc(TestProductCase::getCreateTime).last(" limit
//                1"), false);
//        long num = 1L;
//        if (testCase != null)
//        {
//            num = StringUtil.getLongToStr(testCase.getCaseKey()) + 1;
//        }
        String sysCode = moduleFunctionApi.findProductInfoCodeByFunctionId(byId.getModuleFunctionId());
        String caseKey = getProductCaseKey(sysCode);
        byId.setCaseKey(caseKey);
//        byId.setCaseKey(getCaseKey(byId.getLibraryId()));
        byId.setLeadingBy(ContextUtil.getUserId());
        byId.setUpdatedBy(ContextUtil.getUserId());
        byId.setVersion("v1.0");
        super.save(byId);

        // 复制文件
        this.copyNewFile(oldId, FileBizType.TEST_PRODUCT_CASE_FILE_UPLOAD,
                byId.getId(), FileBizType.TEST_PRODUCT_CASE_FILE_UPLOAD);
        this.copyNewFile(oldId, FileBizType.TEST_PRODUCT_CASE_INTENT_FILE_UPLOAD,
                byId.getId(), FileBizType.TEST_PRODUCT_CASE_INTENT_FILE_UPLOAD);
        this.copyNewFile(oldId, FileBizType.TEST_PRODUCT_CASE_PREREQUISITE_FILE_UPLOAD,
                byId.getId(), FileBizType.TEST_PRODUCT_CASE_PREREQUISITE_FILE_UPLOAD);
        this.copyNewFile(oldId, FileBizType.TEST_PRODUCT_CASE_TESTSTEP_FILE_UPLOAD,
                byId.getId(), FileBizType.TEST_PRODUCT_CASE_TESTSTEP_FILE_UPLOAD);

        return byId;

    }


    /**
     * 复制附件文件，目前只copy了File实体类的记录，物理文件为相同文件
     *
     * @param oldBizId 旧文件绑定的ID
     * @param oldType 旧文件绑定的类型
     * @param newBizId 新文件绑定的ID
     * @param newType 新文件绑定的类型
     */
    private void copyNewFile(Long oldBizId, FileBizType oldType, Long newBizId, FileBizType newType)
    {
        List<File> oldFiles = fileApi.findByBizTypeAndBizId(oldType, oldBizId);
        List<File> newFiles = oldFiles.stream()
                .map(oldFile ->
                {
                    File newFile = BeanPlusUtil.toBean(oldFile, File.class);
                    newFile.setId(UidGeneratorUtil.getId());
                    newFile.setBizId(newBizId);
                    newFile.setBizType(newType);
                    return newFile;
                })
                .collect(Collectors.toList());
        fileApi.saveBatch(newFiles);
    }


    @Override
    public TestSummaryComponent dataFormat(Map<Integer, Long> map, Integer size)
    {
        TestSummaryComponent testCaseTypeComponentResult = new TestSummaryComponent();
        List<TestSummaryComponent.Pie> pies = new ArrayList<>();
        testCaseTypeComponentResult.setCount(size);

        for (Integer t : map.keySet())
        {
            if (new Integer(1).equals(t))
            {
                pies.add(new TestSummaryComponent.Pie().setName("Highest").setValue(map.get(t).intValue()));
            }
            if (new Integer(2).equals(t))
            {
                pies.add(new TestSummaryComponent.Pie().setName("High").setValue(map.get(t).intValue()));
            }
            if (new Integer(3).equals(t))
            {
                pies.add(new TestSummaryComponent.Pie().setName("Medium").setValue(map.get(t).intValue()));
            }
            if (new Integer(4).equals(t))
            {
                pies.add(new TestSummaryComponent.Pie().setName("Low").setValue(map.get(t).intValue()));
            }
            if (new Integer(5).equals(t))
            {
                pies.add(new TestSummaryComponent.Pie().setName("Lowest").setValue(map.get(t).intValue()));
            }
        }
        //testCaseTypeComponentResult.setData(defaultPriorityData(pies));
        testCaseTypeComponentResult.setData(pies);
        return testCaseTypeComponentResult;
    }

    @Override
    public void saveTestProductCaseList(List<TestProductCase> caseList)
    {
        /*//循环处理case_key
        caseList.forEach(item ->
        {
            TestProductCase testCase =
                    super.getOne(Wraps.<TestProductCase>lbQ().orderByDesc(TestProductCase::getCreateTime).last("
                    limit 1"), false);
            long num = 1L;
            if (testCase != null)
            {
                num = StringUtil.getLongToStr(testCase.getCaseKey()) + 1;
            }
            item.setCaseKey(PREFIX+ (num + 1));
        });*/
        testProductCaseMapper.saveTestProductCaseList(caseList);
    }

    @Override
    public List<File> selectFilesByCaseIds(List<Long> caseIdList)
    {
        return testProductCaseMapper.selectFilesByCaseIds(caseIdList);
    }

    @Override
    public void saveFileList(List<File> fileList)
    {
        testProductCaseMapper.saveFileList(fileList);
    }

    public List<TestSummaryComponent.Pie> defaultPriorityData(List<TestSummaryComponent.Pie> pies)
    {
        List<String> resultList = new ArrayList<>();
        List<String> list = new ArrayList<>();
        list.add("Highest");
        list.add("High");
        list.add("Medium");
        list.add("Low");
        list.add("Lowest");
        for (TestSummaryComponent.Pie t : pies)
        {
            noExistPriority(list, t.getName());
        }
        for (String t : list)
        {
            pies.add(new TestSummaryComponent.Pie().setName(t).setValue(0));
        }
        return pies;
    }

    public void noExistPriority(List<String> list, String s)
    {
        Iterator<String> iterator = list.iterator();
        String remove = "";
        for (String t : list)
        {
            if (t.equals(s))
            {
                remove = s;
                break;
            }
        }
        list.remove(remove);
    }


    @Override
    public void setOverView(TestProductCase testProductCase)
    {
        TestProductCase testProductCase1 = baseMapper.selectById(testProductCase.getId());
        TestOverview testOverview = testOverviewService.getOne(Wraps.<TestOverview>lbQ());
        if (!testProductCase1.getPriority().equals(testProductCase.getPriority()))
        {
            switch (testProductCase1.getPriority())
            {
                case 1:
                    testOverview.setCaseMinimum(testOverview.getCaseMinimum() - 1);
                    break;
                case 2:
                    testOverview.setCaseLower(testOverview.getCaseLower() - 1);
                    break;
                case 3:
                    testOverview.setCaseOrdinary(testOverview.getCaseOrdinary() - 1);
                    break;
                case 4:
                    testOverview.setCaseHigher(testOverview.getCaseHigher() - 1);
                    break;
                case 5:
                    testOverview.setCaseHighest(testOverview.getCaseHighest() - 1);
                    break;
            }
            switch (testProductCase.getPriority())
            {
                case 1:
                    testOverview.setCaseMinimum(
                            testOverview.getCaseMinimum() == null ? 0 : testOverview.getCaseMinimum() + 1);
                    break;
                case 2:
                    testOverview.setCaseLower(
                            testOverview.getCaseLower() == null ? 0 : testOverview.getCaseLower() + 1);
                    break;
                case 3:
                    testOverview.setCaseOrdinary(
                            testOverview.getCaseOrdinary() == null ? 0 : testOverview.getCaseOrdinary() + 1);
                    break;
                case 4:
                    testOverview.setCaseHigher(
                            testOverview.getCaseHigher() == null ? 0 : testOverview.getCaseHigher() + 1);
                    break;
                case 5:
                    testOverview.setCaseHighest(
                            testOverview.getCaseHighest() == null ? 0 : testOverview.getCaseHighest() + 1);
                    break;
            }
            testOverviewService.updateById(testOverview);
        }
    }

    @Override
    public void saveOverView(TestProductCase testProductCase)
    {
        TestOverview testOverview = testOverviewService.getOne(Wraps.<TestOverview>lbQ());
        switch (testProductCase.getPriority())
        {
            case 1:
                testOverview.setCaseMinimum(
                        testOverview.getCaseMinimum() == null ? 0 : testOverview.getCaseMinimum() + 1);
                break;
            case 2:
                testOverview.setCaseLower(testOverview.getCaseLower() == null ? 0 : testOverview.getCaseLower() + 1);
                break;
            case 3:
                testOverview.setCaseOrdinary(
                        testOverview.getCaseOrdinary() == null ? 0 : testOverview.getCaseOrdinary() + 1);
                break;
            case 4:
                testOverview.setCaseHigher(testOverview.getCaseHigher() == null ? 0 : testOverview.getCaseHigher() + 1);
                break;
            case 5:
                testOverview.setCaseHighest(
                        testOverview.getCaseHighest() == null ? 0 : testOverview.getCaseHighest() + 1);
                break;
        }
        testOverviewService.updateById(testOverview);
    }

    @Override
    public void deleteAllOverView(List<TestProductCase> testProductCaseList)
    {
        TestOverview testOverview = testOverviewService.getOne(Wraps.<TestOverview>lbQ());
        Map<Integer, List<TestProductCase>> collect =
                testProductCaseList.stream().collect(Collectors.groupingBy(TestProductCase::getPriority));
        if (collect.get(1) != null)
        {
            testOverview.setCaseMinimum(
                    testOverview.getCaseMinimum() == null ? 0 : testOverview.getCaseMinimum() - collect.get(1).size());
        }
        if (collect.get(2) != null)
        {
            testOverview.setCaseLower(
                    testOverview.getCaseLower() == null ? 0 : testOverview.getCaseLower() - collect.get(2).size());
        }
        if (collect.get(3) != null)
        {
            testOverview.setCaseOrdinary(testOverview.getCaseOrdinary() == null ? 0 :
                    testOverview.getCaseOrdinary() - collect.get(3).size());
        }
        if (collect.get(4) != null)
        {
            testOverview.setCaseHigher(
                    testOverview.getCaseHigher() == null ? 0 : testOverview.getCaseHigher() - collect.get(4).size());
        }
        if (collect.get(5) != null)
        {
            testOverview.setCaseHighest(
                    testOverview.getCaseHighest() == null ? 0 : testOverview.getCaseHighest() - collect.get(5).size());
        }

        testOverviewService.updateById(testOverview);
    }

    @Override
    public void deleteOverView(TestProductCase testProductCase)
    {
        TestOverview testOverview = testOverviewService.getOne(Wraps.<TestOverview>lbQ());
        switch (testProductCase.getPriority())
        {
            case 1:
                testOverview.setCaseMinimum(testOverview.getCaseMinimum() - 1);
                break;
            case 2:
                testOverview.setCaseLower(testOverview.getCaseLower() - 1);
                break;
            case 3:
                testOverview.setCaseOrdinary(testOverview.getCaseOrdinary() - 1);
                break;
            case 4:
                testOverview.setCaseHigher(testOverview.getCaseHigher() - 1);
                break;
            case 5:
                testOverview.setCaseHighest(testOverview.getCaseHighest() - 1);
                break;
        }
        testOverviewService.updateById(testOverview);
    }

    @Override
    public void saveAllOverView(List<TestProductCase> taskList)
    {
        TestOverview testOverview = testOverviewService.getOne(Wraps.<TestOverview>lbQ());
        Map<Integer, List<TestProductCase>> collect =
                taskList.stream().collect(Collectors.groupingBy(TestProductCase::getPriority));
        if (collect.get(1) != null)
        {
            testOverview.setCaseMinimum(
                    testOverview.getCaseMinimum() == null ? 0 : testOverview.getCaseMinimum() + collect.get(1).size());
        }
        if (collect.get(2) != null)
        {
            testOverview.setCaseLower(
                    testOverview.getCaseLower() == null ? 0 : testOverview.getCaseLower() + collect.get(2).size());
        }
        if (collect.get(3) != null)
        {
            testOverview.setCaseOrdinary(testOverview.getCaseOrdinary() == null ? 0 :
                    testOverview.getCaseOrdinary() + collect.get(3).size());
        }
        if (collect.get(4) != null)
        {
            testOverview.setCaseHigher(
                    testOverview.getCaseHigher() == null ? 0 : testOverview.getCaseHigher() + collect.get(4).size());
        }
        if (collect.get(5) != null)
        {
            testOverview.setCaseHighest(
                    testOverview.getCaseHighest() == null ? 0 : testOverview.getCaseHighest() + collect.get(5).size());
        }

        testOverviewService.updateById(testOverview);
    }


    @Override
    public void saveAllTestProductCase(List<TestProductCase> testProductCaseList)
    {
        TestOverview testOverview = testOverviewService.getOne(Wraps.<TestOverview>lbQ());
        Map<Integer, List<TestProductCase>> collect =
                testProductCaseList.stream().collect(Collectors.groupingBy(TestProductCase::getPriority));
        if (collect.get(1) != null)
        {
            testOverview.setCaseMinimum(
                    testOverview.getCaseMinimum() == null ? 0 : testOverview.getCaseMinimum() + collect.get(1).size());
        }
        if (collect.get(2) != null)
        {
            testOverview.setCaseLower(
                    testOverview.getCaseLower() == null ? 0 : testOverview.getCaseLower() + collect.get(2).size());
        }
        if (collect.get(3) != null)
        {
            testOverview.setCaseOrdinary(testOverview.getCaseOrdinary() == null ? 0 :
                    testOverview.getCaseOrdinary() + collect.get(3).size());
        }
        if (collect.get(4) != null)
        {
            testOverview.setCaseHigher(
                    testOverview.getCaseHigher() == null ? 0 : testOverview.getCaseHigher() + collect.get(4).size());
        }
        if (collect.get(5) != null)
        {
            testOverview.setCaseHighest(
                    testOverview.getCaseHighest() == null ? 0 : testOverview.getCaseHighest() + collect.get(5).size());
        }

        testOverviewService.updateById(testOverview);
    }

    @Override
    public void removeAllTestProductCase(List<TestProductCase> testProductCaseList)
    {
        TestOverview testOverview = testOverviewService.getOne(Wraps.<TestOverview>lbQ());
        Map<Integer, List<TestProductCase>> collect =
                testProductCaseList.stream().collect(Collectors.groupingBy(TestProductCase::getPriority));
        if (collect.get(1) != null)
        {
            testOverview.setCaseMinimum(
                    testOverview.getCaseMinimum() == null ? 0 : testOverview.getCaseMinimum() - collect.get(1).size());
        }
        if (collect.get(2) != null)
        {
            testOverview.setCaseLower(
                    testOverview.getCaseLower() == null ? 0 : testOverview.getCaseLower() - collect.get(2).size());
        }
        if (collect.get(3) != null)
        {
            testOverview.setCaseOrdinary(testOverview.getCaseOrdinary() == null ? 0 :
                    testOverview.getCaseOrdinary() - collect.get(3).size());
        }
        if (collect.get(4) != null)
        {
            testOverview.setCaseHigher(
                    testOverview.getCaseHigher() == null ? 0 : testOverview.getCaseHigher() - collect.get(4).size());
        }
        if (collect.get(5) != null)
        {
            testOverview.setCaseHighest(
                    testOverview.getCaseHighest() == null ? 0 : testOverview.getCaseHighest() - collect.get(5).size());
        }

        testOverviewService.updateById(testOverview);
    }

    @Override
    public List<TestProductCaseNumberOfTreeVo> queryCaseNumberOfTree(Long libraryId)
    {

        List<TestProductCaseNumberOfTreeVo> testProductCases = baseMapper.queryCaseNumberOfTree(libraryId);
        return testProductCases;
    }

    @Override
    public List<TestProductCaseNumberOfTreeVo> getNumberOfTree(Long libraryId, List<Long> treeIds)
    {

        List<TestProductCaseNumberOfTreeVo> testProductCases = baseMapper.getNumberOfTree(libraryId, treeIds);
        return testProductCases;
    }

    @Override
    public List<TestProductCaseNumberOfTreeVo> getQueryCaseNumberOfTree(TestProductCase testProductCase, List<Long> ids)
    {

        List<TestProductCaseNumberOfTreeVo> testProductCases =
                baseMapper.getQueryCaseNumberOfTree(testProductCase, ids);
        return testProductCases;
    }

    @Override
    public List<TestProductCaseNumberOfTreeVo> queryCaseNumberOfDraftTree(Long libraryId)
    {

        List<TestProductCaseNumberOfTreeVo> testProductCases = baseMapper.queryCaseNumberOfDraftTree(libraryId);
        return testProductCases;
    }

    @Override
    public void updateBatchByLibraryId(TestProductCaseUpdateDTO model, Long libraryId)
    {

        Map<String, Object> map = BeanPlusUtil.beanToMap(model);
        baseMapper.updateBatchByLibraryId(map, libraryId);
    }

    @Override
    public List<TestCaseLibraryAndCaseNumber> getCaseNumberOflibrary(List<String> libraryIds)
    {

        List<Long> longLibraryIds = libraryIds.stream().map(x -> Long.valueOf(x)).collect(Collectors.toList());
        List<TestCaseLibraryAndCaseNumber> result = baseMapper.getCaseNumberOflibrary(longLibraryIds);
        return result;
    }

    @Override
    public List<TestProductCase> queryProductCaseByIds(List<Long> testCaseIds)
    {
        return baseMapper.selectBatchIds(testCaseIds);
    }


    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> set)
    {
        return listByIds(set).stream().collect(Collectors.toMap(TestProductCase::getId, v -> v));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatch(TestProductCaseDTO testProductCaseS)
    {
        if (testProductCaseS == null || CollUtil.isEmpty(testProductCaseS.getIds()))
        {
            return;
        }
        LambdaUpdateWrapper<TestProductCase> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(TestProductCase::getId, testProductCaseS.getIds())
//                .set(testProductCaseS.getName()!=null,TestProductCase::getName, testProductCaseS.getName())
                .set(testProductCaseS.getPriority() != null, TestProductCase::getPriority,
                        testProductCaseS.getPriority())
                .set(testProductCaseS.getReviewStatus() != null, TestProductCase::getReviewStatus,
                        testProductCaseS.getReviewStatus())
                .set(testProductCaseS.getCaseType() != null, TestProductCase::getCaseType,
                        testProductCaseS.getCaseType())
                .set(testProductCaseS.getIsExamples() != null, TestProductCase::getIsExamples,
                        testProductCaseS.getIsExamples())
                .set(TestProductCase::getUpdateTime, LocalDateTime.now())
                .set(TestProductCase::getUpdatedBy, ContextUtil.getUserId());

        // 执行批量更新操作
        baseMapper.update(null, lambdaUpdateWrapper);
    }

    @Transactional
    @Override
    public List<TestProductCase> referenceFromTestCase(List<Long> testCaseIds, Long taskId)
    {
        Task task = taskApi.getById(taskId);
        if (task == null)
        {
            throw new BizException("任务不存在");
        }

        List<Long> taskModuleFunctionIds = getTaskModuleFunctionIds(task.getProjectId(),task.getTestreqId(),task.getId());
        if (taskModuleFunctionIds.isEmpty())
        {
            throw new BizException("当前任务未关联任何系统交易");
        }

        TestCaseService testCaseService = SpringUtils.getBean(TestCaseService.class);
        List<TestCase> sourceCases = testCaseService.listByIds(testCaseIds);
        if (sourceCases.size() != testCaseIds.size())
        {
            throw new BizException("部分用例库用例不存在");
        }

        for (TestCase sourceCase : sourceCases)
        {
            if (sourceCase.getModuleFunctionId() != null &&
                !taskModuleFunctionIds.contains(sourceCase.getModuleFunctionId()))
            {
                throw new BizException("用例 " + sourceCase.getCaseKey() + " 的关联交易不在当前任务范围内");
            }
        }

        List<TestProductCase> resultCases = new ArrayList<>();
        for (TestCase sourceCase : sourceCases)
        {
            // 检查同一个任务下是否已经引入过相同的用例库用例
            TestProductCase existingCase = super.getOne(Wraps.<TestProductCase>lbQ()
                    .eq(TestProductCase::getTaskId, taskId)
                    .eq(TestProductCase::getCaseKey, sourceCase.getCaseKey()));

            if (existingCase != null)
            {
                // 已存在，执行更新操作
                resultCases.add(updateExistingReferenceCase(existingCase, sourceCase, task));
            }
            else
            {
                // 不存在，执行新增操作
                resultCases.add(createNewReferenceCase(sourceCase, task, sourceCase.getId(), taskId));
            }
        }
        return resultCases;
    }

    /**
     * 创建新的引用用例
     */
    private TestProductCase createNewReferenceCase(TestCase sourceCase, Task task, Long testCaseId, Long taskId)
    {
        // 复制用例数据，保持用例编号不变
        CopyOptions copyOptions = new CopyOptions();
        copyOptions.setIgnoreProperties("sourceType");
        TestProductCase newProductCase = BeanPlusUtil.toBean(sourceCase, TestProductCase.class, copyOptions);
        newProductCase.setId(UidGeneratorUtil.getId());
        newProductCase.setCaseKey(sourceCase.getCaseKey()); // 保持用例编号不变
        newProductCase.setSourceType(ProductCaseSourceTypeEnum.FROM_LIBRARY);
        newProductCase.setSourceLibCaseId(sourceCase.getId());

        // 设置引用数据（保持原有的moduleFunctionId）
        newProductCase.setTaskId(taskId);
        newProductCase.setModuleFunctionId(sourceCase.getModuleFunctionId()); // 保持用例库用例的原有交易关联
        newProductCase.setProjectId(task.getProjectId());
        newProductCase.setTestreqId(task.getTestreqId());
        newProductCase.setProductId(task.getProductId());

        // 设置创建信息
        newProductCase.setCreateTime(LocalDateTime.now());
        newProductCase.setCreatedBy(ContextUtil.getUserId());
        newProductCase.setUpdateTime(LocalDateTime.now());
        newProductCase.setUpdatedBy(ContextUtil.getUserId());

        // 保存用例
        super.save(newProductCase);

        // 更新用例库用例的引用计数
        TestCaseService testCaseService = SpringUtils.getBean(TestCaseService.class);
        testCaseService.incrementReferenceCount(testCaseId);

        // 创建revision记录
        createRevision(newProductCase, 1, ProductCaseRevisionOperationTypeEnum.REFERENCE, "引用用例库用例");
        updateRegistry(newProductCase);

        return newProductCase;
    }

    /**
     * 更新已存在的引用用例
     */
    private TestProductCase updateExistingReferenceCase(TestProductCase existingCase, TestCase sourceCase, Task task)
    {
        // 保存原有的关键信息
        Integer nextVersion = getNextVersionNo(existingCase);
        Long originalId = existingCase.getId();
        String originalCaseKey = existingCase.getCaseKey();
        LocalDateTime originalCreateTime = existingCase.getCreateTime();
        Long originalCreatedBy = existingCase.getCreatedBy();
        ProductCaseSourceTypeEnum originalSourceType = existingCase.getSourceType();
        Long originalSourceLibCaseId = existingCase.getSourceLibCaseId();
        Long originalTaskId = existingCase.getTaskId();
        Long originalModuleFunctionId = existingCase.getModuleFunctionId();
        Long originalProjectId = existingCase.getProjectId();
        Long originalTestreqId = existingCase.getTestreqId();
        Long originalProductId = existingCase.getProductId();
        CopyOptions copyOptions = new CopyOptions();
        copyOptions.setIgnoreProperties("sourceType");
        // 从用例库用例复制最新数据
        BeanPlusUtil.copyProperties(sourceCase, existingCase, copyOptions);
        existingCase.setVersion(String.valueOf(nextVersion));
        // 恢复关键信息，确保不被覆盖
        existingCase.setId(originalId);
        existingCase.setCaseKey(originalCaseKey);
        existingCase.setCreateTime(originalCreateTime);
        existingCase.setCreatedBy(originalCreatedBy);
        existingCase.setSourceType(originalSourceType);
        existingCase.setSourceLibCaseId(originalSourceLibCaseId);
        existingCase.setTaskId(originalTaskId);
        existingCase.setModuleFunctionId(originalModuleFunctionId);
        existingCase.setProjectId(originalProjectId);
        existingCase.setTestreqId(originalTestreqId);
        existingCase.setProductId(originalProductId);

        // 设置更新信息
        existingCase.setUpdateTime(LocalDateTime.now());
        existingCase.setUpdatedBy(ContextUtil.getUserId());

        // 更新用例
        super.updateById(existingCase);

        // 创建revision记录
        createRevision(existingCase, nextVersion, ProductCaseRevisionOperationTypeEnum.REFERENCE,
                "更新引用的用例库用例");
        updateRegistry(existingCase);

        return existingCase;
    }

    /**
     * 获取下一个版本号 - 从revision表查询当前case_key的最大版本号后递增
     */
    @Override
    public int getNextVersionNo(TestProductCase testCase) {
        int maxVersionNo = testProductCaseRevisionService.getMaxVersionByCaseKey(testCase.getCaseKey());
        return maxVersionNo + 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restoreFromRevision(Long revisionId, Long taskId)
    {
        TestProductCaseRevision revision = testProductCaseRevisionService.getById(revisionId);
        if (revision == null)
        {
            throw new BizException("修订记录不存在");
        }

        if (taskId != null)
        {
            // 传了任务ID，还原到test_product_case表（过程用例）
            restoreToProductCase(revision, taskId);
        }
        else
        {
            // 没传任务ID，还原到test_product_case_registry表（注册表）
            restoreToRegistry(revision);
        }
    }

    /**
     * 还原到test_product_case表（过程用例）
     */
    private void restoreToProductCase(TestProductCaseRevision revision, Long taskId)
    {
        TestProductCase testCase = getById(revision.getCaseId());
        if (testCase == null)
        {
            throw new BizException("用例不存在");
        }

        // 从revision复制数据到用例
        BeanPlusUtil.copyProperties(revision, testCase);
        testCase.setId(revision.getCaseId());
        testCase.setTaskId(taskId); // 设置任务ID

        // 获取下一个版本号
        int newVersionNo = getNextVersionNo(testCase);
        testCase.setVersion(String.valueOf(newVersionNo));

        // 更新用例
        super.updateById(testCase);

        // 创建新的revision记录
        createRevision(testCase, newVersionNo, ProductCaseRevisionOperationTypeEnum.RESTORE,
                "从版本 " + revision.getVersionNo() + " 还原");

        // 更新注册表
        updateRegistry(testCase);
    }

    /**
     * 还原到test_product_case_registry表
     */
    private void restoreToRegistry(TestProductCaseRevision revision)
    {
        // 创建注册表记录
        TestProductCaseRegistry registry = BeanPlusUtil.toBean(revision, TestProductCaseRegistry.class);
        registry.setId(null); // 清空ID，让数据库自动生成
        registry.setLastCaseId(revision.getCaseId());
        registry.setSourceLibCaseId(revision.getCaseId()); // 设置来源用例ID
        registry.setSavedToLib(false);

        // 设置创建信息
        registry.setCreateTime(LocalDateTime.now());
        registry.setCreatedBy(ContextUtil.getUserId());
        registry.setUpdateTime(LocalDateTime.now());
        registry.setUpdatedBy(ContextUtil.getUserId());

        // 保存或更新注册表
        testProductCaseRegistryService.saveOrUpdateByCaseKey(registry);
    }

    @Override
    public boolean updateById(TestProductCase testCase)
    {
        TestProductCase oldCase = getById(testCase.getId());
        if (oldCase == null)
        {
            throw new BizException("用例不存在");
        }

        // 从revision表获取当前case_key的最大版本号并递增
        int newVersionNo = getNextVersionNo(oldCase);
        testCase.setVersion(String.valueOf(newVersionNo));

        super.updateById(testCase);

        // 维护revision表
        createRevision(testCase, newVersionNo, ProductCaseRevisionOperationTypeEnum.UPDATE, "修改用例");
        updateRegistry(testCase);
        return false;
    }

    @Override
    public boolean removeByIds(Collection<? extends Serializable> idList)
    {
        if (CollUtil.isEmpty(idList))
        {
            return false;
        }

        // 获取要删除的用例信息
        List<TestProductCase> casesToDelete = listByIds(idList);
        TestCaseService testCaseService = SpringUtils.getBean(TestCaseService.class);

        // 减少引用计数
        for (TestProductCase testCase : casesToDelete)
        {
            if (testCase.getSourceLibCaseId() != null)
            {
                testCaseService.decrementReferenceCount(testCase.getSourceLibCaseId());
            }
        }

        super.removeByIds(idList);
        return false;
    }


    @Override
    public void createRevision(TestProductCase testCase, int versionNo, ProductCaseRevisionOperationTypeEnum operationType, String desc) {
        TestProductCaseRevision revision = BeanPlusUtil.toBean(testCase, TestProductCaseRevision.class);

        revision.setId(null);
        revision.setProjectId(testCase.getProjectId());
        revision.setTaskId(testCase.getTaskId());
        revision.setRequirementId(testCase.getTestreqId());
        revision.setCaseId(testCase.getId());
        revision.setVersionNo(versionNo);
        revision.setOperationType(operationType);
        revision.setOperationDesc(desc);
        testProductCaseRevisionService.save(revision);
    }

    private void updateRegistry(TestProductCase testCase)
    {
        TestProductCaseRegistry registry = BeanPlusUtil.toBean(testCase, TestProductCaseRegistry.class);
        registry.setLastCaseId(testCase.getId());
        registry.setSourceLibCaseId(testCase.getSourceLibCaseId());
        registry.setSavedToLib(false);
        testProductCaseRegistryService.saveOrUpdateByCaseKey(registry);
    }

    @Override
    public String getProductCaseKey(String sysCode)
    {
        CacheKey cacheKey = new TypeCodePrefixCacheKeyBuilder().key(sysCode);
        Long codeNum = cacheOps.incr(cacheKey);
        if (codeNum == null || codeNum == 1)
        {
            String productCaseMaxKey;
            String testCaseMaxKey;
            try
            {
                productCaseMaxKey = this.getMaxCaseKey(sysCode);
                TestCaseService testCaseService = SpringUtils.getBean(TestCaseService.class);
                testCaseMaxKey = testCaseService.getMaxCaseKey(sysCode);
            }
            finally
            {
                cacheOps.del(cacheKey);
            }
            Long productCaseNum = 0L;
            if (productCaseMaxKey != null)
            {
                productCaseNum = StringUtil.getLongToStr(productCaseMaxKey);
            }
            Long testCaseNum = 0L;
            if (testCaseMaxKey != null)
            {
                testCaseNum = StringUtil.getLongToStr(testCaseMaxKey);
            }
            Long num = Math.max(productCaseNum, testCaseNum);
            Long incr = cacheOps.incrBy(cacheKey, num + 1);
            return sysCode + String.format("%07d", incr);
        }
        return sysCode + String.format("%07d", codeNum);
    }

    @Override
    public String getMaxCaseKey(String sysCode)
    {
        return baseMapper.getProductCaseKey(sysCode);
    }

    @Override
    public Workbook downloadTestProductCaseImportTemplate(Long taskId)
    {
        return buildExcelExportWorkbook(null, taskId);
    }

    @Override
    public Workbook exportTestProductCases(Long taskId)
    {
        // 根据任务ID查询测试过程用例
        List<TestProductCase> list = lambdaQuery()
                .eq(taskId != null, TestProductCase::getTaskId, taskId)
                .list();

        return buildExcelExportWorkbook(list, taskId);
    }

    @Override
    public Workbook buildExcelExportWorkbook(List<TestProductCase> data)
    {
        return buildExcelExportWorkbook(data, null);
    }

    /**
     * 构建Excel导出工作簿（用于导出和模板下载）
     *
     * @param data 数据列表，null表示生成模板
     * @param taskId 任务ID
     * @return Workbook
     */
    private Workbook buildExcelExportWorkbook(List<TestProductCase> data, Long taskId)
    {
        try
        {
            if (data == null)
            {
                data = new ArrayList<>();
            }

            ExportParams exportParams = new ExportParams("测试过程用例", "测试过程用例");
            exportParams.setDictHandler(testProductCaseDictHandler);
            exportParams.setTitle("1、导入模板不同任务不能通用\n" +
                    "2、带*的列必须填写\n" +
                    "3、测试步骤类型为文本类型时，直接填入内容；当测试步骤类型为条目时，测试步骤输入如下格式：1,xxxx,xxxx;2,xxxx,xxxx;");

            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, TestProductCase.class, data);

            // 添加下拉字段支持
            addDropDownSupport(workbook, taskId);

            // 设置标题行样式
            Row row = workbook.getSheetAt(0).getRow(0);
            if (row != null)
            {
                row.setHeightInPoints(47);
                Cell cell = row.getCell(0);
                if (cell != null)
                {
                    CellStyle cellStyle = cell.getCellStyle();
                    cellStyle.setWrapText(true);
                    Font font = workbook.createFont();
                    font.setBold(true);
                    font.setColor(COLOR_RED);
                    cellStyle.setFont(font);
                    cellStyle.setAlignment(HorizontalAlignment.LEFT);
                }
            }

            return workbook;
        }
        finally
        {
            // 确保字典处理器缓存被清理
            testProductCaseDictHandler.clear();
        }
    }

    /**
     * 添加下拉字段支持
     *
     * @param workbook Excel工作簿
     * @param taskId 任务ID
     */
    private void addDropDownSupport(Workbook workbook, Long taskId)
    {
        try
        {

            Map<String, String[]> dropMap = testProductCaseDictHandler.dropMap(null);


            if (dropMap != null)
            {
                for (Map.Entry<String, String[]> entry : dropMap.entrySet())
                {
                    String fieldName = entry.getKey();
                    String[] values = entry.getValue();
                    if (values != null && values.length > 0)
                    {
                    }
                }
            }

            if (dropMap != null && !dropMap.isEmpty())
            {
                // 创建文本格式样式
                CellStyle textStyle = workbook.createCellStyle();
                DataFormat dataFormat = workbook.createDataFormat();
                textStyle.setDataFormat(dataFormat.getFormat("@"));

                Sheet sheet = workbook.getSheetAt(0);

                // 获取表头行，确定字段列的位置
                Row headerRow = sheet.getRow(1); // 第二行是表头（第一行是标题）
                if (headerRow != null)
                {
                    Map<String, Integer> fieldColumnMap = getFieldColumnMapping(headerRow);

                    // 为每个有下拉数据的字段添加下拉选项
                    for (Map.Entry<String, String[]> entry : dropMap.entrySet())
                    {
                        String fieldName = entry.getKey();
                        String[] dropDownValues = entry.getValue();

                        // 特殊处理functionPointsId字段，根据任务查询
                        if ("functionPointsId".equals(fieldName) && taskId != null)
                        {
                            dropDownValues = getFunctionPointsDropData(taskId);
                        }

                        Integer columnIndex = fieldColumnMap.get(fieldName);
                        if (columnIndex != null && dropDownValues != null && dropDownValues.length > 0)
                        {
                            // 添加下拉列表，从第3行开始（索引2），到第65535行
                            ExcelExportPlusUtil.addDropDownList(workbook, sheet, dropDownValues, 2, 65535, columnIndex);
                            // 设置列的默认格式为文本
                            sheet.setDefaultColumnStyle(columnIndex, textStyle);
                        }
                    }
                }
            }
        }
        catch (Exception e)
        {
            log.warn("添加下拉字段支持时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取字段名到列索引的映射
     *
     * @param headerRow 表头行
     * @return 字段名到列索引的映射
     */
    private Map<String, Integer> getFieldColumnMapping(Row headerRow)
    {
        Map<String, Integer> fieldColumnMap = new HashMap<>();

        // TestProductCase中需要下拉支持的字段映射（移除了使用replace处理的字段）
        Map<String, String> fieldNameMapping = new HashMap<>();
        fieldNameMapping.put("所属系统", "productId");
        fieldNameMapping.put("优先级", "priority");
        fieldNameMapping.put("负责人", "leadingBy");
        fieldNameMapping.put("案例类型", "caseType");
        fieldNameMapping.put("所属交易", "moduleFunctionId");
        fieldNameMapping.put("案例级别", "caseLevel");
        fieldNameMapping.put("测试方式", "testMode");
        fieldNameMapping.put("关联测试点", "functionPointsId");
        // 注意：步骤类型(stepType)和正反例(isExamples)使用Excel注解的replace处理，不需要字典下拉

        // 遍历表头行，建立列名到列索引的映射
        for (int i = 0; i < headerRow.getLastCellNum(); i++)
        {
            Cell cell = headerRow.getCell(i);
            if (cell != null)
            {
                String cellValue = cell.getStringCellValue();
                if (cellValue != null)
                {
                    cellValue = cellValue.trim();
                    // 移除可能的必填标记
                    if (cellValue.endsWith("*"))
                    {
                        cellValue = cellValue.substring(0, cellValue.length() - 1).trim();
                    }

                    String fieldName = fieldNameMapping.get(cellValue);
                    if (fieldName != null)
                    {
                        fieldColumnMap.put(fieldName, i);
                    }
                }
            }
        }

        return fieldColumnMap;
    }

    /**
     * 根据任务ID获取功能点下拉数据
     *
     * @param taskId 任务ID
     * @return 功能点下拉数据数组
     */
    private String[] getFunctionPointsDropData(Long taskId)
    {
        try
        {
            // 通过任务API获取任务信息
            Task task = taskApi.getById(taskId);
            if (task == null)
            {
                log.warn("未找到任务信息，taskId: {}", taskId);
                return new String[0];
            }

            // 查询该任务关联的测试功能点
            List<TestRequirementFunctionPoints> functionPoints = testRequirementFunctionPointsService.lambdaQuery()
                    .eq(task.getProjectId() != null, TestRequirementFunctionPoints::getProjectId, task.getProjectId())
                    .eq(task.getTestreqId() != null, TestRequirementFunctionPoints::getIssueTestReqId,
                            task.getTestreqId())
                    .list();

            if (CollUtil.isEmpty(functionPoints))
            {
                log.warn("任务 {} 没有关联的测试功能点", taskId);
                return new String[0];
            }

            // 针对名称相同的测试点，只保留第一个（在需求下唯一）
            Set<String> seenNames = new HashSet<>();
            List<String> uniqueFunctionPoints = new ArrayList<>();

            for (TestRequirementFunctionPoints point : functionPoints)
            {
                String pointName = point.getFunctionPoint();

                // 如果名称已经存在，跳过后续相同名称的测试点
                if (seenNames.contains(pointName))
                {
                    continue;
                }

                seenNames.add(pointName);
                uniqueFunctionPoints.add(pointName);
            }

            return uniqueFunctionPoints.toArray(new String[0]);

        }
        catch (Exception e)
        {
            log.error("获取功能点下拉数据失败，taskId: {}", taskId, e);
            return new String[0];
        }
    }

    @Override
    public ImportResult processFile(MultipartFile multipartFile, String taskId,
            java.util.function.Consumer<ImportProgressEvent> consumer,
            TestProductCaseImportTaskSaveDTO importTaskSaveDTO)
    {
        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        params.setHeadRows(1);
//        params.setNeedVerify(true);
        params.setDictHandler(testProductCaseDictHandler);

        try
        {
            // 在导入前初始化字典处理器，确保dictVar被正确设置
            Map<String, String[]> dropMap = testProductCaseDictHandler.dropMap(null);

            consumer.accept(ImportProgressEvent.builder()
                    .taskId(taskId)
                    .status(ImportStatus.PROCESSING)
                    .progress(10)
                    .message("开始解析Excel文件...")
                    .build());

            ExcelImportResult<TestProductCase> result = ExcelImportUtil.importExcelMore(
                    multipartFile.getInputStream(),
                    TestProductCase.class,
                    params);

            List<TestProductCase> list = result.getList();
            if (CollUtil.isEmpty(list))
            {
                return ImportResult.builder()
                        .success(true)
                        .message("没有数据需要导入")
                        .build();
            }

            consumer.accept(ImportProgressEvent.builder()
                    .taskId(taskId)
                    .status(ImportStatus.PROCESSING)
                    .progress(50)
                    .message("开始处理导入数据...")
                    .build());

            // 处理导入数据
            ImportResult importResult = processTestProductCaseImport(list, importTaskSaveDTO);

            consumer.accept(ImportProgressEvent.builder()
                    .taskId(taskId)
                    .status(importResult.isSuccess() ? ImportStatus.SUCCESS : ImportStatus.FAILED)
                    .progress(100)
                    .message(importResult.getMessage())
                    .build());

            return importResult;

        }
        catch (Exception e)
        {
            log.error("导入测试过程用例失败", e);
            consumer.accept(ImportProgressEvent.builder()
                    .taskId(taskId)
                    .status(ImportStatus.FAILED)
                    .progress(100)
                    .message("导入失败: " + e.getMessage())
                    .build());
            return ImportResult.builder()
                    .success(false)
                    .message("导入失败: " + e.getMessage())
                    .build();
        }
        finally
        {
            // 清理字典处理器缓存
            testProductCaseDictHandler.clear();
        }
    }

    /**
     * 处理测试过程用例导入
     */
    private ImportResult processTestProductCaseImport(List<TestProductCase> testCases,
            TestProductCaseImportTaskSaveDTO importTaskSaveDTO)
    {
        try
        {
            // 1. 校验任务是否存在
            Task task = taskApi.getById(importTaskSaveDTO.getTaskId());
            if (task == null)
            {
                return ImportResult.builder()
                        .success(false)
                        .message("任务不存在")
                        .build();
            }

            // 2. 获取任务关联的系统交易
            List<Long> taskModuleFunctionIds = getTaskModuleFunctionIds(importTaskSaveDTO.getProjectId(),
                    importTaskSaveDTO.getTestreqId(),importTaskSaveDTO.getTaskId());
            if (taskModuleFunctionIds.isEmpty())
            {
                return ImportResult.builder()
                        .success(false)
                        .message("当前任务未关联任何系统交易")
                        .build();
            }

            // 3. 处理导入数据（新增/更新逻辑）
            TestCaseImportResultDTO importResult = processTestProductCaseImportWithCaseKey(testCases, importTaskSaveDTO, task, taskModuleFunctionIds);

            if (!importResult.isSuccess()) {
                return ImportResult.builder()
                        .success(false)
                        .message(importResult.getErrorMessage())
                        .build();
            }

            return ImportResult.builder()
                    .success(true)
                    .message(importResult.getSuccessMessage())
                    .build();

        }
        catch (Exception e)
        {
            log.error("处理导入数据失败", e);
            return ImportResult.builder()
                    .success(false)
                    .message("处理导入数据失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 处理测试过程用例导入（支持case_key的新增/更新逻辑）
     *
     * @param testCases 测试用例列表
     * @param importTaskSaveDTO 导入任务参数
     * @param task 任务信息
     * @param taskModuleFunctionIds 任务关联的系统交易ID列表
     * @return 导入结果统计
     */
    private TestCaseImportResultDTO processTestProductCaseImportWithCaseKey(
            List<TestProductCase> testCases,
            TestProductCaseImportTaskSaveDTO importTaskSaveDTO,
            Task task,
            List<Long> taskModuleFunctionIds) {

        List<TestCaseImportResultDTO.ImportErrorDetail> errorDetails = new ArrayList<>();
        List<TestProductCase> insertList = new ArrayList<>();
        List<TestProductCase> updateList = new ArrayList<>();

        // 1. 执行基础校验
        TestCaseImportResultDTO validationResult = validateTestProductCasesForImport(
                testCases, importTaskSaveDTO, task, taskModuleFunctionIds, errorDetails);
        if (!validationResult.isSuccess()) {
            return validationResult;
        }

        // 2. 批量查询现有用例编号
        Set<String> caseKeys = testCases.stream()
                .map(TestProductCase::getCaseKey)
                .filter(StringUtil::isNotEmpty)
                .collect(Collectors.toSet());

        Map<String, TestProductCase> existingCaseMap = new HashMap<>();
        if (!caseKeys.isEmpty()) {
            List<TestProductCase> existingCases = lambdaQuery()
                    .in(TestProductCase::getCaseKey, caseKeys)
                    .list();
            existingCaseMap = existingCases.stream()
                    .collect(Collectors.toMap(TestProductCase::getCaseKey, Function.identity()));
        }

        // 3. 分类处理：新增/更新
        for (int i = 0; i < testCases.size(); i++) {
            TestProductCase testCase = testCases.get(i);
            int rowNum = i + 3; // Excel行号（标题行+表头行+数据行索引）

            try {
                if (StringUtil.isNotEmpty(testCase.getCaseKey())) {
                    // 有case_key：检查是否存在
                    TestProductCase existingCase = existingCaseMap.get(testCase.getCaseKey());
                    if (existingCase != null) {
                        // 存在：准备更新
                        prepareUpdateTestProductCase(testCase, existingCase, importTaskSaveDTO, task);
                        updateList.add(testCase);
                    } else {
                        // 不存在：报错
                        errorDetails.add(TestCaseImportResultDTO.ImportErrorDetail.builder()
                                .rowNumber(rowNum)
                                .caseKey(testCase.getCaseKey())
                                .errorType("用例不存在")
                                .errorMessage("用例编号不存在，无法更新")
                                .build());
                    }
                } else {
                    // 无case_key：生成编号并新增
                    prepareInsertTestProductCase(testCase, importTaskSaveDTO, task);
                    insertList.add(testCase);
                }
            } catch (Exception e) {
                log.error("处理第{}行数据失败", rowNum, e);
                errorDetails.add(TestCaseImportResultDTO.ImportErrorDetail.builder()
                        .rowNumber(rowNum)
                        .caseKey(testCase.getCaseKey())
                        .errorType("处理异常")
                        .errorMessage("处理失败: " + e.getMessage())
                        .build());
            }
        }

        // 4. 如果有校验错误，返回失败结果
        if (!errorDetails.isEmpty()) {
            return TestCaseImportResultDTO.failure("数据校验失败", errorDetails);
        }

        // 5. 批量保存
        int insertCount = 0;
        int updateCount = 0;

        try {
            // 批量新增
            if (!insertList.isEmpty()) {
                super.saveBatch(insertList);
                insertCount = insertList.size();

                // 为新增用例创建修订记录
                for (TestProductCase testCase : insertList) {
                    createRevision(testCase, 1, ProductCaseRevisionOperationTypeEnum.ADD, "导入用例");
                }
            }

            // 批量更新
            if (!updateList.isEmpty()) {
                super.updateBatchById(updateList);
                updateCount = updateList.size();

                // 为更新用例创建修订记录
                for (TestProductCase testCase : updateList) {
                    int versionNo = Integer.parseInt(testCase.getVersion());
                    createRevision(testCase, versionNo, ProductCaseRevisionOperationTypeEnum.UPDATE, "导入更新用例");
                }
            }

            return TestCaseImportResultDTO.success(insertCount, updateCount);

        } catch (Exception e) {
            log.error("批量保存用例失败", e);
            return TestCaseImportResultDTO.failure("保存失败: " + e.getMessage(), errorDetails);
        }
    }

    /**
     * 获取任务关联的系统交易ID列表
     */
    private List<Long> getTaskModuleFunctionIds(Long projectId, Long reqId, Long taskId)
    {
        try
        {
            // 通过ProductModuleFunctionApi查询任务关联的系统交易
            List<ProductModuleFunction> moduleFunctions = moduleFunctionApi.findModuleFunctionByProjectId(
                    projectId, taskId , reqId, null);

            return moduleFunctions.stream()
                    .map(ProductModuleFunction::getId)
                    .collect(Collectors.toList());
        }
        catch (Exception e)
        {
            log.error("查询任务关联的系统交易失败", e);
            return new ArrayList<>();
        }
    }



    @Override
    public String getProcessorName()
    {
        return FileProcessorConstants.TEST_PRODUCT_CASE_IMPORT;
    }

    @Override
    public Set<String> getSupportedFileTypes()
    {
        return CollUtil.newHashSet(".xls", ".xlsx");
    }

    @Override
    public Class<TestProductCaseImportTaskSaveDTO> getExtraType(){
        return TestProductCaseImportTaskSaveDTO.class;
    }

    /**
     * 校验测试过程用例导入数据
     */
    private TestCaseImportResultDTO validateTestProductCasesForImport(
            List<TestProductCase> testCases,
            TestProductCaseImportTaskSaveDTO importTaskSaveDTO,
            Task task,
            List<Long> taskModuleFunctionIds,
            List<TestCaseImportResultDTO.ImportErrorDetail> errorDetails) {

        for (int i = 0; i < testCases.size(); i++) {
            TestProductCase testCase = testCases.get(i);
            int rowNum = i + 3; // Excel行号（标题行+表头行+数据行索引）

            // 校验关联交易
            if (testCase.getModuleFunctionId() == null) {
                errorDetails.add(TestCaseImportResultDTO.ImportErrorDetail.builder()
                        .rowNumber(rowNum)
                        .caseKey(testCase.getCaseKey())
                        .errorType("字段缺失")
                        .errorMessage("关联交易不能为空")
                        .build());
                continue;
            }

            if (!taskModuleFunctionIds.contains(testCase.getModuleFunctionId())) {
                errorDetails.add(TestCaseImportResultDTO.ImportErrorDetail.builder()
                        .rowNumber(rowNum)
                        .caseKey(testCase.getCaseKey())
                        .errorType("数据不匹配")
                        .errorMessage("关联交易不属于当前任务范围")
                        .build());
                continue;
            }

            // 设置任务相关信息
            testCase.setTaskId(importTaskSaveDTO.getTaskId());
            testCase.setProjectId(task.getProjectId());
            testCase.setTestreqId(task.getTestreqId());
        }

        // 如果有校验错误，返回失败结果
        if (!errorDetails.isEmpty()) {
            return TestCaseImportResultDTO.failure("数据校验失败", errorDetails);
        }

        return TestCaseImportResultDTO.success(0, 0);
    }

    /**
     * 准备新增测试过程用例
     */
    private void prepareInsertTestProductCase(TestProductCase testCase,
            TestProductCaseImportTaskSaveDTO importTaskSaveDTO, Task task) {

        // 生成用例编号
        if (StringUtil.isEmpty(testCase.getCaseKey()) && testCase.getModuleFunctionId() != null) {
            String sysCode = moduleFunctionApi.findProductInfoCodeByFunctionId(testCase.getModuleFunctionId());
            if (StringUtil.isNotEmpty(sysCode)) {
                testCase.setCaseKey(getProductCaseKey(sysCode));
            }
        }

        // 设置基础字段
        testCase.setId(UidGeneratorUtil.getId());
        testCase.setCreateTime(LocalDateTime.now());
        testCase.setCreatedBy(ContextUtil.getUserId());
        testCase.setUpdateTime(LocalDateTime.now());
        testCase.setUpdatedBy(ContextUtil.getUserId());
        testCase.setVersion("1");
    }

    /**
     * 准备更新测试过程用例
     */
    private void prepareUpdateTestProductCase(TestProductCase testCase, TestProductCase existingCase,
            TestProductCaseImportTaskSaveDTO importTaskSaveDTO, Task task) {

        // 保留原有ID和基础信息
        testCase.setId(existingCase.getId());
        testCase.setCreateTime(existingCase.getCreateTime());
        testCase.setCreatedBy(existingCase.getCreatedBy());

        // 更新版本号
        int currentVersionNo = Integer.parseInt(existingCase.getVersion());
        int newVersionNo = currentVersionNo + 1;
        testCase.setVersion(String.valueOf(newVersionNo));

        // 设置更新信息
        testCase.setUpdateTime(LocalDateTime.now());
        testCase.setUpdatedBy(ContextUtil.getUserId());
    }

}
