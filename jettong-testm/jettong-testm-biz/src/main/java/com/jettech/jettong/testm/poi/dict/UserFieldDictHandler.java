package com.jettech.jettong.testm.poi.dict;

import cn.hutool.core.collection.CollUtil;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.util.poi.dict.AbstractSingleFieldDictHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 用户字段字典处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户字段字典处理器，处理用户相关字段的字典转换
 * @projectName jettong-tm
 * @package com.jettech.jettong.testm.poi.dict
 * @className UserFieldDictHandler
 * @date 2025-09-16
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component("tmUserFieldDictHandler")
@RequiredArgsConstructor
public class UserFieldDictHandler extends AbstractSingleFieldDictHandler {

    private static final Set<String> dictList = CollUtil.newHashSet("userName", "leadingBy");

    private final UserApi userApi;

    @Override
    public Supplier<Map<String, String>> dataSupplier() {
        return () -> userApi.findUserListByQuery(new User()).stream()
                .collect(Collectors.toMap(
                        user -> String.valueOf(user.getId()),
                        user -> String.format("%s(%s)", user.getName(), user.getAccount())
                ));
    }

    @Override
    protected Set<String> getDictList() {
        return dictList;
    }
}
