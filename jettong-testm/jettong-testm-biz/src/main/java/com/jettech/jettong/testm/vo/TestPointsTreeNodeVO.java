package com.jettech.jettong.testm.vo;


import com.jettech.basic.base.entity.TreeEntity;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong
 * @package com.jettech.jettong.testm.vo
 * @className TestPointsTreeVO
 * @date 2025/8/1 09:57
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class TestPointsTreeNodeVO extends TreeEntity<TestPointsTreeNodeVO, Long>
{
    private PointsDataVo data;
}
