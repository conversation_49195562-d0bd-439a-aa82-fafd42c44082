package com.jettech.jettong.testm.poi.dict;

import com.jettech.jettong.common.util.poi.dict.SingleFieldDictHandler;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * TestProductCase 导入导出字典处理类
 *
 * <AUTHOR>
 * @version 1.0
 * @description TestProductCase 导入导出字典处理类，基于common包接口实现
 * @projectName jettong-tm
 * @package com.jettech.jettong.testm.poi.dict
 * @className TestProductCaseDictHandler
 * @date 2025-09-16
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Service
public class TestProductCaseDictHandler extends TmCompositeDictHandler {

    public TestProductCaseDictHandler(List<SingleFieldDictHandler> handlerList) {
        super(handlerList);
    }
}
