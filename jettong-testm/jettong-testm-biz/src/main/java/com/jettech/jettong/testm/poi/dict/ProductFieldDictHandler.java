package com.jettech.jettong.testm.poi.dict;

import cn.hutool.core.collection.CollUtil;
import com.jettech.jettong.common.util.poi.dict.AbstractSingleFieldDictHandler;
import com.jettech.jettong.product.api.ProductInfoApi;
import com.jettech.jettong.product.entity.ProductInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 产品字段字典处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品字段字典处理器，处理产品相关字段的字典转换
 * @projectName jettong-tm
 * @package com.jettech.jettong.testm.poi.dict
 * @className ProductFieldDictHandler
 * @date 2025-09-16
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component("tmProductFieldDictHandler")
@RequiredArgsConstructor
public class ProductFieldDictHandler extends AbstractSingleFieldDictHandler
{

    private static final Set<String> dictList = CollUtil.newHashSet("productId");

    private final ProductInfoApi productInfoApi;

    private String dictVar;

    @Override
    public boolean enable(String dict) {
        dictVar = dict;
        return getDictList().contains(dict);
    }

    @Override
    public Supplier<Map<String, String>> dataSupplier()
    {
        return () -> {
            try {
                Map<String, String> result = productInfoApi.findAll().stream().collect(Collectors.toMap(
                                ProductInfo::getId,
                                p -> p,
                                (existing, replacement) -> existing // 遇到重复code时保留已存在的元素
                        ))
                        .values().stream()
                        .collect(Collectors.toMap(
                                info -> String.valueOf(info.getId()),
                                ProductInfo::getName
                        ));
                return result;
            } catch (Exception e) {
                return Collections.emptyMap();
            }
        };
    }

    @Override
    protected Set<String> getDictList()
    {
        return dictList;
    }

    @Override
    public Map<String, String[]> dropMap(Long projectId) {
        Map<String, String[]> result = new HashMap<>();

        // 为每个支持的字段生成下拉数据
        for (String field : getDictList()) {
            try {
                // 设置当前处理的字段
                dictVar = field;

                // 获取该字段的下拉数据
                String[] values = getBiMap().values().toArray(new String[0]);
                result.put(field, values);

            } catch (Exception e) {
                result.put(field, new String[0]);
            }
        }

        return result;
    }
}
