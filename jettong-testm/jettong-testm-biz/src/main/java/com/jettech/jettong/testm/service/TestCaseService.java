package com.jettech.jettong.testm.service;


import com.jettech.basic.base.service.SuperService;
import com.jettech.jettong.testm.dto.TestCasePageQuery;
import com.jettech.jettong.testm.entity.TestCase;
import com.jettech.jettong.testm.vo.TestCaseTypeComponentResult;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.HashMap;
import java.util.List;

/**
 * 测试案例表业务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例表业务接口
 * @projectName jettong
 * @package com.jettech.jettong.testm.service
 * @className TestCaseService
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface TestCaseService extends SuperService<TestCase>
{
    void saveTestCase(TestCase testCase);

    List<TestCase> findTestCaseOfTestPlan(Long planId);

    void copyCaseById(Long item, Long treeId);

    TestCaseTypeComponentResult findTypeByProjectId(Long projectId);

    HashMap<String, Object> findCaseUserById(Long projectId);

    List<TestCase> queryTestCaseByIds(List<Long> testCaseIds);

    /**
     * 从注册表入库到用例库
     * @param registryIds 注册表记录ID
     */
    void saveToLibrary(List<Long> registryIds);

    void restoreFromRevision(Long revisionId);

    String getProductCaseKey(String sysCode);

    String getMaxCaseKey(String sysCode);

    /**
     * 增加用例库用例的引用计数
     * @param testCaseId 用例库用例ID
     */
    void incrementReferenceCount(Long testCaseId);

    /**
     * 减少用例库用例的引用计数
     * @param testCaseId 用例库用例ID
     */
    void decrementReferenceCount(Long testCaseId);

    /**
     * 导出测试用例
     * @param params 查询参数
     * @return Workbook
     */
    Workbook exportTestCases(com.jettech.jettong.testm.dto.TestCasePageQuery params);

    /**
     * 下载测试用例导入模板
     * @return Workbook
     */
    Workbook downloadTestCaseImportTemplate();
}
