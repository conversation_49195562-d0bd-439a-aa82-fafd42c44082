package com.jettech.jettong.testm.service;

import com.jettech.basic.base.service.SuperService;
import com.jettech.basic.model.LoadService;
import com.jettech.jettong.testm.dto.TestProductCasePageQuery;
import com.jettech.jettong.testm.entity.TestProductCaseRegistry;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.Map;

/**
 * TestProductCaseRegistry 业务接口
 * <AUTHOR>
 * @version 1.0
 * @description TestProductCaseRegistry 业务接口
 * @projectName jettong
 * @package com.jettech.jettong.testm.service
 * @className TestProductCaseRegistryService
 * @date 2024-01-01
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface TestProductCaseRegistryService extends SuperService<TestProductCaseRegistry>, LoadService {

    void saveOrUpdateByCaseKey(TestProductCaseRegistry registry);

    Map<String, Object> export(TestProductCasePageQuery query);
}
