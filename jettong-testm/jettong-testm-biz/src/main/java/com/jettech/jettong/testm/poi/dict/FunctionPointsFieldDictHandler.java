package com.jettech.jettong.testm.poi.dict;

import cn.hutool.core.collection.CollUtil;
import com.jettech.jettong.common.util.poi.dict.AbstractSingleFieldDictHandler;
import com.jettech.jettong.testm.entity.TestRequirementFunctionPoints;
import com.jettech.jettong.testm.service.TestRequirementFunctionPointsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;

/**
 * 测试功能点字段处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试功能点字段处理器，处理functionPointsId字段转换
 * @projectName jettong-tm
 * @package com.jettech.jettong.testm.poi.dict
 * @className FunctionPointsFieldDictHandler
 * @date 2025-09-17
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component("tmFunctionPointsFieldDictHandler")
@RequiredArgsConstructor
public class FunctionPointsFieldDictHandler extends AbstractSingleFieldDictHandler {

    private static final Set<String> dictList = CollUtil.newHashSet("functionPointsId");

    private final TestRequirementFunctionPointsService testRequirementFunctionPointsService;

    @Override
    public Supplier<Map<String, String>> dataSupplier() {
        return () -> {
            try {
                // 查询所有测试功能点
                List<TestRequirementFunctionPoints> functionPoints = testRequirementFunctionPointsService.list();

                // 针对名称相同的测试点，只保留第一个（在需求下唯一）
                Map<String, String> result = new LinkedHashMap<>();
                Set<String> seenNames = new HashSet<>();

                for (TestRequirementFunctionPoints point : functionPoints) {
                    String pointName = point.getFunctionPoint();

                    // 如果名称已经存在，跳过后续相同名称的测试点
                    if (seenNames.contains(pointName)) {
                        continue;
                    }

                    seenNames.add(pointName);
                    String key = String.valueOf(point.getId());
                    String value = point.getFunctionPoint();
                    result.put(key, value);
                }

                return result;
            } catch (Exception e) {
                // 如果查询失败，返回空Map
                return new HashMap<>();
            }
        };
    }

    @Override
    protected Set<String> getDictList() {
        return dictList;
    }

    /**
     * 根据项目ID或需求ID查询测试功能点
     * 
     * @param projectId 项目ID
     * @param testreqId 测试需求ID
     * @return 功能点映射
     */
    public Map<String, String> getDataByProjectOrRequirement(Long projectId, Long testreqId) {
        try {
            // 根据项目ID或需求ID查询功能点
            List<TestRequirementFunctionPoints> functionPoints = testRequirementFunctionPointsService.lambdaQuery()
                    .eq(projectId != null, TestRequirementFunctionPoints::getProjectId, projectId)
                    .eq(testreqId != null, TestRequirementFunctionPoints::getIssueTestReqId, testreqId)
                    .list();

            // 针对名称相同的测试点，只保留第一个（在需求下唯一）
            Map<String, String> result = new LinkedHashMap<>();
            Set<String> seenNames = new HashSet<>();

            for (TestRequirementFunctionPoints point : functionPoints) {
                String pointName = point.getFunctionPoint();

                // 如果名称已经存在，跳过后续相同名称的测试点
                if (seenNames.contains(pointName)) {
                    continue;
                }

                seenNames.add(pointName);
                String key = String.valueOf(point.getId());
                String value = point.getFunctionPoint();
                result.put(key, value);
            }

            return result;
        } catch (Exception e) {
            return new HashMap<>();
        }
    }
}
