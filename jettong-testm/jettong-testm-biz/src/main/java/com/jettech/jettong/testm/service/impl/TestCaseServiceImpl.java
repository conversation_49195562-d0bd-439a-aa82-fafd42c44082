package com.jettech.jettong.testm.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.repository.CacheOps;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.fileimport.model.ImportProgressEvent;
import com.jettech.basic.fileimport.model.ImportResult;
import com.jettech.basic.fileimport.service.FileProcessor;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.SpringUtils;
import com.jettech.basic.utils.StringUtil;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.alm.api.ProjectApi;
import com.jettech.jettong.alm.issue.entity.Priority;
import com.jettech.jettong.base.api.FileApi;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.common.cache.alm.TypeCodePrefixCacheKeyBuilder;
import com.jettech.jettong.common.cache.testm.TestMCacheKeyBuilder;
import com.jettech.jettong.common.constant.FileProcessorConstants;
import com.jettech.jettong.common.enumeration.CaseRevisionOperationTypeEnum;
import com.jettech.jettong.common.enumeration.CaseSourceTypeEnum;
import com.jettech.jettong.product.api.ProductModuleFunctionApi;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.dao.TestCaseMapper;
import com.jettech.jettong.testm.dto.TestCaseImportResultDTO;
import com.jettech.jettong.testm.dto.TestCaseImportTaskSaveDTO;
import com.jettech.jettong.testm.dto.TestCasePageQuery;
import com.jettech.jettong.testm.entity.*;
import com.jettech.jettong.testm.poi.dict.TestCaseDictHandler;
import com.jettech.jettong.testm.service.*;
import com.jettech.jettong.testm.vo.TestCaseTypeComponentResult;
import com.jettech.jettong.common.util.poi.ExcelExportPlusUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static org.apache.poi.ss.usermodel.Font.COLOR_RED;

@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class TestCaseServiceImpl extends SuperServiceImpl<TestCaseMapper, TestCase> implements TestCaseService,
        FileProcessor<TestCaseImportTaskSaveDTO>
{
    private static final String SHEETNAME = "测试用例";
    private final CacheOps cacheOps;

    public static final String PREFIX="TC-";

    private final ProductCaseLibraryService productCaseLibraryService;

    private final ProjectApi priorityApi;

    private final UserApi userApi;

    private final TestProductCaseService testProductCaseService;

    public final FileApi fileApi;
    public final ProductModuleFunctionApi moduleFunctionApi;
    private final TestCaseRevisionService testCaseRevisionService;
    private final TestProductCaseRegistryService testProductCaseRegistryService;
    private final TestCaseDictHandler testCaseDictHandler;
    private final TestTabCaseService testTabCaseService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveTestCase(TestCase testCase)
    {
        String sysCode = moduleFunctionApi.findProductInfoCodeByFunctionId(testCase.getModuleFunctionId());

        String caseKey= getProductCaseKey(sysCode);
        testCase.setCaseKey(caseKey);
        testCase.setId(UidGeneratorUtil.getId());
        testCase.setCreateTime(LocalDateTime.now());
        testCase.setCreatedBy(ContextUtil.getUserId());
        testCase.setUpdateTime(LocalDateTime.now());
        testCase.setUpdatedBy(ContextUtil.getUserId());

        // 设置初始版本
        testCase.setVersion("1");

        // 保存文件
        List<File> files = testCase.getFiles();
        if (CollUtil.isNotEmpty(files))
        {
            files.forEach(item -> item.setBizType(FileBizType.TEST_PRODUCT_CASE_FILE_UPLOAD).setBizId(testCase.getId()));
            fileApi.updateBatchById(files);
        }

        //保存意图附件
        List<File> intentFiles = testCase.getIntentFiles();
        if (CollUtil.isNotEmpty(intentFiles))
        {
            intentFiles.forEach(item -> item.setBizType(FileBizType.TEST_PRODUCT_CASE_INTENT_FILE_UPLOAD).setBizId(testCase.getId()));
            fileApi.updateBatchById(intentFiles);
        }

        //保存前置条件附件
        List<File> prerequisiteFiles = testCase.getPrerequisiteFiles();
        if (CollUtil.isNotEmpty(prerequisiteFiles))
        {
            prerequisiteFiles.forEach(item -> item.setBizType(FileBizType.TEST_PRODUCT_CASE_PREREQUISITE_FILE_UPLOAD).setBizId(testCase.getId()));
            fileApi.updateBatchById(prerequisiteFiles);
        }

        //保存测试步骤附件
        List<File> testStepFiles = testCase.getTestStepFiles();
        if (CollUtil.isNotEmpty(testStepFiles))
        {
            testStepFiles.forEach(item -> item.setBizType(FileBizType.TEST_PRODUCT_CASE_TESTSTEP_FILE_UPLOAD).setBizId(testCase.getId()));
            fileApi.updateBatchById(testStepFiles);
        }

        // 使用重载的save方法，避免重复创建修订记录
        super.save(testCase);

        // 创建修订记录
        createRevision(testCase, 1, CaseRevisionOperationTypeEnum.ADD, "创建用例");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(TestCase testCase) {
        return save(testCase, CaseRevisionOperationTypeEnum.ADD, "创建用例");
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean save(TestCase testCase, CaseRevisionOperationTypeEnum operationType, String operationDesc) {
        return save(testCase, operationType, operationDesc, null, null, null);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean save(TestCase testCase, CaseRevisionOperationTypeEnum operationType, String operationDesc,
                       Long sourceProjectId, Long sourceRequirementId, Long sourceTaskId) {
        if (testCase.getModuleFunctionId() != null) {
            ProductModuleFunction function = moduleFunctionApi.findProductModuleFunctionById(testCase.getModuleFunctionId());
            if (operationType != CaseRevisionOperationTypeEnum.IMPORT) {
                testCase.setCaseKey(getProductCaseKey(function.getCode()));
            }
            testCase.setProductId(function.getProductId());
        }
        testCase.setVersion("1");
        boolean result = super.save(testCase);
        if (result) {
            createRevision(testCase, 1, operationType, operationDesc, sourceProjectId, sourceRequirementId, sourceTaskId);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(TestCase testCase) {
        TestCase oldCase = getById(testCase.getId());
        if (oldCase == null) {
            throw new BizException("用例不存在");
        }

        int currentVersionNo = 0;
        try {
            currentVersionNo = Integer.parseInt(oldCase.getVersion());
        } catch (NumberFormatException e) {
            currentVersionNo = 1;
        }
        int newVersionNo = currentVersionNo + 1;
        testCase.setVersion(String.valueOf(newVersionNo));

        boolean result = super.updateById(testCase);
        if (result) {
            createRevision(testCase, newVersionNo, CaseRevisionOperationTypeEnum.UPDATE, "修改用例");
        }
        return result;
    }

    private void createRevision(TestCase testCase, int versionNo, CaseRevisionOperationTypeEnum operationType, String desc) {
        createRevision(testCase, versionNo, operationType, desc, null, null, null);
    }

    private void createRevision(TestCase testCase, int versionNo, CaseRevisionOperationTypeEnum operationType, String desc,
                               Long sourceProjectId, Long sourceRequirementId, Long sourceTaskId) {
        TestCaseRevision revision = BeanPlusUtil.toBean(testCase, TestCaseRevision.class);
        revision.setId(UidGeneratorUtil.getId());
        revision.setCaseId(testCase.getId());
        revision.setVersionNo(versionNo);
        revision.setOperationType(operationType);
        revision.setOperationDesc(desc);

        if (operationType == CaseRevisionOperationTypeEnum.IMPORT) {
            revision.setSourceProjectId(sourceProjectId);
            revision.setSourceRequirementId(sourceRequirementId);
            revision.setSourceTaskId(sourceTaskId);
        }

        testCaseRevisionService.save(revision);
    }

    @Override
    public String getProductCaseKey(String sysCode) {
        CacheKey cacheKey = new TypeCodePrefixCacheKeyBuilder().key(sysCode);
        Long codeNum = cacheOps.incr(cacheKey);
        if (codeNum == null || codeNum == 1)
        {
            String productCaseMaxKey;
            String testCaseMaxKey;
            try
            {
                TestProductCaseService testProductCaseService = SpringUtils.getBean(TestProductCaseService.class);
                productCaseMaxKey = testProductCaseService.getMaxCaseKey(sysCode);
                testCaseMaxKey = this.getMaxCaseKey(sysCode);
            }
            finally
            {
                cacheOps.del(cacheKey);
            }
            Long productCaseNum = 0L;
            if (productCaseMaxKey != null) {
                productCaseNum = StringUtil.getLongToStr(productCaseMaxKey);
            }
            Long testCaseNum = 0L;
            if (testCaseMaxKey != null) {
                testCaseNum = StringUtil.getLongToStr(testCaseMaxKey);
            }

            Long num = Math.max(productCaseNum, testCaseNum);

            Long incr = cacheOps.incrBy(cacheKey, num + 1);
            return sysCode + String.format("%07d", incr);
        }
        return sysCode + String.format("%07d", codeNum);
    }

    @Override
    public String getMaxCaseKey(String sysCode) {
        TestCase testCase = getOne(Wraps.<TestCase>lbQ()
                .likeRight(TestCase::getCaseKey, sysCode)
                .orderByDesc(TestCase::getCaseKey) // Order by the key itself
                .last("limit 1"), false);
        if (testCase != null) {
            String maxKey = testCase.getCaseKey();
            if (maxKey != null && maxKey.startsWith(sysCode)) {
                return maxKey.substring(sysCode.length());
            }
        }
        return null;
    }

    @Override
    public List<TestCase> findTestCaseOfTestPlan(Long planId)
    {

        return baseMapper.findTestCaseOfTestPlan(planId);
    }

    private synchronized String getCaseKey(String prefix)
    {
        CacheKey cacheKey = new TestMCacheKeyBuilder().key(prefix);
        Long codeNum = cacheOps.incr(cacheKey);
        if (codeNum == null || codeNum == 1)
        {
            TestCase testCase = super.getOne(Wraps.<TestCase>lbQ().orderByDesc(TestCase::getCreateTime), false);
            long num = 1L;
            if (testCase != null)
            {
                num = StringUtil.getLongToStr(testCase.getCaseKey()) + 1;
            }
            cacheOps.incrBy(cacheKey, num);
            return prefix + (num + 1);
        }
        return prefix + codeNum;
    }

    @Override
    public void copyCaseById(Long item, Long treeId)
    {
        TestCase byId = baseMapper.selectById(item);
        byId.setId(UidGeneratorUtil.getId());
        byId.setCreatedBy(ContextUtil.getUserId());
        byId.setCaseKey(getCaseKey(PREFIX));
        byId.setLeadingBy(ContextUtil.getUserId());
        byId.setUpdatedBy(ContextUtil.getUserId());
        super.save(byId);
    }

    @Override
    public TestCaseTypeComponentResult findTypeByProjectId(Long projectId)
    {
        ProductCaseLibrary testCaseLibrary = productCaseLibraryService
                .list(Wraps.<ProductCaseLibrary>lbQ().eq(ProductCaseLibrary::getProjectId, projectId)).get(0);
        List<TestProductCase> testProductCases = 
                testProductCaseService.list(Wraps.<TestProductCase>lbQ().eq(TestProductCase::getLibraryId, testCaseLibrary.getId()));

        List<Priority> priorities = priorityApi.getAll();

        Map<String, Priority> codeNameMap = priorities.stream().collect(Collectors.toMap(Priority::getCode, priority -> priority));


        Map<Integer, List<TestProductCase>> collect = testProductCases.stream().collect(Collectors.groupingBy(TestProductCase::getPriority));

        List<TestCaseTypeComponentResult.Pie> data = new LinkedList<>();

        for (Map.Entry<Integer, List<TestProductCase>> entry : collect.entrySet())
        {
            data.add(TestCaseTypeComponentResult.Pie.builder()
                    .name(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getName() :
                            entry.getKey()+"")
                    .color(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getColor() :
                            entry.getKey()+"")
                    .value(entry.getValue().size()).build());
        }

        return TestCaseTypeComponentResult.builder().count(testProductCases.size()).data(data).build();
    }

    @Override
    public HashMap<String, Object> findCaseUserById(Long projectId)
    {
        ProductCaseLibrary testCaseLibrary = productCaseLibraryService
                .list(Wraps.<ProductCaseLibrary>lbQ().eq(ProductCaseLibrary::getProjectId, projectId)).get(0);
        List<TestProductCase> testProductCases = 
                testProductCaseService.list(Wraps.<TestProductCase>lbQ().eq(TestProductCase::getLibraryId, testCaseLibrary.getId()));

        ArrayList<Long> name = new ArrayList<>();
        Map<Long, List<TestProductCase>> collect = testProductCases.stream().collect(Collectors.groupingBy(TestProductCase::getCreatedBy));
        for(Long key : collect.keySet()){
            name.add(key);
        }
        ArrayList<Integer> conut = new ArrayList<>();
        for (Long user : name)
        {
            int size = collect.get(user).size();
            conut.add(size);
        }
        ArrayList<String> objects = new ArrayList<>();
        name.forEach(item->
        {
            objects.add(userApi.findUserById(item).getName());
        });

        HashMap<String, Object> result = new HashMap<>();
        result.put("user",objects);
        result.put("count",conut);
        return result;
    }

    @Override
    public List<TestCase> queryTestCaseByIds(List<Long> testCaseIds) {
        return baseMapper.selectBatchIds(testCaseIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveToLibrary(List<Long> registryIds) {
        List<TestProductCaseRegistry> registries = testProductCaseRegistryService.listByIds(registryIds);
        if (registries.isEmpty()) {
            throw new BizException("过程用例记录不存在");
        }

        for (TestProductCaseRegistry registry : registries) {
            TestCase testCase = getOne(Wraps.<TestCase>lbQ().eq(TestCase::getCaseKey, registry.getCaseKey()));

            if (testCase != null) {
                int currentVersionNo = 0;
                try {
                    currentVersionNo = Integer.parseInt(testCase.getVersion());
                } catch (NumberFormatException e) {
                    currentVersionNo = 1;
                }
                int newVersionNo = currentVersionNo + 1;

                BeanPlusUtil.copyProperties(registry, testCase);
                testCase.setVersion(String.valueOf(newVersionNo));
                super.updateById(testCase);
                createRevision(testCase, newVersionNo, CaseRevisionOperationTypeEnum.IMPORT, "从注册表入库",
                        registry.getProjectId(), registry.getRequirementId(), registry.getTaskId());
            } else {
                testCase = BeanPlusUtil.toBean(registry, TestCase.class);
                testCase.setVersion("1");
                testCase.setId(null);
                testCase.setSourceType(CaseSourceTypeEnum.FROM_PRODUCT);
                save(testCase, CaseRevisionOperationTypeEnum.IMPORT, "从注册表入库",
                        registry.getProjectId(), registry.getTestreqId(), registry.getTaskId());
            }
        }
        registries.forEach(item-> item.setSavedToLib(true));
        testProductCaseRegistryService.updateBatchById(registries);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void restoreFromRevision(Long revisionId) {
        TestCaseRevision revision = testCaseRevisionService.getById(revisionId);
        if (revision == null) {
            throw new BizException("修订记录不存在");
        }

        TestCase testCase = getById(revision.getCaseId());
        if (testCase == null) {
            throw new BizException("用例不存在");
        }

        BeanPlusUtil.copyProperties(revision, testCase);

        int newVersionNo = 0;
        try {
            newVersionNo = Integer.parseInt(testCase.getVersion()) + 1;
        } catch (NumberFormatException e) {
            newVersionNo = revision.getVersionNo() + 1;
        }
        testCase.setVersion(String.valueOf(newVersionNo));

        super.updateById(testCase);
        createRevision(testCase, newVersionNo, CaseRevisionOperationTypeEnum.RESTORE, "从版本 " + revision.getVersionNo() + " 还原");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void incrementReferenceCount(Long testCaseId) {
        TestCase testCase = getById(testCaseId);
        if (testCase == null) {
            throw new BizException("用例库用例不存在");
        }

        int currentCount = testCase.getReferenceCount() == null ? 0 : testCase.getReferenceCount();
        testCase.setReferenceCount(currentCount + 1);
        super.updateById(testCase);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void decrementReferenceCount(Long testCaseId) {
        TestCase testCase = getById(testCaseId);
        if (testCase == null) {
            throw new BizException("用例库用例不存在");
        }

        int currentCount = testCase.getReferenceCount() == null ? 0 : testCase.getReferenceCount();
        testCase.setReferenceCount(Math.max(0, currentCount - 1)); // 确保不会小于0
        super.updateById(testCase);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<? extends Serializable> idList) {
        if (CollUtil.isEmpty(idList)) {
            return true;
        }

        for (Serializable id : idList) {
            TestCase testCase = getById(id);
            if (testCase != null) {
                long count = testProductCaseService.count(Wraps.<TestProductCase>lbQ().eq(TestProductCase::getCaseKey, testCase.getCaseKey()));
                if (count > 0) {
                    throw new BizException("用例库用例[" + testCase.getCaseKey() + "]存在过程用例引用，禁止删除");
                }
            }
        }

        return super.removeByIds(idList);
    }

    @Override
    public Workbook exportTestCases(TestCasePageQuery model) {
        LbqWrapper<TestCase> wrapper = Wraps.lbQ();
        List<TestTabCase> testTabCaseList = new ArrayList<>();
        if(model.getTabName()!=null&&model.getTabName().length > 0){
            for (String tabId : model.getTabName()){
                if (testTabCaseList.size() == 0) {
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ().eq(TestTabCase::getTabId, Long.valueOf(tabId)));
                }else{
                    List<Long> prodIds = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ()
                            .eq(TestTabCase::getTabId, Long.valueOf(tabId))
                            .in(TestTabCase::getProdId, prodIds));
                }
                if (testTabCaseList.size() == 0){
                    return buildExcelExportWorkbook(new ArrayList<>());
                }
            }
        }

        wrapper.eq(TestCase::getProductId, model.getProductId())
                .like(TestCase::getName, model.getName())
                .eq(TestCase::getLeadingBy, model.getLeadingBy())
                .eq(TestCase::getPriority,model.getPriority())
                .like(TestCase::getCaseKey,model.getCaseKey());
        List<Long> collect = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
        if (!collect.isEmpty()){
            wrapper.in(TestCase::getId, testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList()));
        }
        if(model.getFunctionIds() != null && model.getFunctionIds().size() > 0){
            wrapper.in(TestCase::getModuleFunctionId,model.getFunctionIds());
        }

        List<TestCase> list = list(wrapper);
        return buildExcelExportWorkbook(list);
    }

    @Override
    public Workbook downloadTestCaseImportTemplate() {
        return buildExcelExportWorkbook(null);
    }

    private Workbook buildExcelExportWorkbook(List<TestCase> data) {
        try {
            if (data == null) {
                data = new ArrayList<>();
            }
            ExportParams exportParams = new ExportParams("测试用例", "测试用例");
            exportParams.setDictHandler(testCaseDictHandler);
            exportParams.setTitle("1、导入模板不同系统不能通用\n" +
                    "2、带*的列必须填写\n" +
                    "3、测试步骤类型为文本类型时，直接填入内容；当测试步骤类型为条目时，测试步骤输入如下格式：1,xxxx,xxxx;2,xxxx,xxxx;");

            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, TestCase.class, data);

            // 添加下拉字段支持
            addDropDownSupport(workbook);

            Row row = workbook.getSheetAt(0).getRow(0);
            row.setHeightInPoints(47);
            CellStyle cellStyle = row.getCell(0).getCellStyle();
            cellStyle.setWrapText(true);
            Font font = workbook.createFont();
            font.setBold(true);
            font.setColor(COLOR_RED);
            cellStyle.setFont(font);
            cellStyle.setAlignment(HorizontalAlignment.LEFT);

            return workbook;
        } finally {
            // 确保字典处理器缓存被清理（防止addDropDownSupport异常时缓存未清理）
            testCaseDictHandler.clear();
        }
    }

    @Override
    public ImportResult processFile(MultipartFile multipartFile, String s, Consumer<ImportProgressEvent> consumer,
            TestCaseImportTaskSaveDTO testCaseImportTaskSaveDTO)
    {
        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        params.setHeadRows(1);
        params.setNeedVerify(true);
        params.setDictHandler(testCaseDictHandler);

        try {
            ExcelImportResult<TestCase> result = ExcelImportUtil.importExcelMore(
                    multipartFile.getInputStream(),
                    TestCase.class,
                    params);

            List<TestCase> list = result.getList();
            if (CollUtil.isEmpty(list)) {
                return ImportResult.builder()
                        .success(true)
                        .message("没有数据需要导入")
                        .build();
            }

            // 处理导入数据（新增/更新逻辑）
            TestCaseImportResultDTO importResult = processTestCaseImport(list, testCaseImportTaskSaveDTO);

            // 转换为ImportResult格式
            return ImportResult.builder()
                    .success(importResult.isSuccess())
                    .message(importResult.isSuccess() ? importResult.getSuccessMessage() : importResult.getErrorMessage())
                    .build();

        } catch (Exception e) {
            log.error("导入失败", e);
            return ImportResult.builder()
                    .success(false)
                    .message("导入失败: " + e.getMessage())
                    .build();
        } finally {
            // 清理字典处理器缓存
            testCaseDictHandler.clear();
        }
    }

    @Override
    public Set<String> getSupportedFileTypes()
    {
        return CollUtil.newHashSet(".xls", ".xlsx");
    }

    @Override
    public String getProcessorName()
    {
        return FileProcessorConstants.LIB_TEST_CASE_IMPORT;
    }

    /**
     * 添加下拉字段支持 (临时方案）
     *
     * @param workbook Excel工作簿
     */
    private void addDropDownSupport(Workbook workbook) {
        try {
            // 获取字典处理器的下拉数据
            Map<String, String[]> dropMap = testCaseDictHandler.dropMap(null);

            if (dropMap != null && !dropMap.isEmpty()) {
                Sheet sheet = workbook.getSheetAt(0);

                // 设置单元格为文本格式
                CellStyle textStyle = workbook.createCellStyle();
                DataFormat dataFormat = workbook.createDataFormat();
                textStyle.setDataFormat(dataFormat.getFormat("@"));

                // 获取表头行，确定字段列的位置
                Row headerRow = sheet.getRow(1); // 第二行是表头（第一行是标题）
                if (headerRow != null) {
                    Map<String, Integer> fieldColumnMap = getFieldColumnMapping(headerRow);

                    // 为每个有下拉数据的字段添加下拉选项
                    for (Map.Entry<String, String[]> entry : dropMap.entrySet()) {
                        String fieldName = entry.getKey();
                        String[] dropDownValues = entry.getValue();

                        Integer columnIndex = fieldColumnMap.get(fieldName);
                        if (columnIndex != null && dropDownValues != null && dropDownValues.length > 0) {
                            // 添加下拉列表，从第3行开始（索引2），到第65535行
                            ExcelExportPlusUtil.addDropDownList(workbook, sheet, dropDownValues, 2, 65535, columnIndex);
                            // 设置列的默认格式为文本
                            sheet.setDefaultColumnStyle(columnIndex, textStyle);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("添加下拉字段支持时发生异常: {}", e.getMessage(), e);
        } finally {
            // 清理字典处理器缓存
            testCaseDictHandler.clear();
        }
    }

    /**
     * 获取字段名到列索引的映射
     *
     * @param headerRow 表头行
     * @return 字段名到列索引的映射
     */
    private Map<String, Integer> getFieldColumnMapping(Row headerRow) {
        Map<String, Integer> fieldColumnMap = new HashMap<>();

        // TestCase中需要下拉支持的字段映射
        Map<String, String> fieldNameMapping = new HashMap<>();
        fieldNameMapping.put("所属系统", "productId");
        fieldNameMapping.put("用例等级", "casePriorityCode");
        fieldNameMapping.put("负责人", "userName");
        fieldNameMapping.put("用例类型", "caseType");
        fieldNameMapping.put("所属交易", "moduleFunctionId");

        // 遍历表头行，建立列名到索引的映射
        for (Cell cell : headerRow) {
            if (cell != null && cell.getCellType() == CellType.STRING) {
                String cellValue = cell.getStringCellValue();
                if (cellValue != null) {
                    // 移除可能的必填标记(*)
                    String cleanCellValue = cellValue.replace("*", "").trim();
                    String fieldName = fieldNameMapping.get(cleanCellValue);
                    if (fieldName != null) {
                        fieldColumnMap.put(fieldName, cell.getColumnIndex());
                    }
                }
            }
        }

        return fieldColumnMap;
    }

    /**
     * 处理测试用例导入（新增/更新逻辑）
     *
     * @param testCases 测试用例列表
     * @param importTaskSaveDTO 导入任务参数
     * @return 导入结果统计
     */
    private TestCaseImportResultDTO processTestCaseImport(List<TestCase> testCases, TestCaseImportTaskSaveDTO importTaskSaveDTO) {
        List<TestCaseImportResultDTO.ImportErrorDetail> errorDetails = new ArrayList<>();
        List<TestCase> insertList = new ArrayList<>();
        List<TestCase> updateList = new ArrayList<>();

        // 1. 执行基础校验
        TestCaseImportResultDTO validationResult = validateTestCasesForImport(testCases, importTaskSaveDTO, errorDetails);
        if (!validationResult.isSuccess()) {
            return validationResult;
        }

        // 2. 批量查询现有用例编号
        Set<String> caseKeys = testCases.stream()
                .map(TestCase::getCaseKey)
                .filter(StringUtil::isNotEmpty)
                .collect(Collectors.toSet());

        Map<String, TestCase> existingCaseMap = new HashMap<>();
        if (!caseKeys.isEmpty()) {
            List<TestCase> existingCases = list(Wraps.<TestCase>lbQ()
                    .in(TestCase::getCaseKey, caseKeys)
                    .eq(TestCase::getProductId, importTaskSaveDTO.getProductId()));
            existingCaseMap = existingCases.stream()
                    .collect(Collectors.toMap(TestCase::getCaseKey, tc -> tc));
        }

        // 3. 分类处理每条数据
        for (int i = 0; i < testCases.size(); i++) {
            TestCase testCase = testCases.get(i);
            int rowNum = i + 3; // Excel行号（标题行+表头行+数据行索引）

            try {
                if (StringUtil.isEmpty(testCase.getCaseKey())) {
                    // 用例编号为空，新增用例
                    processNewTestCase(testCase, importTaskSaveDTO);
                    insertList.add(testCase);
                } else {
                    // 用例编号不为空，检查是否存在
                    TestCase existingCase = existingCaseMap.get(testCase.getCaseKey());
                    if (existingCase != null) {
                        // 存在，更新用例
                        processUpdateTestCase(testCase, existingCase, importTaskSaveDTO);
                        updateList.add(testCase);
                    } else {
                        // 不存在，记录错误
                        errorDetails.add(TestCaseImportResultDTO.ImportErrorDetail.builder()
                                .rowNumber(rowNum)
                                .caseKey(testCase.getCaseKey())
                                .errorType("用例不存在")
                                .errorMessage(String.format("用例编号 '%s' 在当前系统中不存在", testCase.getCaseKey()))
                                .build());
                    }
                }
            } catch (Exception e) {
                log.error("处理第{}行数据时发生异常: {}", rowNum, e.getMessage(), e);
                errorDetails.add(TestCaseImportResultDTO.ImportErrorDetail.builder()
                        .rowNumber(rowNum)
                        .caseKey(testCase.getCaseKey())
                        .errorType("处理异常")
                        .errorMessage("处理数据时发生异常: " + e.getMessage())
                        .build());
            }
        }

        // 4. 批量保存数据
        try {
            if (!insertList.isEmpty()) {
                saveBatch(insertList);
            }
            if (!updateList.isEmpty()) {
                updateBatchById(updateList);
            }
        } catch (Exception e) {
            log.error("批量保存数据失败", e);
            return TestCaseImportResultDTO.failure("保存数据失败: " + e.getMessage(), errorDetails);
        }

        // 5. 返回结果统计
        return TestCaseImportResultDTO.partialSuccess(insertList.size(), updateList.size(), errorDetails);
    }

    /**
     * 处理新增测试用例
     */
    private void processNewTestCase(TestCase testCase, TestCaseImportTaskSaveDTO importTaskSaveDTO) {
        // 生成用例编号
        if (StringUtil.isEmpty(testCase.getCaseKey()) && testCase.getModuleFunctionId() != null) {
            String sysCode = moduleFunctionApi.findProductInfoCodeByFunctionId(testCase.getModuleFunctionId());
            if (StringUtil.isNotEmpty(sysCode)) {
                testCase.setCaseKey(getProductCaseKey(sysCode));
            }
        }

        // 设置基础字段
        testCase.setId(UidGeneratorUtil.getId());
        testCase.setCreateTime(LocalDateTime.now());
        testCase.setCreatedBy(ContextUtil.getUserId());
        testCase.setUpdateTime(LocalDateTime.now());
        testCase.setUpdatedBy(ContextUtil.getUserId());
        testCase.setVersion("1");
    }

    /**
     * 处理更新测试用例
     */
    private void processUpdateTestCase(TestCase newTestCase, TestCase existingCase, TestCaseImportTaskSaveDTO importTaskSaveDTO) {
        // 保留原有的ID和基础信息
        newTestCase.setId(existingCase.getId());
        newTestCase.setCreateTime(existingCase.getCreateTime());
        newTestCase.setCreatedBy(existingCase.getCreatedBy());

        // 更新修改信息
        newTestCase.setUpdateTime(LocalDateTime.now());
        newTestCase.setUpdatedBy(ContextUtil.getUserId());

        // 版本号递增
        String currentVersion = existingCase.getVersion();
        if (StringUtil.isNotEmpty(currentVersion) && StringUtil.isNumeric(currentVersion)) {
            int version = Integer.parseInt(currentVersion) + 1;
            newTestCase.setVersion(String.valueOf(version));
        } else {
            newTestCase.setVersion("2");
        }
    }

    /**
     * 校验测试用例导入数据（新版本）
     *
     * @param testCases 测试用例列表
     * @param importTaskSaveDTO 导入任务参数
     * @param errorDetails 错误详情列表
     * @return 校验结果
     */
    private TestCaseImportResultDTO validateTestCasesForImport(List<TestCase> testCases, TestCaseImportTaskSaveDTO importTaskSaveDTO,
                                                              List<TestCaseImportResultDTO.ImportErrorDetail> errorDetails) {
        if (CollUtil.isEmpty(testCases)) {
            return TestCaseImportResultDTO.failure("导入数据为空", errorDetails);
        }

        // 获取导入任务参数中的系统ID
        Long taskProductId = importTaskSaveDTO.getProductId();
        if (taskProductId == null) {
            return TestCaseImportResultDTO.failure("导入任务参数中的系统ID不能为空", errorDetails);
        }

        // 批量查询所有涉及的交易信息，避免循环查询
        Set<Long> moduleFunctionIds = testCases.stream()
                .map(TestCase::getModuleFunctionId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Map<Long, Long> moduleFunctionProductMap = new HashMap<>();
        if (!moduleFunctionIds.isEmpty()) {
            try {
                // 批量查询交易对应的产品ID
                List<ProductModuleFunction> moduleFunctions = moduleFunctionApi.getProductModuleFunctionListByIds(new ArrayList<>(moduleFunctionIds));
                moduleFunctionProductMap = moduleFunctions.stream()
                        .collect(Collectors.toMap(
                                ProductModuleFunction::getId,
                                ProductModuleFunction::getProductId,
                                (existing, replacement) -> existing
                        ));
            } catch (Exception e) {
                log.error("批量查询交易信息失败", e);
                return TestCaseImportResultDTO.failure("查询交易信息失败: " + e.getMessage(), errorDetails);
            }
        }

        // 逐行校验
        for (int i = 0; i < testCases.size(); i++) {
            TestCase testCase = testCases.get(i);
            int rowNum = i + 3; // Excel行号（标题行+表头行+数据行索引）

            // 1. 校验系统是否与导入任务参数中的系统相同
            if (!Objects.equals(testCase.getProductId(), taskProductId)) {
                errorDetails.add(TestCaseImportResultDTO.ImportErrorDetail.builder()
                        .rowNumber(rowNum)
                        .caseKey(testCase.getCaseKey())
                        .errorType("系统不匹配")
                        .errorMessage("所属系统与导入任务参数中的系统不一致")
                        .build());
                continue;
            }

            // 2. 校验交易是否属于当前系统
            Long moduleFunctionId = testCase.getModuleFunctionId();
            if (moduleFunctionId != null) {
                Long moduleFunctionProductId = moduleFunctionProductMap.get(moduleFunctionId);
                if (moduleFunctionProductId == null) {
                    errorDetails.add(TestCaseImportResultDTO.ImportErrorDetail.builder()
                            .rowNumber(rowNum)
                            .caseKey(testCase.getCaseKey())
                            .errorType("交易不存在")
                            .errorMessage("所属交易不存在或已被删除")
                            .build());
                    continue;
                }
                if (!Objects.equals(moduleFunctionProductId, taskProductId)) {
                    errorDetails.add(TestCaseImportResultDTO.ImportErrorDetail.builder()
                            .rowNumber(rowNum)
                            .caseKey(testCase.getCaseKey())
                            .errorType("交易不匹配")
                            .errorMessage("所属交易不属于当前系统")
                            .build());
                    continue;
                }
            }
        }

        // 如果有校验错误，返回失败结果
        if (!errorDetails.isEmpty()) {
            return TestCaseImportResultDTO.failure("数据校验失败", errorDetails);
        }

        return TestCaseImportResultDTO.success(0, 0);
    }

    /**
     * 批量生成用例编号（性能优化）
     *
     * @param testCases 测试用例列表
     */
    private void generateCaseKeysInBatch(List<TestCase> testCases) {
        if (CollUtil.isEmpty(testCases)) {
            return;
        }


        for (TestCase testCase : testCases) {
            if (StringUtil.isEmpty(testCase.getCaseKey()) && testCase.getModuleFunctionId() != null) {
                try {
                    String sysCode = moduleFunctionApi.findProductInfoCodeByFunctionId(testCase.getModuleFunctionId());
                    if (StringUtil.isNotEmpty(sysCode)) {
                        testCase.setCaseKey(getProductCaseKey(sysCode));
                    }
                } catch (Exception ex) {
                    log.warn("为测试用例生成编号失败: {}", ex.getMessage());
                }
            }
        }
    }
}
