{"theme": {"config": {"node": {"color": "#000000", "marginY": 20, "fontSize": 12, "fillColor": "#EFF3F6", "borderColor": "#7DA0BC", "borderWidth": 1}, "root": {"fontSize": 12, "fillColor": "#7DA0BC", "backgroundColor": "#7DA0BC"}, "second": {"color": "#000000", "fontSize": 12, "fillColor": "#EFF3F6", "borderColor": "#7DA0BC", "borderWidth": 1, "tagPlacement": "left"}, "paddingX": 12, "paddingY": 3, "lineColor": "#7DA0BC", "lineStyle": "curve", "lineRadius": 5, "backgroundColor": "rgb(251, 251, 251)", "generalizationLineColor": "#333", "generalizationLineWidth": 1, "rootLineKeepSameInCurve": true}, "template": "classic4"}, "layout": "logicalStructure", "hierarchy": [{"sort": 1, "type": "root", "level": 1, "style": {"fontSize": 14, "fillColor": "rgba(47, 128, 247, 1)", "lineColor": "rgba(47, 128, 237, 1)", "lineWidth": 1, "borderColor": "#73D8FF", "borderWidth": 1, "borderRadius": 4}, "enabled": true}, {"sort": 2, "type": "system", "typeName": "系统", "level": 2, "style": {"color": "#FFFFFF", "fillColor": "rgba(128, 176, 255, 1)", "lineColor": "rgba(47, 128, 237, 1)", "borderColor": "rgba(128, 176, 255, 1)", "borderWidth": 0, "borderRadius": 4}, "enabled": true}, {"sort": 3, "type": "trade", "typeName": "交易", "level": 3, "style": {"color": "#FFFFFF", "fillColor": "rgba(128, 176, 255, 1)", "lineColor": "rgba(47, 128, 237, 1)", "borderColor": "rgba(128, 176, 255, 1)", "borderWidth": 0, "borderRadius": 4}, "enabled": true, "nameTemplate": "【交易】{tradeName}"}, {"sort": 4, "type": "functionPoint", "typeName": "测试点名称", "level": 4, "style": {"color": "#FFFFFF", "fillColor": "rgba(128, 176, 255, 1)", "borderWidth": 0, "borderRadius": 4, "borderyjColor": "#FFFFFF"}, "enabled": true}, {"sort": 5, "type": "involveAccount", "isAttr": true, "typeName": "是否涉账", "level": 5, "style": {"color": "#000000", "fillColor": "rgba(227, 233, 255, 1)", "borderColor": "#FCDC00", "borderWidth": 0, "borderRadius": 4}, "enabled": true, "dataField": "involveAccount"}, {"sort": 6, "type": "involveBatch", "typeName": "是否批量", "isAttr": true, "level": 5, "style": {"color": "#000000", "fillColor": "rgba(191, 244, 204, 1)", "borderWidth": 0, "borderRadius": 4}, "enabled": true, "dataField": "involveBatch"}, {"sort": 7, "type": "priority", "isAttr": true, "level": 5, "style": {"color": "#000000", "fillColor": "rgba(253, 179, 180, 1)", "borderWidth": 0, "borderRadius": 4}, "enabled": true, "dataField": "priority", "nameTemplate": "【优先级】"}, {"sort": 8, "type": "ruleType", "isAttr": true, "level": 5, "style": {"color": "#000000", "fillColor": "rgba(252, 216, 245, 1)", "borderWidth": 0, "borderRadius": 4}, "enabled": true, "dataField": "ruleType", "nameTemplate": "【测试点类型】"}, {"sort": 9, "type": "testPoints", "typeName": "测试点细分", "isAttr": true, "level": 5, "style": {"color": "#000000", "fillColor": "rgba(255, 242, 192, 1)", "borderWidth": 0, "borderRadius": 4}, "enabled": true, "dataField": "testPoints"}, {"sort": 10, "type": "caseCount", "typeName": "关联用例", "isAttr": true, "level": 5, "style": {"color": "#000000", "fillColor": "rgba(255, 227, 191, 1)", "borderWidth": 0, "borderRadius": 4}, "enabled": true, "dataField": "caseCount"}]}