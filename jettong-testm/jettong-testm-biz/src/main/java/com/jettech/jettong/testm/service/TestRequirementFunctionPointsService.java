package com.jettech.jettong.testm.service;

import com.jettech.basic.base.service.SuperService;
import com.jettech.basic.fileimport.service.FileProcessor;
import com.jettech.basic.model.LoadService;
import com.jettech.jettong.testm.dto.FunctionPointsImportTaskSaveDTO;
import com.jettech.jettong.testm.entity.TestRequirementFunctionPoints;
import com.jettech.jettong.testm.dto.TestRequirementFunctionPointsPageQuery;
import com.jettech.jettong.testm.vo.TestPointsTreeVO;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.List;

/**
 * 测试分析功能要点表业务接口
 * <AUTHOR>
 * @version 1.0
 * @description 测试分析功能要点表业务接口
 * @projectName jettong
 * @package com.jettech.jettong.testm.service
 * @className TestRequirementFunctionPointsService
 * @date 2025-07-24
  * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface TestRequirementFunctionPointsService extends SuperService<TestRequirementFunctionPoints>, FileProcessor<FunctionPointsImportTaskSaveDTO>, LoadService
{
    /**
     * 根据项目ID和产品模块功能ID查询功能ID列表
     * @param projectId 项目ID
     * @param productModuleFunctionId 产品模块功能ID，为0时查询项目关联的所有功能ID
     * @return 功能ID列表
     */
    List<Long> getFunctionIdsByProjectAndModule(Long projectId, Long productModuleFunctionId);

    /**
     * 产品模块功能ID查询功能ID列表
     * @param
     * @param productModuleFunctionId 产品模块功能ID，为0时查询项目关联的所有功能ID
     * @return 功能ID列表
     */
    List<Long> getFunctionIdsByFunctionId(Long productModuleFunctionId);
    /**
     * 对功能点+1
     * @param pointId 功能点id
     * @return
     */
    void addCaseCount(Long pointId);
    /**
     * 对功能点-1
     * @param pointId 功能点id
     * @return
     */
    void redCaseCount(Long pointId);

    /**
     * 生成测试要点脑图树形结构
     * @param projectId 项目ID
     * @param reqId 需求ID
     * @return 脑图数据，
     *
     */
    TestPointsTreeVO generateTestPointsTree(Long projectId, Long reqId,Long taskId);


    /**
     * 下载导入模板
     * @param projectId
     * @param taskId 必填，导入必须基于任务
     * @return
     * @throws
     * <AUTHOR>
     * @date 2025/9/6 16:43
     * @update wzj 2025/9/7 16:43
     * @since 1.0
     */
    void template(Long projectId, Long taskId);

    /**
     * 导出测试分析数据
     *
     * @param query
     * @param isWHL
     * @return {@link Workbook}
     * @throws
     * <AUTHOR>
     * @date 2025/9/6 16:43
     * @update wzj 2025/9/7 16:43
     * @since 1.0
     */
    void export(TestRequirementFunctionPointsPageQuery query, Boolean isWHL);

}
