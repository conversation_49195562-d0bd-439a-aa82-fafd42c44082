package com.jettech.jettong.testm.vo;


import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong
 * @package com.jettech.jettong.testm.vo
 * @className PointsStyle
 * @date 2025/8/5 15:29
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class PointsDataVo
{
    private String type;
    private String typeName;
    private String text;
    private final Boolean richText= false;
    //expand: true,
    private final Boolean expand = true;
    //sActive: false, // 是否是激活状态
    private final Boolean isActive = false;
    // imageSize: { // 图片的尺寸
    //      width: 100, // 图片的宽度，必传
    //      height: 100, // 图片的高度，必传
    //      custom: false // 如果设为true，图片的显示大小不受主题控制，以imageSize.width和imageSize.height为准
    //    },
    private final String imageSize = "{width: 100, height: 100, custom: false}";

    private String borderColor;

    private String fillColor;

    private Integer borderRadius;

    private Integer borderWidth;

    private String color;

    private String lineColor;



}
