package com.jettech.jettong.testm.dao;

import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.testm.entity.TestRequirementFunctionPoints;

import org.springframework.stereotype.Repository;

/**
 * 测试分析功能要点表Mapper 接口
 * <AUTHOR>
 * @version 1.0
 * @description 测试分析功能要点表Mapper 接口
 * @projectName jettong
 * @package com.jettech.jettong.testm.dao
 * @className TestRequirementFunctionPointsMapper
 * @date 2025-07-24
  * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface TestRequirementFunctionPointsMapper extends SuperMapper<TestRequirementFunctionPoints>
{

}
