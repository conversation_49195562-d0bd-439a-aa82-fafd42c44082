package com.jettech.jettong.testm.service;

import com.jettech.basic.base.service.SuperService;
import com.jettech.basic.model.LoadService;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.basic.fileimport.service.FileProcessor;
import com.jettech.jettong.common.enumeration.ProductCaseRevisionOperationTypeEnum;
import com.jettech.jettong.testm.dto.TestProductCaseDTO;
import com.jettech.jettong.testm.dto.TestProductCaseImportTaskSaveDTO;
import com.jettech.jettong.testm.dto.TestProductCaseUpdateDTO;
import com.jettech.jettong.testm.entity.TestProductCase;
import com.jettech.jettong.testm.vo.TestCaseLibraryAndCaseNumber;
import com.jettech.jettong.testm.vo.TestProductCaseNumberOfTreeVo;
import com.jettech.jettong.testm.vo.TestSummaryComponent;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 业务接口
 * <AUTHOR>
 * @version 1.0
 * @description 业务接口
 * @projectName jettong
 * @package com.jettech.jettong.testm.service
 * @className TestProductCaseService
 * @date 2022-03-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface TestProductCaseService extends SuperService<TestProductCase> , LoadService, FileProcessor<TestProductCaseImportTaskSaveDTO>
{
    void saveTestCase(TestProductCase testCase);

    TestProductCase copyCaseById(Long item, Long treeId);


    TestSummaryComponent dataFormat(Map<Integer, Long> map ,Integer size);

    void saveTestProductCaseList(List<TestProductCase> caseList);

    List<File> selectFilesByCaseIds(List<Long> caseIdList);

    void saveFileList(List<File> fileList);

    void setOverView(TestProductCase testProductCase);

    void saveOverView(TestProductCase testProductCase);

    void deleteOverView(TestProductCase testProductCase);

    void saveAllOverView(List<TestProductCase> taskList);

    void saveAllTestProductCase(List<TestProductCase> testProductCaseList);

    void removeAllTestProductCase(List<TestProductCase> testProductCaseList);

    List<TestProductCaseNumberOfTreeVo> queryCaseNumberOfTree(Long libraryId);

    List<TestProductCaseNumberOfTreeVo> getNumberOfTree(Long libraryId,List<Long> treeIds);

    List<TestProductCaseNumberOfTreeVo> getQueryCaseNumberOfTree(TestProductCase testProductCase,List<Long> ids);

    List<TestProductCaseNumberOfTreeVo> queryCaseNumberOfDraftTree(Long libraryId);

    void deleteAllOverView(List<TestProductCase> testProductCaseList);

    void updateBatchByLibraryId(TestProductCaseUpdateDTO model, Long libraryId);


    List<TestCaseLibraryAndCaseNumber> getCaseNumberOflibrary(List<String> libraryIds);

    List<TestProductCase> queryProductCaseByIds(List<Long> testCaseIds);

    String getProductCaseKey(String projectCode);

    void updateBatch(TestProductCaseDTO testProductCaseS);

    /**
     * 引用用例库用例
     * @param testCaseIds 用例库用例ID
     * @param taskId 任务ID
     * @return 引用后的用例
     */
    List<TestProductCase> referenceFromTestCase(List<Long> testCaseIds, Long taskId);

    /**
     * 从修订版本还原用例
     * @param revisionId 修订记录ID
     * @param taskId 任务ID，如果传入则还原到test_product_case表，如果为null则还原到test_product_case_registry表
     */
    void restoreFromRevision(Long revisionId, Long taskId);

    String getMaxCaseKey(String sysCode);

    /**
     * 下载测试过程用例导入模板
     * @param taskId 任务ID
     * @return Workbook
     */
    Workbook downloadTestProductCaseImportTemplate(Long taskId);

    /**
     * 导出测试过程用例
     *
     * @param taskId 任务ID
     * @return Workbook
     */
    Workbook exportTestProductCases(Long taskId);

    /**
     * 构建Excel导出工作簿
     * @param data 导出数据
     * @return Workbook
     */
    Workbook buildExcelExportWorkbook(List<TestProductCase> data);

    void createRevision(TestProductCase testCase, int versionNo, ProductCaseRevisionOperationTypeEnum operationType, String desc);
    int getNextVersionNo(TestProductCase testCase);
}
