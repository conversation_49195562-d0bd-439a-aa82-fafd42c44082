package com.jettech.jettong.testm.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.jettong.product.api.ProductInfoApi;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.testm.dao.TestProductCaseRegistryMapper;
import com.jettech.jettong.testm.dto.TestProductCasePageQuery;
import com.jettech.jettong.testm.entity.TestProductCaseRegistry;
import com.jettech.jettong.testm.poi.dict.TestProductCaseDictHandler;
import com.jettech.jettong.testm.service.TestProductCaseRegistryService;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class TestProductCaseRegistryServiceImpl extends SuperServiceImpl<TestProductCaseRegistryMapper, TestProductCaseRegistry> implements TestProductCaseRegistryService {

    private final TestProductCaseDictHandler testProductCaseDictHandler;
    private final ProductInfoApi productInfoApi;

    @Override
    public void saveOrUpdateByCaseKey(TestProductCaseRegistry registry) {
        TestProductCaseRegistry existing = getOne(Wraps.<TestProductCaseRegistry>lbQ().eq(TestProductCaseRegistry::getCaseKey, registry.getCaseKey()), false);
        if (existing != null) {
            registry.setId(existing.getId());
            updateById(registry);
        } else {
            save(registry);
        }
    }



    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        List<TestProductCaseRegistry> list = baseMapper.selectBatchIds(ids);
        return list.stream().collect(Collectors.toMap(item -> (Serializable) item.getId(), item -> (Object) item));
    }

    @Override
    public Map<String, Object> export(TestProductCasePageQuery query) {
        LbqWrapper<TestProductCaseRegistry> wrapper = Wraps.lbQ();
        wrapper.like(TestProductCaseRegistry::getName, query.getName())
                .eq(TestProductCaseRegistry::getLeadingBy, query.getLeadingBy())
                .eq(TestProductCaseRegistry::getLibraryId, query.getLibraryId())
                .eq(TestProductCaseRegistry::getPriority, query.getPriority())
                .like(TestProductCaseRegistry::getCaseKey, query.getCaseKey())
                .eq(TestProductCaseRegistry::getState, query.getState())
                .eq(TestProductCaseRegistry::getDraft, query.getDraft())
                .eq(TestProductCaseRegistry::getTaskId, query.getTaskId())
                .eq(TestProductCaseRegistry::getRequirementId, query.getRequirementId())
                .eq(TestProductCaseRegistry::getProjectId, query.getProjectId())
                .eq(TestProductCaseRegistry::getTestreqId, query.getTestreqId());
        if (query.getFunctionIds() != null && !query.getFunctionIds().isEmpty()) {
            wrapper.in(TestProductCaseRegistry::getModuleFunctionId, query.getFunctionIds());
        }
        List<TestProductCaseRegistry> list = list(wrapper);

        String sheetName = "系统用例";
        String fileName = "系统用例.xlsx";
        if (query.getProductId() != null) {
            list.forEach(item -> { item.setProductId(query.getProductId()); });
            ProductInfo productInfo = productInfoApi.selectProductInfoById(query.getProductId());
            if (productInfo != null) {
                sheetName = productInfo.getName();
                fileName = productInfo.getName() + "-系统用例.xlsx";
            }
        }

        ExportParams exportParams = new ExportParams(sheetName, sheetName);
        exportParams.setDictHandler(testProductCaseDictHandler);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, TestProductCaseRegistry.class, list);

        Map<String, Object> result = new HashMap<>();
        result.put("workbook", workbook);
        result.put("fileName", fileName);
        return result;
    }
}
