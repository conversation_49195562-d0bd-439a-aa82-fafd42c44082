package com.jettech.jettong.testm.poi.dict;

import cn.hutool.core.collection.CollUtil;
import com.jettech.jettong.common.util.poi.dict.AbstractSingleFieldDictHandler;
import com.jettech.jettong.product.api.ProductModuleFunctionApi;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 产品模块功能字段字典处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品模块功能字段字典处理器，处理模块功能相关字段的字典转换
 * @projectName jettong-tm
 * @package com.jettech.jettong.testm.poi.dict
 * @className ModuleFunctionFieldDictHandler
 * @date 2025-09-16
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component("tmModuleFunctionFieldDictHandler")
@RequiredArgsConstructor
public class ModuleFunctionFieldDictHandler extends AbstractSingleFieldDictHandler {

    private static final Set<String> dictList = CollUtil.newHashSet("moduleFunctionId","functionId");

    private final ProductModuleFunctionApi productModuleFunctionApi;

    @Override
    public Supplier<Map<String, String>> dataSupplier() {
        return () -> productModuleFunctionApi.findProductModuleAll().stream()
                .filter(x -> x.getProductModuleFunction().getNodeType() == 2)
                .collect(Collectors.toMap(
                        module -> String.valueOf(module.getProductModuleFunction().getId()),
                        module -> String.format("%s-%s",
                                module.getProductName(),
                                module.getProductModuleFunction().getName())
                ));
    }

    @Override
    protected Set<String> getDictList() {
        return dictList;
    }
}
