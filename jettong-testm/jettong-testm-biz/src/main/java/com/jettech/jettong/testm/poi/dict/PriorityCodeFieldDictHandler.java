package com.jettech.jettong.testm.poi.dict;

import cn.hutool.core.collection.CollUtil;
import com.jettech.jettong.common.util.poi.dict.AbstractSingleFieldDictHandler;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;

/**
 * 用例优先级字典翻译
 * 历史原因，用例优先级并没有加入字典，先硬编码.....
 * @param
 * @return {@link null}
 * @throws
 * <AUTHOR>
 * @date 2025/9/17 07:25
 * @update wzj 2025/9/17 07:25
 * @since 1.0
 */
@Component("tmPriorityCodeFieldDictHandler")
public class PriorityCodeFieldDictHandler extends AbstractSingleFieldDictHandler {

    private static final Set<String> dictList = CollUtil.newHashSet("casePriorityCode", "priority");

    @Override
    public Supplier<Map<String, String>> dataSupplier() {
        Map <String, String> map = new HashMap<>();
        // 1 最低 ，2 较低 3 普通  4 较高  5 最高
        map.put("1","最低");
        map.put("2","较低");
        map.put("3","普通");
        map.put("4","较高");
        map.put("5","最高");
        return () -> map;
    }

    @Override
    protected Set<String> getDictList() {
        return dictList;
    }

}
