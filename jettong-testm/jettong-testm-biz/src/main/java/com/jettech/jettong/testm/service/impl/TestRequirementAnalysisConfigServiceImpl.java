package com.jettech.jettong.testm.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.jettong.testm.dao.TestRequirementAnalysisConfigMapper;
import com.jettech.jettong.testm.entity.TestRequirementAnalysisConfig;
import com.jettech.jettong.testm.service.TestRequirementAnalysisConfigService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 测试分析配置表业务层
 * <AUTHOR>
 * @version 1.0
 * @description 测试分析配置表业务层
 * @projectName jettong
 * @package com.jettech.jettong.testm.service.impl
 * @className TestRequirementAnalysisConfigServiceImpl
 * @date 2025-07-24
  * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class TestRequirementAnalysisConfigServiceImpl extends SuperServiceImpl<TestRequirementAnalysisConfigMapper, TestRequirementAnalysisConfig>
        implements TestRequirementAnalysisConfigService
{
}
