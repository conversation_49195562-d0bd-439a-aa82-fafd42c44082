package com.jettech.jettong.testm.poi.dict;

import com.jettech.jettong.common.util.poi.dict.SingleFieldDictHandler;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * TestCase 导入导出处理类
 *
 * <AUTHOR>
 * @version 1.0
 * @description TestCase 导入导出字典处理类
 * @projectName jettong-tm
 * @package com.jettech.jettong.testm.poi.testcase
 * @className TestCaseDictHandler
 * @date 2025-09-16
 */
@Service
public class TestCaseDictHandler extends TmCompositeDictHandler {

    public TestCaseDictHandler(List<SingleFieldDictHandler> handlerList) {
        super(handlerList);
    }
}
