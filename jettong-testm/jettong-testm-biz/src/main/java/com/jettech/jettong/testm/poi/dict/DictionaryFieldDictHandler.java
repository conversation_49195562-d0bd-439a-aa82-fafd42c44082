package com.jettech.jettong.testm.poi.dict;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.jettech.jettong.base.api.DictionaryApi;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.common.util.poi.dict.AbstractSingleFieldDictHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 字典字段处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 字典字段处理器，处理基于数据字典的字段转换
 * @projectName jettong-tm
 * @package com.jettech.jettong.testm.poi.dict
 * @className DictionaryFieldDictHandler
 * @date 2025-09-16
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component("tmDictionaryFieldDictHandler")
@RequiredArgsConstructor
public class DictionaryFieldDictHandler extends AbstractSingleFieldDictHandler {

    private static final Set<String> dictList = CollUtil.newHashSet(
             "caseLevel", "caseType", "testMode"
    );

    private final DictionaryApi dictionaryApi;

    private String dictVar;

    @Override
    public boolean enable(String dict) {
        dictVar = dict;
        return getDictList().contains(dict);
    }

    @Override
    public Supplier<Map<String, String>> dataSupplier() {

        // 将字段名转换为对应的字典类型
        String type = getDictionaryType(dictVar);

        if (StrUtil.isBlank(type)) {
            return Collections::emptyMap;
        }
        return () -> {
            try {
                List<Dictionary> dictionaries = dictionaryApi.query(Dictionary.builder().type(type).build());
                Map<String, String> result = dictionaries.stream()
                        .collect(Collectors.toMap(
                                item -> String.valueOf(item.getId()),
                                Dictionary::getName)
                        );
                // 添加调试日志
                return result;
            } catch (Exception e) {
                return Collections.emptyMap();
            }
        };
    }

    @Override
    protected Set<String> getDictList() {
        return dictList;
    }

    @Override
    public Map<String, String[]> dropMap(Long projectId) {
        Map<String, String[]> result = new HashMap<>();

        // 为每个支持的字段生成下拉数据
        for (String field : getDictList()) {
            try {
                // 设置当前处理的字段
                dictVar = field;

                // 获取该字段的下拉数据
                String[] values = getBiMap().values().toArray(new String[0]);
                result.put(field, values);

            } catch (Exception e) {
                result.put(field, new String[0]);
            }
        }

        return result;
    }

    /**
     * 根据字段名获取对应的字典类型
     */
    private String getDictionaryType(String fieldName) {
        if (fieldName == null) {
            return null;
        }

        switch (fieldName) {
            case "caseLevel":
                return "CASE_LEVEL";
            case "caseType":
                return "CASE_TYPE";
            case "testMode":
                return "TEST_MODE";
            default:
                return null;
        }
    }
}
