package com.jettech.jettong.testm.poi.dict;

/**
 * 字典处理器测试类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试新的字典处理器是否正常工作
 * @projectName jettong-tm
 * @package com.jettech.jettong.testm.poi.dict
 * @className DictHandlerTest
 * @date 2025-09-16
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public class DictHandlerTest {

    public void testImportResultDTO() {
        // 测试导入结果DTO的创建
        System.out.println("测试导入结果DTO");

        // 测试成功结果
        com.jettech.jettong.testm.dto.TestCaseImportResultDTO successResult =
                com.jettech.jettong.testm.dto.TestCaseImportResultDTO.success(5, 3);
        System.out.println("成功结果: " + successResult.getSuccessMessage());
        System.out.println("新增: " + successResult.getInsertCount() + ", 更新: " + successResult.getUpdateCount());

        // 测试失败结果
        java.util.List<com.jettech.jettong.testm.dto.TestCaseImportResultDTO.ImportErrorDetail> errors =
                new java.util.ArrayList<>();
        errors.add(com.jettech.jettong.testm.dto.TestCaseImportResultDTO.ImportErrorDetail.builder()
                .rowNumber(3)
                .caseKey("TC-001")
                .errorType("用例不存在")
                .errorMessage("用例编号不存在")
                .build());

        com.jettech.jettong.testm.dto.TestCaseImportResultDTO failureResult =
                com.jettech.jettong.testm.dto.TestCaseImportResultDTO.failure("导入失败", errors);
        System.out.println("失败结果: " + failureResult.getErrorMessage());
        System.out.println("错误数量: " + failureResult.getErrorCount());
    }

    public void testDictHandlerCompilation() {
        // 这是一个编译测试，确保所有字典处理器类都能正确编译
        System.out.println("字典处理器编译测试通过");

        // 测试类的实例化
        TestCaseDictHandler testCaseDictHandler = new TestCaseDictHandler(java.util.Collections.emptyList());
        TestProductCaseDictHandler testProductCaseDictHandler = new TestProductCaseDictHandler(java.util.Collections.emptyList());

        System.out.println("TestCaseDictHandler: " + testCaseDictHandler.getClass().getName());
        System.out.println("TestProductCaseDictHandler: " + testProductCaseDictHandler.getClass().getName());

        // 测试dropMap方法
        try {
            java.util.Map<String, String[]> dropMap = testCaseDictHandler.dropMap(null);
            System.out.println("TestCaseDictHandler dropMap方法调用成功，返回字段数量: " + (dropMap != null ? dropMap.size() : 0));

            java.util.Map<String, String[]> productCaseDropMap = testProductCaseDictHandler.dropMap(null);
            System.out.println("TestProductCaseDictHandler dropMap方法调用成功，返回字段数量: " + (productCaseDropMap != null ? productCaseDropMap.size() : 0));
        } catch (Exception e) {
            System.err.println("dropMap方法测试失败: " + e.getMessage());
        } finally {
            // 清理字典处理器缓存
            testCaseDictHandler.clear();
            testProductCaseDictHandler.clear();
        }
    }
}
