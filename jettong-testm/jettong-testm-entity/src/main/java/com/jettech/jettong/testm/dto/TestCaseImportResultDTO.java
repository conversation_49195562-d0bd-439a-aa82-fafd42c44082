package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 测试用例导入结果统计
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试用例导入结果统计
 * @projectName jettong-tm
 * @package com.jettech.jettong.testm.dto
 * @className TestCaseImportResultDTO
 * @date 2025-09-17
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("测试用例导入结果统计")
public class TestCaseImportResultDTO {

    @ApiModelProperty("是否成功")
    private boolean success;

    @ApiModelProperty("总处理数量")
    private int totalCount;

    @ApiModelProperty("新增数量")
    private int insertCount;

    @ApiModelProperty("更新数量")
    private int updateCount;

    @ApiModelProperty("错误数量")
    private int errorCount;

    @ApiModelProperty("错误详情列表")
    @Builder.Default
    private List<ImportErrorDetail> errorDetails = new ArrayList<>();

    @ApiModelProperty("成功消息")
    private String successMessage;

    @ApiModelProperty("错误消息")
    private String errorMessage;

    /**
     * 导入错误详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ApiModel("导入错误详情")
    public static class ImportErrorDetail {
        
        @ApiModelProperty("行号")
        private int rowNumber;
        
        @ApiModelProperty("用例编号")
        private String caseKey;
        
        @ApiModelProperty("错误类型")
        private String errorType;
        
        @ApiModelProperty("错误消息")
        private String errorMessage;
    }

    /**
     * 创建成功结果
     */
    public static TestCaseImportResultDTO success(int insertCount, int updateCount) {
        return TestCaseImportResultDTO.builder()
                .success(true)
                .totalCount(insertCount + updateCount)
                .insertCount(insertCount)
                .updateCount(updateCount)
                .errorCount(0)
                .successMessage(String.format("导入成功！新增 %d 条，更新 %d 条，共处理 %d 条数据", 
                        insertCount, updateCount, insertCount + updateCount))
                .build();
    }

    /**
     * 创建失败结果
     */
    public static TestCaseImportResultDTO failure(String errorMessage, List<ImportErrorDetail> errorDetails) {
        return TestCaseImportResultDTO.builder()
                .success(false)
                .totalCount(0)
                .insertCount(0)
                .updateCount(0)
                .errorCount(errorDetails != null ? errorDetails.size() : 0)
                .errorDetails(errorDetails != null ? errorDetails : new ArrayList<>())
                .errorMessage(errorMessage)
                .build();
    }

    /**
     * 创建部分成功结果
     */
    public static TestCaseImportResultDTO partialSuccess(int insertCount, int updateCount, 
                                                        List<ImportErrorDetail> errorDetails) {
        int errorCount = errorDetails != null ? errorDetails.size() : 0;
        return TestCaseImportResultDTO.builder()
                .success(errorCount == 0)
                .totalCount(insertCount + updateCount + errorCount)
                .insertCount(insertCount)
                .updateCount(updateCount)
                .errorCount(errorCount)
                .errorDetails(errorDetails != null ? errorDetails : new ArrayList<>())
                .successMessage(String.format("处理完成！新增 %d 条，更新 %d 条，错误 %d 条", 
                        insertCount, updateCount, errorCount))
                .errorMessage(errorCount > 0 ? "部分数据导入失败，请查看错误详情" : null)
                .build();
    }
}
