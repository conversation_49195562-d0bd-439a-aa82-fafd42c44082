package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.constant.DictionaryType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import static com.jettech.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;
import com.jettech.basic.model.EchoVO;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.*;


/**
 * 测试任务用例表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试任务用例表实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestTaskCase
 * @date 2025-08-12
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("test_task_case")
@ApiModel(value = "TestTaskCase", description = "测试任务用例表")
@AllArgsConstructor
public class TestTaskCase extends TestmCustomFormFieldExtend implements EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();


    /**
     * 冗余字段:名称/标题
     */
    @ApiModelProperty(value = "冗余字段:名称/标题")
    @Size(max = 500, message = "冗余字段:名称/标题长度不能超过500")
    @TableField(value = "`name`", condition = LIKE)
    @Excel(name = "案例名称",width = 20)
    private String name;


    /**
     * 冗余字段:英文标识
     */
    @ApiModelProperty(value = "冗余字段:英文标识")
    @Size(max = 20, message = "冗余字段:英文标识长度不能超过20")
    @TableField(value = "`case_key`", condition = LIKE)
    @Excel(name = "案例编号",width = 20)
    private String caseKey;

    /**
     *用例类型
     */
    @ApiModelProperty(value = "用例类型")
    @TableField(value = "case_type")
    @Excel(name = "案例类型",dict = "caseType",width = 20)
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.CASE_TYPE)
    private String caseType;



    /**
     * 前提条件
     */
    @ApiModelProperty(value = "前提条件")
    @Size(max = 65535, message = "前提条件长度不能超过65535")
    @TableField(value = "prerequisite", condition = LIKE)
    @Excel(name = "前提条件",width = 20)
    private String prerequisite;

    /**
     * 正反例
     */
    @ApiModelProperty(value = "正反例")
    @TableField(value = "is_examples")
    @Excel(name = "正反例",dict = "isExamples",width = 20)
    private Boolean isExamples;


    /**
     * 冗余字段:优先级
     */
    @ApiModelProperty(value = "冗余字段:优先级")
    @TableField(value = "`priority`")
    @Excel(name = "优先级",dict = "priority",width = 20)
    private Integer priority;





    /**
     * 测试步骤类型，文本/条目
     */
    @ApiModelProperty(value = "测试步骤类型，文本/条目")
    @Size(max = 20, message = "测试步骤类型，文本/条目长度不能超过20")
    @TableField(value = "step_type", condition = LIKE)
    @Excel(name = "测试步骤类型，文本/条目",dict = "stepType",width = 25)
    private String stepType;

    /**
     * 测试步骤
     */
    @ApiModelProperty(value = "测试步骤")
    @Size(max = 65535, message = "测试步骤长度不能超过65535")
    @TableField(value = "test_step", condition = LIKE)
    @Excel(name = "测试步骤",width = 30)
    private String testStep;

    /**
     * 预期结果
     */
    @ApiModelProperty(value = "预期结果")
    @Size(max = 65535, message = "预期结果长度不能超过65535")
    @TableField(value = "expected_result", condition = LIKE)
    @Excel(name = "预期结果",width = 20)
    private String expectedResult;

    /**
     * 预期结果
     */
    @ApiModelProperty(value = "单条执行结果")
    @TableField(exist = false)
    @Excel(name = "单条步骤执行结果",dict = "singleExecResult",width = 18)
    private String singleExecResult;

    /**
     * 测试意图
     */
    @ApiModelProperty(value = "测试意图")
    @TableField(value = "intent", condition = LIKE)
    @Excel(name = "测试意图",width = 20)
    private String intent;

    /**
     *测试方式
     */
    @ApiModelProperty(value = "测试方式")
    @TableField(exist = false)
    @Excel(name = "测试方式",dict = "testMode",width = 20)
    private String testMode;


    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    @TableField(value = "product_id")
    @Excel(name = "所属系统", dict = "productId", width = 20)
    private Long productId;

    /**
     * 关联交易
     */
    @ApiModelProperty(value = "关联交易")
    @TableField(value = "module_function_id")
    @Excel(name = "所属交易", dict = "moduleFunctionId" ,width = 20)
    private Long moduleFunctionId;

    /**
     * 任务标识
     */
    @ApiModelProperty(value = "任务标识")
    @NotNull(message = "请填写任务标识")
    @TableField(value = "`task_id`")
    private Long taskId;

    /**
     * 用例标识
     */
    @ApiModelProperty(value = "用例标识")
    @NotNull(message = "请填写用例标识")
    @TableField(value = "`testcase_id`")
    private Long testcaseId;


    /**
     * 冗余字段:执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过
     */
    @ApiModelProperty(value = "冗余字段:执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过")
    @Size(max = 20, message = "冗余字段:执行结果状态:system成功/smoke失败/Blocking阻塞/Skip跳过长度不能超过20")
    @TableField(value = "`status`", condition = LIKE)
    @Excel(name = "最终执行结果",dict = "status",width = 10)
    private String status;


    /**
     * 冗余字段:版本号
     */
    @ApiModelProperty(value = "冗余字段:版本号")
    @Size(max = 20, message = "冗余字段:版本号长度不能超过20")
    @TableField(value = "`version`", condition = LIKE)
    private String version;




    /**
     * 关联测试点
     */
    @ApiModelProperty(value = "关联测试点")
    @TableField(value = "function_points_id")
//    @Excel(name = "关联测试点", dict = "functionPointsId", width = 30)
    @Echo(api = REQUIREMENT_FUNCTION_POINTS_CLASS, beanClass = TestRequirementFunctionPoints.class)
    private Long functionPointsId;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "leading_by",updateStrategy = FieldStrategy.IGNORED)
//    @Excel(name = "负责人", dict = "leadingBy", width = 10)
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long leadingBy;

    /**
     * 冗余字段:执行人
     */
    @ApiModelProperty(value = "冗余字段:执行人")
    @TableField(value = "`exec_by`")
    @Excel(name = "实际执行人",dict = "execBy",width = 10)
    private Long execBy;

    /**
     * 冗余字段:用例树id
     */
    @ApiModelProperty(value = "冗余字段:关联测试计划用例树id")
    @TableField(value = "`tree_id`")
    private Long treeId;

    /**
     * 冗余字段:执行结束时间
     */
    @ApiModelProperty(value = "冗余字段:执行结束时间")
    @TableField(value = "`exec_etime`")
    @Excel(name = "最新执行时间", format = DEFAULT_DATE_TIME_FORMAT, width = 20)
    private LocalDateTime execEtime;

    /**
     *案例级别
     */
    @ApiModelProperty(value = "案例级别")
    @TableField(value = "case_level")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.CASE_LEVEL)
    private String caseLevel;


    /**
     * 关联测试需求
     */
    @ApiModelProperty(value = "关联测试需求")
    @TableField(value = "testreq_id")
    private Long testreqId;

    /**
     * 归属项目
     */
    @ApiModelProperty(value = "归属项目")
    @TableField(value = "project_id")
    private Long projectId;



    @ApiModelProperty(value = "预估执行时间")
    @TableField(value = "exec_time")
//    @Excel(name = "预估执行时间")
    private Long execTime;


    /**
     * 测试用例上传的附件文件
     */
    @ApiModelProperty(value = "用例执行附件文件")
    @TableField(exist = false)
    private List<File> files;

    /**
     * 测试用例上传的附件文件
     */
    @ApiModelProperty(value = "测试用例附件文件")
    @TableField(exist = false)
    private List<File> caseFiles;

    /**
     * 意图附件文件
     */
    @ApiModelProperty(value = "测试用例意图附件文件")
    @TableField(exist = false)
    private List<File> intentFiles;

    /**
     * 前置条件附件文件
     */
    @ApiModelProperty(value = "测试用例前置条件附件文件")
    @TableField(exist = false)
    private List<File> prerequisiteFiles;

    /**
     * 测试步骤附件文件
     */
    @ApiModelProperty(value = "测试用例测试步骤附件文件")
    @TableField(exist = false)
    private List<File> testStepFiles;

    @Builder
    public TestTaskCase(
                    Long id, Long taskId, Long testcaseId, Long createdBy, LocalDateTime createTime, 
                    LocalDateTime updateTime, Long updatedBy, Integer priority, String status, String name, String version, 
                    Long execBy, Long treeId, LocalDateTime execEtime, String caseKey, Long testreqId, Long projectId,
                    Long moduleFunctionId,String intent,String stepType,String testStep,String expectedResult,
                    Long leadingBy,String prerequisite,Long execTime,String caseLevel,String caseType,Boolean isExamples,
                    Long functionPointsId)
    {
        this.id = id;
        this.taskId = taskId;
        this.testcaseId = testcaseId;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.priority = priority;
        this.status = status;
        this.name = name;
        this.version = version;
        this.execBy = execBy;
        this.treeId = treeId;
        this.execEtime = execEtime;
        this.caseKey = caseKey;
        this.testreqId = testreqId;
        this.projectId = projectId;
        this.moduleFunctionId = moduleFunctionId;
        this.intent = intent;
        this.stepType = stepType;
        this.testStep = testStep;
        this.expectedResult = expectedResult;
        this.leadingBy = leadingBy;
        this.prerequisite = prerequisite;
        this.execTime = execTime;
        this.caseLevel = caseLevel;
        this.caseType = caseType;
        this.isExamples = isExamples;
        this.functionPointsId = functionPointsId;
    }

}
