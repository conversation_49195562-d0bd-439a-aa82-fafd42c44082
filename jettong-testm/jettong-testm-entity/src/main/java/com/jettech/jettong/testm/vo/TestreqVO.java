package com.jettech.jettong.testm.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.product.entity.ProductVersion;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jettech.jettong.common.constant.EchoConstants.*;

/**
 * 测试需求信息
 *
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong1
 * @package com.jettech.jettong.alm.issue.dto
 * @className aa
 * @date 2025/7/7 18:38
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "TestreqVo", description = "测试需求信息")
@AllArgsConstructor
public class TestreqVO extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 父需求ID
     */
    @ApiModelProperty(value = "父需求ID")
    @Echo(api = REQUIREMENT_ID_CLASS, beanClass = TestreqVO.class)
    private Long parentId;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String code;

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 状态code
     */
    @ApiModelProperty(value = "状态code")
    @Echo(api = STATE_CODE_CLASS, beanClass = StateVO.class)
    private String stateCode;

    /**
     * 优先级code
     */
    @ApiModelProperty(value = "优先级code")
    @Echo(api = PRIORITY_CODE_CLASS, beanClass = PriorityVO.class)
    private String priorityCode;

    /**
     * 事项类型code
     */
    @ApiModelProperty(value = "事项类型code")
    @Echo(api = TYPE_CODE_CLASS, beanClass = TypeVO.class)
    private String typeCode;

    /**
     * 是否延期
     */
    @ApiModelProperty(value = "是否延期")
    private Boolean delay;

    /**
     * 进度0-100
     */
    @ApiModelProperty(value = "进度0-100")
    private Integer rateProgress;

    /**
     * 产品版本id
     */
    @ApiModelProperty(value = "产品版本id")
    @Echo(api = PRODUCT_VERSION_FEIGN_CLASS, beanClass = ProductVersion.class)
    private Long productVersionId;

    /**
     * 产品功能模块id
     */
    @ApiModelProperty(value = "产品功能模块id")
    @Echo(api = PRODUCT_MODULE_FUNCTION_FEIGN_CLASS, beanClass = ProductModuleFunction.class)
    private Long productModuleFunctionId;

    /**
     * 规模
     */
    @ApiModelProperty(value = "规模")
    private Double estimatePoint;

    /**
     * 估算工时
     */
    @ApiModelProperty(value = "估算工时")
    private Double estimateHour;

    /**
     * 实际工时
     */
    @ApiModelProperty(value = "实际工时")
    private Double usedHour;

    /**
     * 归属项目集
     */
    @ApiModelProperty(value = "归属项目集")
    private Long programId;

    /**
     * 归属项目
     */
    @ApiModelProperty(value = "归属项目")
    @Echo(api = PROJECT_INFO_FEIGN_CLASS, beanClass = ProjectInfoVO.class)
    private Long projectId;

    /**
     * 归属计划
     */
    @ApiModelProperty(value = "归属计划")
    private Long planId;

    /**
     * 归属产品
     */
    @ApiModelProperty(value = "主办系统")
    @Echo(api = PRODUCT_ID_FEIGN_CLASS, beanClass = ProductInfo.class)
    private Long productId;

    /**
     * 提出人
     */
    @ApiModelProperty(value = "提出人")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long putBy;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long leadingBy;

    /**
     * 处理人
     */
    @ApiModelProperty(value = "处理人")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long handleBy;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private LocalDateTime planStime;

    /**
     * 计划完成时间
     */
    @ApiModelProperty(value = "计划完成时间")
    private LocalDateTime planEtime;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    /**
     * 完成时间
     */
    @ApiModelProperty(value = "完成时间")
    private LocalDateTime endTime;

    /**
     * 是否归档
     */
    @ApiModelProperty(value = "是否归档")
    private Boolean isFiled;

    /**
     * 需求来源code
     */
    @ApiModelProperty(value = "需求来源code")
    private String sourceCode;


    /**
     * 产品修复版本id
     */
    @ApiModelProperty(value = "产品修复版本id")
    @Echo(api = PRODUCT_VERSION_FEIGN_CLASS, beanClass = ProductVersion.class)
    private Long productRepairVersionId;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long projectManagerBy;

    /**
     * 测试经理
     */
    @ApiModelProperty(value = "测试经理")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long testManagerBy;

    @Builder
    public TestreqVO(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
            Long parentId, String code, String name, String description, String stateCode,
            String priorityCode, String typeCode, Boolean delay, Integer rateProgress, Long productVersionId,
            Double estimatePoint,
            Double estimateHour, Double usedHour, Long programId, Long projectId, Long productId, Long planId,
            Long putBy, Long leadingBy, Boolean isFiled,
            Long handleBy, LocalDateTime planStime, LocalDateTime planEtime, LocalDateTime startTime,
            LocalDateTime endTime, String sourceCode,Long projectManagerBy,Long testManagerBy,
            Long productModuleFunctionId, Long productRepairVersionId)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.parentId = parentId;
        this.code = code;
        this.name = name;
        this.description = description;
        this.stateCode = stateCode;
        this.priorityCode = priorityCode;
        this.typeCode = typeCode;
        this.delay = delay;
        this.rateProgress = rateProgress;
        this.productVersionId = productVersionId;
        this.estimatePoint = estimatePoint;
        this.estimateHour = estimateHour;
        this.usedHour = usedHour;
        this.programId = programId;
        this.projectId = projectId;
        this.productId = productId;
        this.planId = planId;
        this.putBy = putBy;
        this.leadingBy = leadingBy;
        this.handleBy = handleBy;
        this.planStime = planStime;
        this.planEtime = planEtime;
        this.startTime = startTime;
        this.endTime = endTime;
        this.sourceCode = sourceCode;
        this.isFiled = isFiled;
        this.productModuleFunctionId = productModuleFunctionId;
        this.productRepairVersionId = productRepairVersionId;
        this.projectManagerBy = projectManagerBy;
        this.testManagerBy = testManagerBy;
    }

}
