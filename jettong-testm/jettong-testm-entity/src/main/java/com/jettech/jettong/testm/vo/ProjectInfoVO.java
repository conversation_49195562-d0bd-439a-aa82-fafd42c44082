package com.jettech.jettong.testm.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.jettech.jettong.common.constant.BaseEchoConstants.ORG_ID_FEIGN_CLASS;
import static com.jettech.jettong.common.constant.BaseEchoConstants.USER_ID_FEIGN_CLASS;


/**
 * 项目信息实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 项目信息实体类
 * @projectName jettong
 * @package com.jettech.jettong.alm.project.entity
 * @className ProjectInfo
 * @date 2021-11-09
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProjectInfoVo", description = "项目信息")
@AllArgsConstructor
public class ProjectInfoVO extends Entity<Long> implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String code;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 项目类型code
     */
    @ApiModelProperty(value = "项目模式code，WELL_瀑布类型、AGILE_敏捷类型、TEST_测试类型")
    private String typeCode;

    /**
     * 项目类型  交付类/研发类  原项目类型改为项目模式
     */
    @ApiModelProperty(value = "项目类型")
    private String classify;

    /**
     * 机构ID
     */
    @ApiModelProperty(value = "机构ID")
    @Echo(api = ORG_ID_FEIGN_CLASS, beanClass = Org.class)
    private Long orgId;
    /**
     * 主板产品
     */
    @ApiModelProperty(value = "主办系统ID")
    private Long productId;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long leadingBy;

    /**
     * 项目集
     */
    @ApiModelProperty(value = "项目集")
    private Long programId;

    /**
     * 是否归档
     */
    @ApiModelProperty(value = "是否归档")
    private Boolean isFiled;

    /**
     * 是否结项
     */
    @ApiModelProperty(value = "是否结项")
    private Boolean isClosure;

    /**
     * 申请部门机构Id
     */
    @ApiModelProperty(value = "申请部门机构Id")
    private Long applyOrgId;

    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    private LocalDateTime planStartTime;

    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    private LocalDateTime planEndTine;

    /**
     * 项目阶段
     */
    @ApiModelProperty(value = "项目阶段")
    @TableField(value = "`project_stage`")
    @Excel(name = "项目阶段")
    private ProjectStageEnumVO projectStage;

    /**
     * 父项目ID
     */
    @ApiModelProperty(value = "父项目ID")
    private Long parentId;


    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    @ApiModelProperty(value = "项目预算")
    private BigDecimal budget;

    @ApiModelProperty(value = "测试方式")
    @TableField(exist = false)
    private List<String> testModes;
    /**
     * 测试方式
     */
    @ApiModelProperty(value = "测试方式")
    @TableField(value = "test_mode")
    private String testMode;

    @ApiModelProperty(value = "实施方案是否可裁剪 1是-项目模板可变动；0否-项目模板不可变动")
    private Boolean isCropping;

    @Builder
    public ProjectInfoVO(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
            String code, String name, String description, String typeCode,
            Long orgId, Long leadingBy, Long programId, Boolean isFiled, Boolean isClosure, //Long hostProductId,
            Long applyOrgId, LocalDateTime planStartTime, LocalDateTime planEndTine,
            ProjectStageEnumVO projectStage, Long parentId,
            String projectStatus,Boolean isCropping
    )
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.code = code;
        this.name = name;
        this.description = description;
        this.typeCode = typeCode;
        this.orgId = orgId;
        this.leadingBy = leadingBy;
        this.programId = programId;
        this.isFiled = isFiled;
        this.isClosure = isClosure;
        this.applyOrgId = applyOrgId;
        this.planStartTime = planStartTime;
        this.planEndTine = planEndTine;
        this.projectStage = projectStage;
        this.parentId = parentId;
        this.projectStatus = projectStatus;
        this.isCropping = isCropping;
    }

}
