package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 测试过程用例导入任务对象
 * <AUTHOR>
 * @date 2025-09-17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TestProductCaseImportTaskSaveDTO {
    
    @ApiModelProperty(value = "任务ID")
    @NotNull(message = "请填写任务ID")
    private Long taskId;

    @ApiModelProperty(value = "项目ID")
    @NotNull(message = "请填写项目ID")
    private Long projectId;

    @ApiModelProperty(value = "测试需求ID")
    private Long testreqId;
}
