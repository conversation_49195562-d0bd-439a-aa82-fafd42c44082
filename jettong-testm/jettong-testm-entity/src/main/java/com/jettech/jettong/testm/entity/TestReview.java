package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.testm.vo.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.EchoConstants.*;


/**
 * 评审管理表实体类
 * <AUTHOR>
 * @version 1.0
 * @description 评审管理表实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestReview
 * @date 2025-09-06
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("test_review")
@ApiModel(value = "TestReview", description = "评审管理表")
@AllArgsConstructor
public class TestReview extends TestmCustomFormFieldExtend implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 评审编码
     */
    @ApiModelProperty(value = "评审编码")
    @Size(max = 20, message = "评审编码长度不能超过20")
    @TableField(value = "`code`", condition = LIKE)
    private String code;

    /**
     * 评审标题
     */
    @ApiModelProperty(value = "评审标题")
    @NotEmpty(message = "请填写评审标题")
    @Size(max = 128, message = "评审标题长度不能超过128")
    @TableField(value = "`name`", condition = LIKE)
    private String name;

    /**
     * 事项类型code
     */
    @ApiModelProperty(value = "事项类型code")
    @NotEmpty(message = "请填写事项类型code")
    @Size(max = 20, message = "事项类型code长度不能超过20")
    @TableField(value = "`type_code`")
    @Echo(api = TYPE_CODE_FEIGN_CLASS, beanClass = TypeVO.class)
    @Excel(name = "事项类型code")
    private String typeCode;

    /**
     * 评审类型
     */
    @ApiModelProperty(value = "评审类型")
    @Size(max = 50, message = "评审类型长度不能超过50")
    @TableField(value = "`review_type`", condition = LIKE)
    @Echo(api = TYPE_CODE_FEIGN_CLASS, beanClass = TypeVO.class)
    private String reviewType;

    /**
     * 评审方式
     */
    @ApiModelProperty(value = "评审方式")
    @TableField(value = "`review_method`")
    private String reviewMethod;

    /**
     * 评审说明
     */
    @ApiModelProperty(value = "评审说明")
    @TableField(value = "`review_content`", condition = LIKE)
    private String reviewContent;

    /**
     * 状态code
     */
    @ApiModelProperty(value = "状态code")
    @Size(max = 20, message = "状态code长度不能超过20")
    @TableField(value = "`state_code`", condition = LIKE)
    @Echo(api = STATE_CODE_FEIGN_CLASS, beanClass = StateVO.class)
    private String stateCode;

    /**
     * 期望截止时间
     */
    @ApiModelProperty(value = "期望截止时间")
    @TableField(value = "`expected_deadline`")
    private LocalDateTime expectedDeadline;

    /**
     * 更新关联数据
     */
    @ApiModelProperty(value = "更新关联数据")
    @TableField(value = "`update_associated_data`")
    private Boolean updateAssociatedData;

    /**
     * 当前环节
     */
    @ApiModelProperty(value = "当前环节")
    @Size(max = 100, message = "当前环节长度不能超过100")
    @TableField(value = "`current_stage`", condition = LIKE)
    private String currentStage;

    /**
     * 处理人
     */
    @ApiModelProperty(value = "处理人")
    @TableField(value = "`handle_by`")
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long handleBy;

    @ApiModelProperty(value = "创建人")
    @TableField(value = "`created_by`", fill = FieldFill.INSERT)
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    private Long createdBy;

    /**
     * 关联项目
     */
    @ApiModelProperty(value = "关联项目")
    @TableField(value = "`project_id`")
    @Echo(api = PROJECT_INFO_FEIGN_CLASS, beanClass = ProjectInfoVO.class)
    private Long projectId;

    /**
     * 关联测试需求
     */
    @ApiModelProperty(value = "关联测试需求")
    @TableField(value = "`testreq_id`")
    @Echo(api = TESTREQ_ID_FEIGN_CLASS, beanClass = TestreqVO.class)
    private Long testreqId;

    @ApiModelProperty(value = "当前登录人能否办理")
    @Echo(api = WORKFLOW_FEIGN_CLASS, beanClass = WorkflowCountersignRecordVO.class)
    @TableField(exist = false)
    private String isProcess;


    @ApiModelProperty(value = "是否起始节点")
    @TableField(exist = false)
    private Boolean isStartNode;

    @ApiModelProperty(value = "是否上传评审报告")
    @TableField(exist = false)
    private Boolean isUploadReviewFile;

    @ApiModelProperty(value = "附件文件")
    @TableField(exist = false)
    private List<File> files;

    @ApiModelProperty(value = "关联评审数据")
    @TableField(exist = false)
    private List<Long> bizIds;

    @ApiModelProperty(value = "评审数据")
    @TableField(exist = false)
    private Object linkedData;

    @ApiModelProperty(value = "评审报告")
    @TableField(exist = false)
    private List<File> reviewFiles;


    @Builder
    public TestReview(
                    Long id, String code, String name, String reviewType, String reviewMethod,
                    String reviewContent, String stateCode, LocalDateTime expectedDeadline, Boolean updateAssociatedData, String currentStage, Long handleBy, 
                    Long projectId, Long testreqId, Long createdBy, LocalDateTime createTime, Long updatedBy, LocalDateTime updateTime)
    {
        this.id = id;
        this.code = code;
        this.name = name;
        this.reviewType = reviewType;
        this.reviewMethod = reviewMethod;
        this.reviewContent = reviewContent;
        this.stateCode = stateCode;
        this.expectedDeadline = expectedDeadline;
        this.updateAssociatedData = updateAssociatedData;
        this.currentStage = currentStage;
        this.handleBy = handleBy;
        this.projectId = projectId;
        this.testreqId = testreqId;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updatedBy = updatedBy;
        this.updateTime = updateTime;
    }

}
