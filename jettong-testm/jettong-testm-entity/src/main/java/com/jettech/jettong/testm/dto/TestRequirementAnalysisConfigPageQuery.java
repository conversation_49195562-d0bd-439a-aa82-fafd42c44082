package com.jettech.jettong.testm.dto;

import java.time.LocalDateTime;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 测试分析配置表分页实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试分析配置表分页实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dtoPageQuery
 * @className TestRequirementAnalysisConfig
 * @date 2025-07-24
  * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestRequirementAnalysisConfigPageQuery", description = "测试分析配置表")
public class TestRequirementAnalysisConfigPageQuery implements Serializable
{

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 标识
     */
    @ApiModelProperty(value = "标识")
    private String code;
    /**
     * 是否启用（同时只有一个配置启用）
     */
    @ApiModelProperty(value = "是否启用（同时只有一个配置启用）")
    private Boolean enable;
    /**
     * 字段配置
     */
    @ApiModelProperty(value = "字段配置")
    private String fieldConfig;
    /**
     * 脑图层级配置
     */
    @ApiModelProperty(value = "脑图层级配置")
    private String hierarchyConfig;
    /**
     * 视图配置（数据列展示）
     */
    @ApiModelProperty(value = "视图配置（数据列展示）")
    private String viewConfig;
    /**
     * 测试要点分割配置
     */
    @ApiModelProperty(value = "测试要点分割配置")
    private String testpointParsingConfig;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private Long updatedBy;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

}
