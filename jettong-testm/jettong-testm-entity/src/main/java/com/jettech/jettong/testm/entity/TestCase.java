package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.base.entity.Entity;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.common.enumeration.CaseSourceTypeEnum;
import com.jettech.jettong.common.enumeration.CaseStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;


/**
 * 测试案例表实体类
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例表实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestCase
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_case")
@ApiModel(value = "TestCase", description = "测试案例表")
@AllArgsConstructor
public class TestCase extends TestmCustomFormFieldExtend implements EchoVO
{

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();
    /**
     * 用例编号(全局唯一)
     */
    @ApiModelProperty(value = "用例编号(全局唯一)")
    @Size(max = 100, message = "用例编号长度不能超过100")
    @TableField(value = "case_key", condition = LIKE)
    @Excel(name = "用例编号", orderNum = "3")
    private String caseKey;

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    @TableField(value = "name", condition = LIKE)
    @Excel(name = "名称/标题", orderNum = "4")
    private String name;

    /**
     * 测试意图
     */
    @ApiModelProperty(value = "测试意图")
    @NotBlank(message = "测试意图不能为空")
    @Size(max = 255, message = "测试意图长度不能超过255")
    @TableField(value = "intent", condition = LIKE)
    @Excel(name = "测试意图", orderNum = "5")
    private String intent;

    /**
     * 测试步骤类型，文本/条目
     */
    @ApiModelProperty(value = "测试步骤类型，文本/条目")
    @Size(max = 20, message = "测试步骤类型，文本/条目长度不能超过20")
    @TableField(value = "step_type", condition = LIKE)
    @Excel(name = "步骤类型", orderNum = "6", replace = {"文本_text", "条目_subclause", "_null"}, addressList = true)
    private String stepType;

    /**
     * 测试步骤
     */
    @ApiModelProperty(value = "测试步骤")
    @NotBlank(message = "测试步骤不能为空")
    @Size(max = 65535, message = "测试步骤长度不能超过65535")
    @TableField(value = "test_step", condition = LIKE)
    @Excel(name = "测试步骤", orderNum = "7")
    private String testStep;

    /**
     * 预期结果
     */
    @ApiModelProperty(value = "预期结果")
    @NotBlank(message = "预期结果不能为空")
    @Size(max = 65535, message = "预期结果长度不能超过65535")
    @TableField(value = "expected_result", condition = LIKE)
    @Excel(name = "预期结果", orderNum = "8")
    private String expectedResult;

    /**
     * 评审状态
     */
    @ApiModelProperty(value = "评审状态")
    @TableField(value = "state_id")
    private Long stateId;

    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    @NotNull(message = "所属系统不能为空")
    @TableField(value = "product_id")
    @Excel(name = "所属系统",orderNum = "1",dict = "productId")
    private Long productId;

    

    /**
     * 用例等级
     */
    @ApiModelProperty(value = "用例等级")
    @TableField(value = "priority")
    @Excel(name = "用例等级", orderNum = "9", dict = "casePriorityCode")
    private Integer priority;

    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "leading_by")
    @Excel(name = "负责人", orderNum = "10", dict = "userName")
    private Long leadingBy;

    /**
     * 前提条件
     */
    @ApiModelProperty(value = "前提条件")
    @Size(max = 65535, message = "前提条件长度不能超过65535")
    @TableField(value = "prerequisite", condition = LIKE)
    @Excel(name = "前提条件", orderNum = "11")
    private String prerequisite;

    /**
     * 用例库
     */
    @ApiModelProperty(value = "用例库")
    @TableField(value = "library_id")
    private Long libraryId;

    /**
     * 用例状态:1-启用,0-废弃
     */
    @ApiModelProperty(value = "用例状态:1-启用,0-废弃")
    @TableField(value = "case_status")
    private CaseStatusEnum caseStatus;

    /**
     * 引用次数统计
     */
    @ApiModelProperty(value = "引用次数统计")
    @TableField(value = "reference_count")
    private Integer referenceCount;

    /**
     * 来源类型:1-原创,2-测试过程入库
     */
    @ApiModelProperty(value = "来源类型:1-原创,2-测试过程入库")
    @TableField(value = "source_type")
    private CaseSourceTypeEnum sourceType;
    /**
     * 回收站状态
     */
    @ApiModelProperty(value = "回收站状态")
    @TableField(value = "state")
    private Boolean state;

    /**
     * 草稿终稿
     */
    @ApiModelProperty(value = "草稿终稿")
    @TableField(value = "draft")
    private Boolean draft;

    /**
     * 用例预估执行时间
     */
    @ApiModelProperty(value = "用例预估执行时间")
    @TableField(value = "exec_time")
    @Excel(name = "预估用例执行时长", orderNum = "12")
    private Long execTime;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @Size(max = 20, message = "版本长度不能超过20")
    @TableField(value = "version", condition = LIKE)
    private String version;

    /**
     * 用例类型
     */
    @ApiModelProperty(value = "用例类型")
    @Size(max = 100, message = "用例类型长度不能超过100")
    @TableField(value = "case_type", condition = LIKE)
    @Excel(name = "用例类型", orderNum = "13", dict = "caseType")
    private String caseType;

    /**
     * 正反例 0正例 1反例
     */
    @ApiModelProperty(value = "正反例 0正例 1反例")
    @TableField(value = "is_examples")
    @Excel(name = "正反例", orderNum = "14", replace = {"正例_true", "反例_false", "_true"}, addressList = true)
    private Boolean isExamples;

    /**
     * 案例级别
     */
    @ApiModelProperty(value = "案例级别")
    @Size(max = 100, message = "案例级别长度不能超过100")
    @TableField(value = "case_level", condition = LIKE)
    private String caseLevel;

    /**
     * 评审状态
     */
    @ApiModelProperty(value = "评审状态")
    @Size(max = 100, message = "评审状态长度不能超过100")
    @TableField(value = "review_status", condition = LIKE)
    private String reviewStatus;

    /**
     * 运行条件
     */
    @ApiModelProperty(value = "运行条件")
    @TableField(value = "run_condition")
    private String runCondition;

    /**
     * 检查点
     */
    @ApiModelProperty(value = "检查点")
    @TableField(value = "checkpoints")
    private String checkpoints;

    /**
     * 数据需求
     */
    @ApiModelProperty(value = "数据需求")
    @TableField(value = "data_requirements")
    private String dataRequirements;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "case_describe")
    private String caseDescribe;

    /**
     * 关联交易
     */
    @ApiModelProperty(value = "关联交易")
    @NotNull(message = "所属交易不能为空")
    @TableField(value = "module_function_id")
    @Excel(name = "所属交易",orderNum = "2", dict = "moduleFunctionId")
    private Long moduleFunctionId;

    

    /**
     * 关联测试点
     */
    @ApiModelProperty(value = "关联测试点")
    @TableField(value = "function_points_id")
    private Long functionPointsId;

    

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称")
    @TableField(exist = false)
    private String[] tabName;

    /**
     * 标签实体类集合
     */
    @ApiModelProperty(value = "标签实体类集合")
    @TableField(exist = false)
    private List<TestTab> tabs;
    /**
     * 测试用例上传的附件文件
     */
    @ApiModelProperty(value = "关联附件文件")
    @TableField(exist = false)
    private List<File> files;
    /**
     * 意图附件文件
     */
    @ApiModelProperty(value = "意图附件文件")
    @TableField(exist = false)
    private List<File> intentFiles;

    /**
     * 前置条件附件文件
     */
    @ApiModelProperty(value = "前置条件附件文件")
    @TableField(exist = false)
    private List<File> prerequisiteFiles;

    /**
     * 测试步骤附件文件
     */
    @ApiModelProperty(value = "测试步骤附件文件")
    @TableField(exist = false)
    private List<File> testStepFiles;

    /**
     * 测试方式
     */
    @ApiModelProperty(value = "测试方式")
    @TableField(exist = false)
    private List<String> testModes;
    /**
     * 测试方式
     */
    @ApiModelProperty(value = "测试方式")
    @TableField(value = "test_mode")
    private String testMode;
    /**
     * 执行用例附件文件
     */
    @ApiModelProperty(value = "执行用例附件文件")
    @TableField(exist = false)
    private List<File> executePlanCaseFiles;
    /**
     * 关联交易名称
     */
    @ApiModelProperty(value = "关联交易名称")
    @TableField(exist = false)
    private String moduleFunctionName;
    @Builder
    public TestCase(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy,
            String caseKey, String name, String intent, String stepType, String testStep,
            String expectedResult, Long stateId, Long productId, Long treeId, Integer priority,
            Long leadingBy, String prerequisite, Long libraryId, CaseStatusEnum caseStatus, Integer referenceCount, CaseSourceTypeEnum sourceType,
            Boolean state, Boolean draft, Long execTime, String version, String caseType,
            Boolean isExamples, String caseLevel, String reviewStatus, String runCondition,
            String checkpoints, String dataRequirements, String caseDescribe,
            String testCoverage, Long moduleFunctionId, Long functionPointsId,
            Long testreqId)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.caseKey = caseKey;
        this.name = name;
        this.intent = intent;
        this.stepType = stepType;
        this.testStep = testStep;
        this.expectedResult = expectedResult;
        this.stateId = stateId;
        this.productId = productId;
        this.priority = priority;
        this.leadingBy = leadingBy;
        this.prerequisite = prerequisite;
        this.libraryId = libraryId;
        this.caseStatus = caseStatus;
        this.referenceCount = referenceCount;
        this.sourceType = sourceType;
        this.state = state;
        this.draft = draft;
        this.execTime = execTime;
        this.version = version;
        this.caseType = caseType;
        this.isExamples = isExamples;
        this.caseLevel = caseLevel;
        this.reviewStatus = reviewStatus;
        this.runCondition = runCondition;
        this.checkpoints = checkpoints;
        this.dataRequirements = dataRequirements;
        this.caseDescribe = caseDescribe;
        this.moduleFunctionId = moduleFunctionId;
    }

}
