package com.jettech.jettong.testm.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.testm.entity.TestmCustomFormFieldExtend;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * 新增实体类
 * <AUTHOR>
 * @version 1.0
 * @description 新增实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestProductCaseSaveDTO
 * @date 2022-03-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestProductCaseRegistrySaveDTO", description = "")
public class TestProductCaseRegistrySaveDTO extends TestmCustomFormFieldExtend implements Serializable
{

    private static final long serialVersionUID = 1L;


    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    private String name;
    /**
     * 测试意图
     */
    @ApiModelProperty(value = "测试意图")
    private String intent;
    /**
     * 测试步骤类型，文本/条目
     */
    @ApiModelProperty(value = "测试步骤类型，文本/条目")
    @Size(max = 20, message = "测试步骤类型，文本/条目长度不能超过20")
    private String stepType;
    /**
     * 测试步骤
     */
    @ApiModelProperty(value = "测试步骤")
    @Size(max = 65535, message = "测试步骤长度不能超过65,535")
    private String testStep;
    /**
     * 预期结果
     */
    @ApiModelProperty(value = "预期结果")
    @Size(max = 65535, message = "预期结果长度不能超过65,535")
    private String expectedResult;
    /**
     * 评审状态
     */
    @ApiModelProperty(value = "评审状态")
    private Long stateId;
    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    private Long productId;
    /**
     * 关联需求
     */
    @ApiModelProperty(value = "关联需求")
    private String requirementId;
    /**
     * 关联案例树
     */
    @ApiModelProperty(value = "关联案例树")
    private Long treeId;
    /**
     * 用例等级
     */
    @ApiModelProperty(value = "用例等级")
    private Integer priority;
    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    private Long leadingBy;
    /**
     * 前提条件
     */
    @ApiModelProperty(value = "前提条件")
    @Size(max = 65535, message = "前提条件长度不能超过65,535")
    private String prerequisite;
    /**
     * 用例库
     */
    @ApiModelProperty(value = "用例库")
    private Long libraryId;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * 标签信息
     */
    private String[] TabInfo;

    @ApiModelProperty(value = "附件文件")
    private List<File> files;

    /**
     * 草稿
     */
    @ApiModelProperty(value = "草稿")
    private Boolean draft;

    /**
     * 意图附件文件
     */
    @ApiModelProperty(value = "意图附件文件")
    private List<File> intentFiles;

    /**
     * 前置条件附件文件
     */
    @ApiModelProperty(value = "前置条件附件文件")
    private List<File> prerequisiteFiles;

    /**
     * 测试步骤附件文件
     */
    @ApiModelProperty(value = "测试步骤附件文件")
    private List<File> testStepFiles;


    /**
     * 回收站状态
     */
    @ApiModelProperty(value = "回收站状态")
    private Boolean state;

    @ApiModelProperty(value = "预估执行时间")
    private Long execTime;

    /**
     *描述
     */
    @ApiModelProperty(value = "描述")
    private String caseDescribe;
    /**
     *数据需求
     */
    @ApiModelProperty(value = "数据需求")
    private String dataRequirements;
    /**
     *测试方式集合
     */
    @ApiModelProperty(value = "测试方式集合")
    private List<String> testModes;

    /**
     *测试方式
     */
    @ApiModelProperty(value = "测试方式")
    private String testMode;

    /**
     *检查点
     */
    @ApiModelProperty(value = "检查点")
    private String checkpoints;
    /**
     *运行条件
     */
    @ApiModelProperty(value = "运行条件")
    private String runCondition;
    /**
     *评审级别
     */
    @ApiModelProperty(value = "评审级别")
    private String reviewStatus;
    /**
     *案例级别
     */
    @ApiModelProperty(value = "案例级别")
    private String caseLevel;
    /**
     *用例类型
     */
    @ApiModelProperty(value = "用例类型")
    private String caseType;

    /**
     * 正反例
     */
    @ApiModelProperty(value = "正反例")
    private Boolean isExamples;

    @ApiModelProperty(value = "测试覆盖情况")
    private String testCoverage;

    /**
     * 关联交易
     */
    @ApiModelProperty(value = "关联交易")
    private Long moduleFunctionId;

    /**
     * 关联任务
     */
    @ApiModelProperty(value = "关联任务")
    private Long taskId;

    /**
     * 关联测试点
     */
    @ApiModelProperty(value = "关联测试点")
    private Long functionPointsId;

    /**
     * 项目标识
     */
    @ApiModelProperty(value = "项目标识")
    private String projectCode;

    /**
     * 关联测试需求
     */
    @ApiModelProperty(value = "关联测试需求")
    private Long testreqId;

    /**
     * 归属项目
     */
    @ApiModelProperty(value = "归属项目")
    private Long projectId;

    @ApiModelProperty(value = "案例目录id")
    private Long tradeflowcaseFolderId;

    @ApiModelProperty(value = "脚本id")
    private Long tradeflowId;

    @ApiModelProperty(value = "是否含有脚本")
    private Integer isHaveScript;

    @ApiModelProperty(value = "案例次数")
    private Integer numCase;
}
