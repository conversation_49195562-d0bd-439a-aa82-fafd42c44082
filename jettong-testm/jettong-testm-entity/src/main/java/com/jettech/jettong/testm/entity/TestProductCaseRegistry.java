package com.jettech.jettong.testm.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_product_case_registry")
@ApiModel(value = "TestProductCaseRegistry", description = "用例仓库表")
public class TestProductCaseRegistry extends  TestProductCase {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "最后一次更新仓库的原caseid")
    @TableField(value = "last_case_id")
    private Long lastCaseId;

    @ApiModelProperty(value = "来源于组织级用例库的用例案例id")
    @TableField(value = "source_lib_case_id")
    private Long sourceLibCaseId;

    @ApiModelProperty(value = "是否已经保存到用例库")
    @TableField(value = "saved_to_lib")
    private Boolean savedToLib;

    @ApiModelProperty(value = "案例目录id")
    @TableField(value = "tradeflowcase_folder_id")
    private Long tradeflowcaseFolderId;

    @ApiModelProperty(value = "脚本id")
    @TableField(value = "trade_flow_id")
    private Long tradeflowId;

    @ApiModelProperty(value = "是否含有脚本 0无 1有")
    @TableField(value = "is_have_script")
    private Integer isHaveScript;

    @ApiModelProperty(value = "案例次数")
    @TableField(value = "num_case")
    private Integer numCase;

    @ApiModelProperty(value = "案例状态1-启用,0-废弃")
    @TableField(value = "case_status")
    private Integer caseStatus;

}
