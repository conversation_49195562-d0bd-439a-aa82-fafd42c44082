package com.jettech.jettong.testm.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jettech.basic.annotation.echo.Echo;
import com.jettech.basic.model.EchoVO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.constant.DictionaryType;
import com.jettech.jettong.common.enumeration.ProductCaseSourceTypeEnum;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.vo.ProjectInfoVO;
import com.jettech.jettong.testm.vo.TestreqVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.baomidou.mybatisplus.annotation.SqlCondition.LIKE;
import static com.jettech.jettong.common.constant.BaseEchoConstants.*;
import static com.jettech.jettong.common.constant.EchoConstants.*;


/**
 * 测试管理中 测试用例 实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试管理中 测试用例 实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.entity
 * @className TestProductCase
 * @date 2022-03-14
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@TableName("test_product_case")
@ApiModel(value = "TestProductCase", description = "")
@AllArgsConstructor
public class TestProductCase extends TestmCustomFormFieldExtend implements EchoVO {

    private static final long serialVersionUID = 1L;
    @TableField(exist = false)
    private Map<String, Object> echoMap = new HashMap<>();

    /**
     * 名称/标题
     */
    @ApiModelProperty(value = "名称/标题")
    @NotEmpty(message = "请填写名称/标题")
    @Size(max = 128, message = "名称/标题长度不能超过128")
    @TableField(value = "name", condition = LIKE)
    @Excel(name = "案例名称",width = 20, orderNum = "6")
    private String name;

    /**
     * 用例编号(与用例库统一)
     */
    @ApiModelProperty(value = "用例编号(与用例库统一)")
    @NotEmpty(message = "请填写用例编号")
    @Size(max = 100, message = "用例编号长度不能超过100")
    @TableField(value = "case_key", condition = LIKE)
    @Excel(name = "案例编号",width = 20, orderNum = "3")
    private String caseKey;

    /**
     *用例类型
     */
    @ApiModelProperty(value = "用例类型")
    @TableField(value = "case_type")
    @Excel(name = "案例类型",dict = "caseType",width = 20, orderNum = "4")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.CASE_TYPE)
    private String caseType;

    /**
     * 前提条件
     */
    @ApiModelProperty(value = "前提条件")
    @Size(max = 65535, message = "前提条件长度不能超过65535")
    @TableField(value = "prerequisite", condition = LIKE)
    @Excel(name = "前提条件",width = 20, orderNum = "5")
    private String prerequisite;

    /**
     * 正反例
     */
    @ApiModelProperty(value = "正反例")
    @TableField(value = "is_examples")
    @Excel(name = "正反例", width = 20, orderNum = "12", replace = {"正例_true", "反例_false", "_true"}, addressList = true)
    private Boolean isExamples;

    /**
     * 用例等级
     */
    @ApiModelProperty(value = "用例等级")
    @TableField(value = "priority")
    @Excel(name = "优先级",dict = "priority",width = 20, orderNum = "13")
    private Integer priority;

    /**
     * 测试步骤类型，文本/条目
     */
    @ApiModelProperty(value = "测试步骤类型，文本/条目")
    @Size(max = 20, message = "测试步骤类型，文本/条目长度不能超过20")
    @TableField(value = "step_type", condition = LIKE)
    @Excel(name = "步骤类型", width = 25, orderNum = "9", replace = {"文本_text", "条目_subclause", "_null"}, addressList = true)
    private String stepType;

    /**
     * 测试步骤
     */
    @ApiModelProperty(value = "测试步骤")
    @Size(max = 65535, message = "测试步骤长度不能超过65535")
    @TableField(value = "test_step", condition = LIKE)
    @Excel(name = "测试步骤",width = 30, orderNum = "10")
    private String testStep;

    /**
     * 预期结果
     */
    @ApiModelProperty(value = "预期结果")
    @Size(max = 65535, message = "预期结果长度不能超过65535")
    @TableField(value = "expected_result", condition = LIKE)
    @Excel(name = "预期结果",width = 20, orderNum = "11")
    private String expectedResult;

    /**
     * 测试意图
     */
    @ApiModelProperty(value = "测试意图")
    @TableField(value = "intent", condition = LIKE)
    @Excel(name = "测试意图",width = 20, orderNum = "7")
    private String intent;

    /**
     *测试方式
     */
    @ApiModelProperty(value = "测试方式")
    @TableField(value = "test_mode")
    @Excel(name = "测试方式",dict = "testMode",width = 20, orderNum = "8")
    private String testMode;

    /**
     * 关联产品
     */
    @ApiModelProperty(value = "关联产品")
    @TableField(value = "product_id")
    @Excel(name = "所属系统", dict = "productId", width = 20, orderNum = "1")
    private Long productId;

    /**
     * 关联交易
     */
    @ApiModelProperty(value = "关联交易")
    @TableField(value = "module_function_id")
    @Echo(api = PRODUCT_MODULE_FUNCTION_FEIGN_CLASS, beanClass = ProductModuleFunction.class)
    @Excel(name = "所属交易", dict = "moduleFunctionId" ,width = 20, orderNum = "2")
    private Long moduleFunctionId;


    /**
     * 评审状态
     */
    @ApiModelProperty(value = "评审状态")
    @TableField(value = "state_id")
    private Long stateId;


    /**
     * 关联需求ID
     */
    @ApiModelProperty(value = "关联需求ID")
    @TableField(value = "requirement_id")
    private Long requirementId;

    /**
     * 关联案例树
     */
    @ApiModelProperty(value = "关联案例树")
    @TableField(value = "tree_id",updateStrategy= FieldStrategy.IGNORED)
    private Long treeId;


    /**
     * 用例库
     */
    @ApiModelProperty(value = "用例库")
    @TableField(value = "library_id")
    private Long libraryId;

    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @TableField(value = "version")
//    @Excel(name = "版本",width = 10)
    private String version;
    /**
     * 标签实体类集合
     */
    @ApiModelProperty(value = "标签实体类集合")
    @TableField(exist = false)
    private List<TestTab> tabs;
    /**
     * 关联用例时，当前测试计划是否已关联
     */
    @ApiModelProperty(value = "是否已关联")
    @TableField(exist = false)
    private Boolean isRelated;

    /**
     * 测试用例上传的附件文件
     */
    @ApiModelProperty(value = "关联附件文件")
    @TableField(exist = false)
    private List<File> files;

    /**
     * 回收站状态
     */
    @ApiModelProperty(value = "回收站状态")
    @TableField(value = "`state`")
    private Boolean state;

    /**
     * 草稿
     */
    @ApiModelProperty(value = "草稿")
    @TableField(value = "`draft`")
    private Boolean draft;

    /**
     * 意图附件文件
     */
    @ApiModelProperty(value = "意图附件文件")
    @TableField(exist = false)
    private List<File> intentFiles;

    /**
     * 前置条件附件文件
     */
    @ApiModelProperty(value = "前置条件附件文件")
    @TableField(exist = false)
    private List<File> prerequisiteFiles;

    /**
     * 测试步骤附件文件
     */
    @ApiModelProperty(value = "测试步骤附件文件")
    @TableField(exist = false)
    private List<File> testStepFiles;

    /**
     * 执行用例附件文件
     */
    @ApiModelProperty(value = "执行用例附件文件")
    @TableField(exist = false)
    private List<File> executePlanCaseFiles;

    @ApiModelProperty(value = "预估执行时间")
    @TableField(value = "exec_time")
//    @Excel(name = "预估执行时间",width = 20)
    private Long execTime;

    @ApiModelProperty(value = "执行人Id")
    @TableField(exist = false)
//    @Excel(name = "执行人Id")
    private Long execBy;

    @ApiModelProperty(value = "标签Id")
    @TableField(exist = false)
    private String[] TabInfo;

    /**
     *测试覆盖情况
     */
    @ApiModelProperty(value = "测试覆盖情况")
    @TableField(value = "test_coverage")
    private String testCoverage;
    /**
     *描述
     */
    @ApiModelProperty(value = "描述")
    @TableField(value = "case_describe")
//    @Excel(name = "描述", width = 20)
    private String caseDescribe;
    /**
     *数据需求
     */
    @ApiModelProperty(value = "数据需求")
    @TableField(value = "data_requirements")
    private String dataRequirements;

    /**
     *测试方式集合
     */
    @ApiModelProperty(value = "测试方式集合")
    @TableField(exist = false)
    private List<String> testModes;


    /**
     *检查点
     */
    @ApiModelProperty(value = "检查点")
    @TableField(value = "checkpoints")
    private String checkpoints;
    /**
     *运行条件
     */
    @ApiModelProperty(value = "运行条件")
    @TableField(value = "run_condition")
    private String runCondition;
    /**
     *评审级别
     */
    @ApiModelProperty(value = "评审级别")
    @TableField(value = "review_status")
//    @Excel(name = "评审级别")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.REVIEW_STATUS)
    private String reviewStatus;
    /**
     *案例级别
     */
    @ApiModelProperty(value = "案例级别")
    @TableField(value = "case_level")
    @Echo(api = DICTIONARY_ITEM_FEIGN_CLASS, dictType = DictionaryType.CASE_LEVEL)
    private String caseLevel;

    /**
     * 关联交易名称
     */
    @ApiModelProperty(value = "关联交易名称")
    @TableField(exist = false)
    private String moduleFunctionName;

    /**
     * 关联任务
     */
    @ApiModelProperty(value = "关联任务")
    @TableField(value = "task_id")
    @NotNull(message = "用例必须关联一个任务")
    private Long taskId;

    /**
     * 关联测试点
     */
    @ApiModelProperty(value = "关联测试点")
    @TableField(value = "function_points_id")
    @Echo(api = TEST_REQUIREMENT_FUNCTION_POINTS_ID_CLASS, beanClass = TestRequirementFunctionPoints.class)
    @Excel(name = "关联测试点", dict = "functionPointsId", width = 30, orderNum = "14")
    private Long functionPointsId;



    /**
     * 负责人
     */
    @ApiModelProperty(value = "负责人")
    @TableField(value = "leading_by",updateStrategy = FieldStrategy.IGNORED)
    @Echo(api = USER_ID_FEIGN_CLASS, beanClass = User.class)
    @Excel(name = "负责人", dict = "leadingBy", width = 10, orderNum = "15")
    private Long leadingBy;
    /**
     * 项目标识
     */
    @ApiModelProperty(value = "项目标识")
    @TableField(exist = false)
    private String projectCode;

    /**
     * 关联测试需求
     */
    @ApiModelProperty(value = "关联测试需求")
    @TableField(value = "testreq_id")
    @Echo(api = TESTREQ_ID_FEIGN_CLASS, beanClass = TestreqVO.class)
    private Long testreqId;

    /**
     * 归属项目
     */
    @ApiModelProperty(value = "归属项目")
    @TableField(value = "project_id")
    @Echo(api = PROJECT_INFO_FEIGN_CLASS, beanClass = ProjectInfoVO.class)
    private Long projectId;

    /**
     * 是否被引用
     */
    @ApiModelProperty(value = "是否被引用")
    @TableField(exist = false)
    private Boolean isCited;
    /**
     * 测试计划下是否被引用
     */
    @ApiModelProperty(value = "测试计划下是否被引用")
    @TableField(exist = false)
    private Boolean isPlanCited;

    /**
     * 来源类型:1-原创,2-资产库引用
     */
    @ApiModelProperty(value = "来源类型:1-原创,2-资产库引用")
    @TableField(value = "source_type")
    private ProductCaseSourceTypeEnum sourceType;

    /**
     * 源用例ID
     */
    @ApiModelProperty(value = "源用例库用例ID")
    @TableField(value = "source_lib_case_id")
    private Long sourceLibCaseId;


    @Builder
    public TestProductCase(Long id, Long createdBy, LocalDateTime createTime, LocalDateTime updateTime, Long updatedBy, 
                    String caseKey, String name, String intent, String stepType, String testStep, 
                    String expectedResult, Long stateId, Long productId, Long requirementId, Long treeId, Integer priority,
                    Long leadingBy, String prerequisite, Long libraryId,String version, List<File> files, Boolean state ,Boolean draft ,
            List<File> intentFiles , List<File> prerequisiteFiles ,List<File> testStepFiles ,List<File>  executePlanCaseFiles,Long execTime,
            String caseType, Boolean isExamples,String caseLevel, String reviewStatus, String runCondition, String checkpoints, String testMode, String dataRequirements, String caseDescribe, String testCoverage,
                           Long moduleFunctionId,Long taskId,Long functionPointsId,Long testreqId, ProductCaseSourceTypeEnum sourceType, Long sourceLibCaseId)
    {
        this.id = id;
        this.createdBy = createdBy;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.updatedBy = updatedBy;
        this.caseKey = caseKey;
        this.name = name;
        this.intent = intent;
        this.stepType = stepType;
        this.testStep = testStep;
        this.expectedResult = expectedResult;
        this.stateId = stateId;
        this.productId = productId;
        this.requirementId = requirementId;
        this.treeId = treeId;
        this.priority = priority;
        this.leadingBy = leadingBy;
        this.prerequisite = prerequisite;
        this.libraryId = libraryId;
        this.version = version;
        this.files = files;
        this.state = state;
        this.draft = draft;
        this.intentFiles = intentFiles;
        this.prerequisiteFiles = prerequisiteFiles;
        this.testStepFiles = testStepFiles;
        this.executePlanCaseFiles = executePlanCaseFiles;
        this.execTime = execTime;
        this.caseType = caseType;
        this.isExamples = isExamples;
        this.caseLevel = caseLevel;
        this.reviewStatus = reviewStatus;
        this.runCondition = runCondition;
        this.checkpoints = checkpoints;
        this.dataRequirements = dataRequirements;
        this.caseDescribe = caseDescribe;
        this.testCoverage = testCoverage;
        this.moduleFunctionId = moduleFunctionId;
        this.taskId = taskId;
        this.functionPointsId = functionPointsId;
        this.testreqId = testreqId;
        this.sourceType = sourceType;
        this.sourceLibCaseId = sourceLibCaseId;
        this.testMode = testMode;
    }

}

