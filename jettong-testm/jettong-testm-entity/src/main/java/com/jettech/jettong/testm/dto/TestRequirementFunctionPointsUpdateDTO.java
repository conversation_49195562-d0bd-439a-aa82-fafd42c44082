package com.jettech.jettong.testm.dto;


import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 测试分析功能要点表修改实体类
 * <AUTHOR>
 * @version 1.0
 * @description 测试分析功能要点表修改实体类
 * @projectName jettong
 * @package com.jettech.jettong.testm.dto
 * @className TestRequirementFunctionPointsUpdateDTO
 * @date 2025-07-24
  * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "TestRequirementFunctionPointsUpdateDTO", description = "测试分析功能要点表")
public class TestRequirementFunctionPointsUpdateDTO implements Serializable
{

    private static final long serialVersionUID = 1L;


    /**
     * ID
     */
    @ApiModelProperty(value = "ID")
    @NotNull(message = "请填写ID")
    private Long id;
    /**
     * 测试需求id
     */
    @ApiModelProperty(value = "测试需求id")
    private Long issueTestReqId;
    /**
     * 功能（交易）id
     */
    @ApiModelProperty(value = "功能（交易）id")
    @NotNull(message = "请填写功能（交易）id")
    private Long functionId;
    /**
     * 状态code
     */
    @ApiModelProperty(value = "状态code")
    private String stateCode;
    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private Long taskId;
    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;
    /**
     * 功能点
     */
    @ApiModelProperty(value = "功能点")
    @Size(max = 1000, message = "功能点长度不能超过1000")
    private String functionPoint;
    /**
     * 测试要点
     */
    @ApiModelProperty(value = "测试要点")
    @Size(max = 2000, message = "测试要点长度不能超过2000")
    private String testPoints;
    /**
     * 规则类型
     */
    @ApiModelProperty(value = "规则类型")
    @Size(max = 255, message = "规则类型长度不能超过255")
    private String ruleType;
    /**
     * 是否涉账
     */
    @ApiModelProperty(value = "是否涉账")
    private Boolean involveAccount;
    /**
     * 是否设计批处理
     */
    @ApiModelProperty(value = "是否设计批处理")
    private Boolean involveBatch;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    @Size(max = 255, message = "优先级长度不能超过255")
    private String priority;
    /**
     * 规则来源
     */
    @ApiModelProperty(value = "规则来源")
    @Size(max = 255, message = "规则来源长度不能超过255")
    private String ruleSource;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 1000, message = "备注长度不能超过1000")
    private String remark;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long createdBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private Long updatedBy;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;
    /**
     * 扩展字段 1
     */
    @ApiModelProperty(value = "扩展字段 1")
    @Size(max = 255, message = "扩展字段 1长度不能超过255")
    private String c1;
    /**
     * 扩展字段 2
     */
    @ApiModelProperty(value = "扩展字段 2")
    @Size(max = 255, message = "扩展字段 2长度不能超过255")
    private String c2;
    /**
     * 扩展字段 3
     */
    @ApiModelProperty(value = "扩展字段 3")
    @Size(max = 255, message = "扩展字段 3长度不能超过255")
    private String c3;
    /**
     * 扩展字段 4
     */
    @ApiModelProperty(value = "扩展字段 4")
    @Size(max = 255, message = "扩展字段 4长度不能超过255")
    private String c4;
    /**
     * 扩展字段 5
     */
    @ApiModelProperty(value = "扩展字段 5")
    @Size(max = 255, message = "扩展字段 5长度不能超过255")
    private String c5;
    /**
     * 测试案例计数
     */
    @ApiModelProperty(value = "测试案例计数,默认为0")
    private Integer caseCount;
}
