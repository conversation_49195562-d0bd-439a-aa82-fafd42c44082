package com.jettech.jettong.testm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@ApiModel("用例引用DTO")
public class TestProductCaseReferenceDTO {

    @ApiModelProperty("用例库用例ID")
    @NotNull(message = "用例库用例ID不能为空")
    private List<Long> testCaseIds;

    @ApiModelProperty("任务ID")
    @NotNull(message = "任务ID不能为空")
    private Long taskId;

}