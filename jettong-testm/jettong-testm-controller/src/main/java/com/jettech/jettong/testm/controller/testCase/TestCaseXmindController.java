package com.jettech.jettong.testm.controller.testCase;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.testm.entity.TestCase;
import com.jettech.jettong.testm.entity.TestCaseComparison;
import com.jettech.jettong.testm.entity.TestCaseTree;
import com.jettech.jettong.testm.service.TestCaseComparisonService;
import com.jettech.jettong.testm.service.TestCaseService;
import com.jettech.jettong.testm.service.TestCaseTreeService;
import com.jettech.jettong.testm.service.TestProductCaseService;
import com.jettech.jettong.testm.utils.xmind.XmindParser;
import com.jettech.jettong.testm.utils.xmind.pojo.*;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description xmind的导入导出
 * @projectName jettong-tm
 * @package com.jettech.jettong.testm.controller.testCase
 * @className TestCaseXmindController
 * @date 2022/3/18 0018 9:06
 * @copyright 2021 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/testCase/xmind")
@Api(value = "TestCaseXmind", tags = "案例导入导出")
@RequiredArgsConstructor
public class TestCaseXmindController extends
        SuperSimpleController<TestCaseService, TestCase> {
    public static final String PREFIX = "TC-";
    private final TestCaseTreeService testCaseTreeService;
    private final TestCaseService testCaseService;
    private final TestCaseComparisonService testCaseComparisonService;
    private final TestProductCaseService testProductCaseService;


    @PostMapping(value = "/import/{libraryId}/{treeId}")
    @SysLog(value = "'导入Xmind:' + #simpleFile?.originalFilename", request = false, optType = OptLogTypeEnum.IMPORT)
    public R importXmind(@PathVariable("libraryId") Long libraryId, @PathVariable("treeId") Long treeId, @RequestParam(value = "file") MultipartFile simpleFile) throws Exception {
        R<Boolean> r = success();
        //数据库树节点数据
        List<TestCaseTree> testCaseTreeOfRepository = testCaseTreeService.findTestCaseTreeOfRepository(libraryId);
        List<TestCaseComparison> libraryIds = testCaseComparisonService.list(new QueryWrapper<TestCaseComparison>().eq("library_id", libraryId));
        if (libraryIds != null && !libraryIds.isEmpty()) {
            return success().setCode(2).setMsg("请先解决重复案例并导入案例");
        }
        //没有数据说明数据异常了
        if (testCaseTreeOfRepository != null && testCaseTreeOfRepository.size() > 0) {
            //当树节点表仅有一条数据时候判定为首次导入
            if (testCaseTreeOfRepository.size() == 1) {
                TestCaseTree testCaseTreeOne = testCaseTreeOfRepository.get(0);
                String name = testCaseTreeOne.getName();
                JsonRootBean jsonRootBean = (JsonRootBean) XmindParser.parseObject(simpleFile);
                RootTopic rootTopic = jsonRootBean.getRootTopic();
                Children children = rootTopic.getChildren();
                String title = rootTopic.getTitle();
                if (StringUtils.isEmpty(name) || !name.equals(title)) {
                    throw BizException.validFail("根节点不一致导入失败");
                }
                //案例节点
                ArrayList testCaseList = new ArrayList();
                //树节点
                ArrayList testCaseTreeList = new ArrayList();
                //递归便利得到所有的树节点和案例节点
                parseInfoToList(children, testCaseList, testCaseTreeList, testCaseTreeOne.getId(), libraryId);
//                ArrayList<XmindAttached> treeDateArrayList = buildTree(testCaseTreeList, testCaseTreeOne.getId(), true);
                //暂时不需要树深度校验
//                int depth = treeDepth(treeDateArrayList);
//                if (depth > 5) {
//                    throw BizException.validFail("树深度不能大于5");
//                }
                testCaseTreeService.saveBatch(testCaseTreeList);
                testCaseService.saveBatch(testCaseList);
            } else {
                String name = "";
                TestCaseTree testCaseTree2;
                List<TestCaseTree> testCaseTreesByLibraryId = testCaseTreeService.list(new QueryWrapper<TestCaseTree>().eq("library_id", libraryId).eq("id", treeId));

                if (testCaseTreesByLibraryId != null && !testCaseTreesByLibraryId.isEmpty()) {
                    testCaseTree2 = testCaseTreesByLibraryId.get(0);
                    name = testCaseTree2.getName();
                } else {
                    throw BizException.validFail("数据异常");
                }
                JsonRootBean jsonRootBean = (JsonRootBean) XmindParser.parseObject(simpleFile);
                RootTopic rootTopic = jsonRootBean.getRootTopic();
                Children children = rootTopic.getChildren();
                String title = rootTopic.getTitle();
                if (StringUtils.isEmpty(name) || !name.equals(title)) {
                    throw BizException.validFail("xmind根节点和所选节点名不一致，导入失败");
                }
                //xmind导入案例
                ArrayList<TestCase> testCaseList = new ArrayList();
                //xmind导入树节点
                ArrayList testCaseTreeList = new ArrayList();
                testCaseTreeList.add(testCaseTree2);
                //装载数据（testCaseList  testCaseTreeList   objectObjectHashMap）
                parseInfoToList(children, testCaseList, testCaseTreeList, testCaseTree2.getId(), libraryId);
                //导入数据转化为树结构的list
                ArrayList<XmindAttached> treeXmindArrayList = buildTree(testCaseTreeList, testCaseTree2.getId(), true);
                //数据库数据转化为树结构的list
                ArrayList<XmindAttached> treeDateArrayList = buildTree(testCaseTreeOfRepository, treeId, true);
//                    ArrayList<XmindAttached> treeDateArrayList2 = buildTree1(testCaseTreeOfRepository, treeId,true);

                XmindAttached treexmindAttached = null;
                if (treeXmindArrayList != null && !treeXmindArrayList.isEmpty()) {
                    treexmindAttached = treeXmindArrayList.get(0);
                }
                XmindAttached treeDateAttached = null;
                if (treeDateArrayList != null && !treeDateArrayList.isEmpty()) {
                    treeDateAttached = treeDateArrayList.get(0);
                }
                ArrayList<XmindAttached> xmindAttacheds = new ArrayList<>();
                //得到所选节点的树结构
                getTreesByTreeId(treeDateAttached, treeId, xmindAttacheds);
                //所选树的所有节点
                ArrayList<TestCaseTree> testCaseTreesDate = new ArrayList<>();
                XmindAttached xmindAttached1 = xmindAttacheds.isEmpty() ? null : xmindAttacheds.get(0);

                treeToList(xmindAttached1, testCaseTreesDate);

                //合并两个树结构，
                XmindAttached xmindAttached = mergeTrees(treexmindAttached, xmindAttached1, testCaseList, true);
                //导入树的所有节点
                ArrayList<TestCaseTree> testCaseTrees = new ArrayList<>();

                //转换成TestCaseTree的list集合
                treeToList(xmindAttached, testCaseTrees);

                //剔除已经存在的树,testCaseTrees1就是需要插入数据库中的数据
                List<TestCaseTree> testCaseTrees1 = deleteRepetitionTreesList(testCaseTrees, testCaseTreesDate);
                //项目测试案例暂时不需要验证
//                testCaseTreeOfRepository.addAll(testCaseTrees1);
//                List<TestCaseTree> collect = testCaseTreeOfRepository.stream().
//                        filter(testCaseTree -> testCaseTree.getParentId().longValue() == 0).collect(Collectors.toList());
//                TestCaseTree testCaseTree = collect.get(0);
//                ArrayList<XmindAttached> treeXmindArrayList1 = buildTree(testCaseTreeOfRepository, testCaseTree.getId(), true);
//                int depth = treeDepth(treeXmindArrayList1);
//                if (depth > 5) {
//                    throw BizException.validFail("树深度不能大于5");
//                }
                //数据库案例库数据
                List<TestCase> testCaseDateList = testCaseService.list(new QueryWrapper<TestCase>().eq("library_id", libraryId));
                ArrayList<TestCaseComparison> testCaseComparisonList = new ArrayList();
                List<TestCaseComparison> testCaseComparisons = deleteRepetitionCaseList(testCaseList, testCaseDateList, testCaseComparisonList);
                //添加新增树节点
                if (testCaseTrees1 != null && testCaseTrees1.size() > 0) {
                    testCaseTreeService.saveBatch(testCaseTrees1);
                }
                //没有重复数据情况
                if (testCaseComparisonList != null && testCaseComparisonList.isEmpty() && testCaseList != null && !testCaseList.isEmpty()) {
                    testCaseService.saveBatch(testCaseList);
                    return r;
                } else {
                    //新增不重复案例
                    if (testCaseComparisons != null && !testCaseComparisons.isEmpty()) {
                        testCaseComparisonService.saveBatch(testCaseComparisons);
                    }
                    //新增重复案例
                    if (testCaseComparisonList != null && !testCaseComparisonList.isEmpty()) {
                        testCaseComparisonService.saveBatch(testCaseComparisonList);
                    }
                    if (testCaseComparisonList != null && !testCaseComparisonList.isEmpty()) {
                        return success("存在冲突用例，是否手动处理冲突后导入").setCode(1);
                    }
                }
//                }
            }
        } else {
            throw BizException.validFail("数据异常");
        }
        return r;
    }

    private int treeDepth(ArrayList<XmindAttached> list) {
        if (list == null || list.isEmpty()) {
            return 0;
        }
        int depth = 0;
        for (int i = 0; i < list.size(); i++) {
            XmindAttached xmindAttached = list.get(i);
            ArrayList<XmindAttached> children = xmindAttached.getChildren();
            int depth1 = treeDepth(children);
            if (depth <= depth1) {
                depth = depth1;
            }
        }
        return depth + 1;
    }

    /**
     * 删除重复案例名（案例name和tree节点视为数据相同）
     *
     * @param testCaseList
     * @param testCaseDateList
     * @return
     */
    private List<TestCaseComparison> deleteRepetitionCaseList(ArrayList<TestCase> testCaseList, List<TestCase> testCaseDateList, List<TestCaseComparison> repetCaseDateList) {
        ArrayList<TestCaseComparison> testCaseComparisons = new ArrayList<>();
        if (testCaseList == null || testCaseList.size() == 0) {
            return testCaseComparisons;
        }
        if (testCaseDateList == null && testCaseDateList.size() == 0) {
            return testCaseComparisons;
        }
        for (int i = 0; testCaseList.size() > i; i++) {
            boolean flag = true;
            TestCase testCase = testCaseList.get(i);
            String name = testCase.getName();
            for (int j = 0; testCaseDateList.size() > j; j++) {
                TestCase testCase1 = testCaseDateList.get(j);
                String name1 = testCase1.getName();
                Long id = testCase1.getId();
                TestCaseComparison testCaseComparison = new TestCaseComparison();
                if (StringUtils.isNoneEmpty(name) && name.equals(name1) ) {
                    //重复数据
                    BeanUtil.copyProperties(testCase, testCaseComparison);
                    testCaseComparison.setRepeatState(1);
                    testCaseComparison.setSaveState(0);
                    testCaseComparison.setId(id);//此处把重复数据id更换掉是为了解决冲突数据后更具id更新数据
                    repetCaseDateList.add(testCaseComparison);
                    testCaseList.remove(testCase);
                    flag = false;
                    i--;
                    break;
                }
            }
            if (flag) {
                TestCaseComparison testCaseComparison = new TestCaseComparison();
                //不重复数据
                BeanUtil.copyProperties(testCase, testCaseComparison);
                testCaseComparison.setRepeatState(0);
                testCaseComparison.setSaveState(0);
                testCaseComparisons.add(testCaseComparison);
            }
        }
        return testCaseComparisons;
    }

    /**
     * 删除重复树节点
     *
     * @param testCaseTrees
     * @param testCaseTreeOfRepository
     * @return
     */
    private static List<TestCaseTree> deleteRepetitionTreesList(ArrayList<TestCaseTree> testCaseTrees, List<TestCaseTree> testCaseTreeOfRepository) {
        if (testCaseTrees == null || testCaseTrees.size() == 0) {
            return null;
        }
        if (testCaseTreeOfRepository == null && testCaseTreeOfRepository.size() == 0) {
            return testCaseTrees;
        }
        for (int i = 0; testCaseTrees.size() > i; i++) {
            TestCaseTree testCaseTree = testCaseTrees.get(i);
            String name = testCaseTree.getName();
            Long parentId = testCaseTree.getParentId();
            for (int j = 0; testCaseTreeOfRepository.size() > j; j++) {
                TestCaseTree testCaseTree1 = testCaseTreeOfRepository.get(j);
                String name1 = testCaseTree1.getName();
                Long parentId1 = testCaseTree1.getParentId();
                if (StringUtils.isNoneEmpty(name) && name.equals(name1) && parentId != null && parentId1 != null && parentId.longValue() == parentId1.longValue()) {
                    testCaseTrees.remove(testCaseTree);
                    i--;
                    break;
                }
            }
        }
        return testCaseTrees;
    }

    /**
     * 树转对象集合
     *
     * @param xmindAttached
     * @param list
     */
    private void treeToList(XmindAttached xmindAttached, ArrayList<TestCaseTree> list) {
        if (xmindAttached == null) {
            return;
        }
        TestCaseTree testCaseTree = xmindAttached.getTestCaseTree();
        if (testCaseTree != null) {
            list.add(testCaseTree);
        }
        ArrayList<XmindAttached> children = xmindAttached.getChildren();
        for (int i = 0; children.size() > i; i++) {
            XmindAttached xmindAttached1 = children.get(i);
            treeToList(xmindAttached1, list);
        }
    }

    /**
     * 合并两个树，并替换新增案例的树id
     *
     * @param treeXmindAttached xmind树
     * @param treeDateAttached  数据库树
     * @param testCaseList      合并树时候替换的案例id的list
     * @return
     */
    private static XmindAttached mergeTrees(XmindAttached treeXmindAttached, XmindAttached treeDateAttached, ArrayList<TestCase> testCaseList, boolean flag1) {
        if (treeXmindAttached == null) {
            return treeDateAttached;
        }
        if (treeDateAttached == null) {
            return treeXmindAttached;
        }
        ArrayList<XmindAttached> children = treeXmindAttached.getChildren();
        ArrayList<XmindAttached> children1 = treeDateAttached.getChildren();
        if (children == null || children.isEmpty()) {
            return null;
        }
        //导入数据
        for (int i = 0; children.size() > i; i++) {
            XmindAttached xmindAttached = children.get(i);
            String name = xmindAttached.getName();
            Long id = xmindAttached.getId();
            boolean flag = false;
            int a = -1;
            Long parentId = -1L;
            Long dateId = -1L;
            if (children1 != null && !children1.isEmpty()) {
                for (int j = 0; children1.size() > j; j++) {
                    XmindAttached xmindAttached1 = children1.get(j);
                    String name1 = xmindAttached1.getName();
                    parentId = xmindAttached1.getParentId();
                    dateId = xmindAttached1.getId();
                    if (name.equals(name1)) {
                        flag = true;
                        a = j;
                        break;
                    }
                }
            } else {
                if (flag1) {
                    parentId = treeDateAttached.getId();
                }
            }
            if (flag) {
                mergeTrees(xmindAttached, children1.get(a), testCaseList, flag1);
            } else {
                TestCaseTree testCaseTree = xmindAttached.getTestCaseTree();
                testCaseTree.setParentId(parentId);
                xmindAttached.setParentId(parentId);
                xmindAttached.setTestCaseTree(testCaseTree);
                children1.add(xmindAttached);
                treeDateAttached.setChildren(children1);
            }
        }
        return treeDateAttached;
    }


//    /**
//     * list转换成list树
//     *
//     * @param testCaseTreeList
//     * @param parentId
//     * @return
//     */
//    private static ArrayList<XmindAttached> buildTree(List<TestCaseTree> testCaseTreeList, Long parentId) {
//        ArrayList<XmindAttached> xmindAttacheds = new ArrayList<>();
//        List<TestCaseTree> array = testCaseTreeList.stream().filter(
//                obj -> (obj.getParentId().longValue()) == parentId).collect(Collectors.toList());
//        for (int i = 0; i < array.size(); i++) {
//            TestCaseTree testCaseTree = array.get(i);
//            XmindAttached xmindAttached = new XmindAttached();
//            xmindAttached.setId(testCaseTree.getId());
//            xmindAttached.setName(testCaseTree.getName());
//            xmindAttached.setTestCaseTree(testCaseTree);
//            xmindAttached.setParentId(parentId);
//            xmindAttached.setChildren(buildTree(testCaseTreeList, xmindAttached.getId()));
//            xmindAttacheds.add(xmindAttached);
//        }
//        return xmindAttacheds;
//    }


    private static ArrayList<XmindAttached> buildTree(List<TestCaseTree> testCaseTreeList, Long id, boolean flag) {
        ArrayList<XmindAttached> xmindAttacheds = new ArrayList<>();
        List<TestCaseTree> array;
        if (flag) {
            array = testCaseTreeList.stream().filter(
                    obj -> (obj.getId().longValue()) == id).collect(Collectors.toList());
        } else {
            array = testCaseTreeList.stream().filter(
                    obj -> (obj.getParentId().longValue()) == id).collect(Collectors.toList());
        }

        for (int i = 0; i < array.size(); i++) {
            TestCaseTree testCaseTree = array.get(i);
            XmindAttached xmindAttached = new XmindAttached();
            xmindAttached.setId(testCaseTree.getId());
            xmindAttached.setName(testCaseTree.getName());
            xmindAttached.setTestCaseTree(testCaseTree);
            if (flag) {
                xmindAttached.setParentId(testCaseTree.getParentId());
            } else {
                xmindAttached.setParentId(id);
            }
            xmindAttached.setChildren(buildTree(testCaseTreeList, xmindAttached.getId(), false));
            xmindAttacheds.add(xmindAttached);
        }
        return xmindAttacheds;
    }

    private static ArrayList<XmindAttached> buildTreeByTreeId(List<TestCaseTree> testCaseTreeList, Long parentId, Long treeId) {
        ArrayList<XmindAttached> xmindAttacheds = new ArrayList<>();
        List<TestCaseTree> array = testCaseTreeList.stream().filter(
                obj -> (obj.getParentId().longValue()) == parentId).collect(Collectors.toList());
        for (int i = 0; i < array.size(); i++) {
            TestCaseTree testCaseTree = array.get(i);
            XmindAttached xmindAttached = new XmindAttached();
            Long id = testCaseTree.getId();
            if (treeId == null || (id.longValue() == treeId.longValue())) {

            }
            xmindAttached.setId(testCaseTree.getId());
            xmindAttached.setName(testCaseTree.getName());
            xmindAttached.setTestCaseTree(testCaseTree);
            xmindAttached.setParentId(parentId);
            xmindAttached.setChildren(buildTreeByTreeId(testCaseTreeList, xmindAttached.getId(), treeId));
            xmindAttacheds.add(xmindAttached);
        }
        return xmindAttacheds;
    }

    //得到所选数据节点的树结构
    private static void getTreesByTreeId(XmindAttached treeXmindAttached, Long treeId, ArrayList<XmindAttached> xmindAttachedList) {
        if (treeXmindAttached == null) {
            return;
        }
        Long id1 = treeXmindAttached.getId();
        if (id1.longValue() == treeId.longValue()) {
            xmindAttachedList.add(treeXmindAttached);
        } else {
            ArrayList<XmindAttached> children = treeXmindAttached.getChildren();
            if (children == null || children.isEmpty()) {
                return;
            }
            //导入数据
            for (int i = 0; children.size() > i; i++) {
                XmindAttached xmindAttached = children.get(i);
                Long id = xmindAttached.getId();
                if (id.longValue() == treeId.longValue()) {
                    xmindAttachedList.add(xmindAttached);
                } else {
                    getTreesByTreeId(xmindAttached, treeId, xmindAttachedList);
                }
            }
        }
    }

    public void parseInfoToList(Children children, ArrayList testCaseList, ArrayList testCaseTreeList, Long caseTreeId, Long libraryId) {
        if (children == null) {
            return;
        }
        List<Attached> attached = children.getAttached();
        if (attached == null || attached.size() == 0) {
            return;
        } else {
            //组装英文标识后缀
            int caseKeySuffix = 1;
            for (Attached attache : attached) {
                List<Markers> markers = attache.getMarkers();
                long flag = 0;
                if (markers != null && markers.size() > 0) {
                    flag = markers.
                            stream().filter(marker -> marker.getMarkerId().startsWith("flag")).count();
                }
                //递归获取树分支
                if (flag > 0) {
                    TestCaseTree testCaseTree = new TestCaseTree();
                    testCaseTree.setId(UidGeneratorUtil.getId());
                    testCaseTree.setName(attache.getTitle());
                    testCaseTree.setLibraryId(libraryId);
                    testCaseTree.setParentId(caseTreeId);
                    testCaseTree.setCreateTime(LocalDateTime.now());
                    testCaseTreeList.add(testCaseTree);
                    Children children1 = attache.getChildren();
                    parseInfoToList(children1, testCaseList, testCaseTreeList, testCaseTree.getId(), libraryId);
                } else {
                    //获取案例数据
                    TestCase testCase = new TestCase();
                    Children children1 = attache.getChildren();
                    if (children1 == null) {
                        return;
                    }
                    List<Attached> attached2 = children1.getAttached();
                    if (attached2 == null) {
                        return;
                    }
                    testCase.setId(UidGeneratorUtil.getId());
                    testCase.setLibraryId(libraryId);
                    testCase.setCreatedBy(ContextUtil.getUserId());
                    testCase.setLeadingBy(ContextUtil.getUserId());
                    testCase.setUpdatedBy(ContextUtil.getUserId());
                    testCase.setCreateTime(LocalDateTime.now());
//                    testCase.setCaseKey(testProductCaseService.getCaseKey(libraryId));
                    String[] title1 = attache.getTitle().split("&");
                    testCase.setName(title1[0]);
                    if (title1.length > 1) {
                        testCase.setPrerequisite(title1[1]);
                    }
                    List<Markers> markers1 = attache.
                            getMarkers();
                    long star = 0;
                    if (markers1 != null) {
                        star = markers1.
                                stream().filter(marker -> marker.getMarkerId().startsWith("star")).count();
                    }
                    List<Markers> markers2 = attache.
                            getMarkers();
                    //没有带等级默认为3
                    if (markers2 == null) {
                        testCase.setPriority(3);
                    } else {
                        //获取等级
                        List<Markers> priorityList = markers2.
                                stream().filter(marker -> marker.getMarkerId().startsWith("priority")).collect(Collectors.toList());
                        if (priorityList != null && priorityList.size() > 0) {
                            //从便签获取等级，不在1.2.3.4.5中的默认是等级为3
                            testCase.setPriority(codeSwitch(priorityList.get(0).getMarkerId()));
                        } else {
                            testCase.setPriority(3);
                        }
                    }
                    //添加测试步骤和结果
                    if (attached2 != null && attached2.size() > 0) {
                        if (star > 0) {
                            testCase.setStepType("subclause");
                            JSONArray testStepJsonArray = new JSONArray();
                            for (int i = 0; i < attached2.size(); i++) {
                                JSONObject testStepJson = new JSONObject();
                                String[] split = attached2.get(i).getTitle().split("&");
                                testStepJson.put("caseStepNum", i + 1);
                                testStepJson.put("caseStepDes", split[0]);
                                if (split.length > 1) {
                                    testStepJson.put("expectResult", split[1]);
                                }
                                testStepJsonArray.add(testStepJson);
                            }
                            testCase.setTestStep(testStepJsonArray.toJSONString());
                        } else {
                            String[] split = attached2.get(0).getTitle().split("&");
                            testCase.setTestStep(split[0]);
                            testCase.setStepType("text");
                            if (split.length > 1) {
                                testCase.setExpectedResult(split[1]);
                            }
                        }

                    }
                    caseKeySuffix++;
                    testCaseList.add(testCase);
                }
            }
        }
    }

    /**
     * 字段转换
     *
     * @param markerId
     * @return
     */
    private static Integer codeSwitch(String markerId) {
        int priorityInt = 3;
        switch (markerId) {
            case "priority-1":
                priorityInt = 1;
                break;
            case "priority-2":
                priorityInt = 2;
                break;
            case "priority-3":
                priorityInt = 3;
                break;
            case "priority-4":
                priorityInt = 4;
                break;
            case "priority-5":
                priorityInt = 5;
                break;
            default:
                break;
        }
        return priorityInt;
    }
}
