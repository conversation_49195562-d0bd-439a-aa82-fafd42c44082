package com.jettech.jettong.testm.controller.testReq;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.ChineseCharToEnUtil;
import com.jettech.basic.utils.StringUtil;
import com.jettech.jettong.alm.api.RequirementApi;
import com.jettech.jettong.alm.api.StateApi;
import com.jettech.jettong.alm.api.TestreqApi;
import com.jettech.jettong.alm.api.WorkflowApi;
import com.jettech.jettong.alm.issue.dto.StateTransitionDTO;
import com.jettech.jettong.alm.issue.entity.Requirement;
import com.jettech.jettong.alm.issue.entity.Testreq;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.product.api.ProductInfoApi;
import com.jettech.jettong.product.api.ProductModuleFunctionApi;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.dto.TestRequirementFunctionPointsPageQuery;
import com.jettech.jettong.testm.dto.TestRequirementFunctionPointsSaveDTO;
import com.jettech.jettong.testm.dto.TestRequirementFunctionPointsUpdateDTO;
import com.jettech.jettong.testm.entity.TestRequirementFunctionPoints;
import com.jettech.jettong.testm.service.TestRequirementFunctionPointsService;
import com.jettech.jettong.testm.vo.TestPointsTreeNodeVO;
import com.jettech.jettong.testm.vo.TestPointsTreeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicReference;

import static com.jettech.jettong.common.constant.SwaggerConstants.DATA_TYPE_LONG;
import static com.jettech.jettong.common.constant.SwaggerConstants.PARAM_TYPE_PATH;

/**
 * 测试分析功能要点表控制器
 * <AUTHOR>
 * @version 1.0
 * @description 测试分析功能要点表控制器
 * @projectName jettong
 * @package com.jettech.jettong.testm.controller
 * @className TestRequirementFunctionPointsController
 * @date 2025-07-24
  * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/testRequirementFunctionPoints")
@Api(value = "TestRequirementFunctionPoints", tags = "测试分析功能要点表")
@PreAuth(replace = "testm:testRequirementFunctionPoints:")
public class TestRequirementFunctionPointsController extends SuperController<TestRequirementFunctionPointsService, Long, TestRequirementFunctionPoints, TestRequirementFunctionPointsPageQuery, TestRequirementFunctionPointsSaveDTO, TestRequirementFunctionPointsUpdateDTO>
{
    private final EchoService echoService;
    private final RequirementApi requirementApi;
    private final ProductModuleFunctionApi productModuleFunctionApi;
    private final WorkflowApi workflowApi;
    private final StateApi stateApi;
    private final UserApi userApi;
    private final TestreqApi testreqApi;
    private final ProductInfoApi productInfoApi;
    @Value("${testm.testPoints.workflow:9}")
    private Long workflowId;
    @Override
    public R<TestRequirementFunctionPoints> handlerSave(@Validated TestRequirementFunctionPointsSaveDTO data)
    {
        TestRequirementFunctionPoints entity = BeanPlusUtil.toBean(data, TestRequirementFunctionPoints.class);
        String startStateCode = workflowApi.getStartStateCode(workflowId);
        entity.setStateCode(startStateCode);
        baseService.save(entity);
        return success(entity);
    }

    @Override
    public R<Boolean> handlerDelete(List<Long> ids)
    {
        if (ids.isEmpty()) {
            return fail("请选择要删除的信息");
        }
        AtomicReference<Boolean> delete = new AtomicReference<>(false);
        //关联有测试用例不允许删除
        List<TestRequirementFunctionPoints> testRequirementFunctionPoints = baseService.listByIds(ids);
        testRequirementFunctionPoints.forEach(testRequirementFunctionPoint -> {
            if(testRequirementFunctionPoint.getCaseCount()!=0){
                delete.set(true);

            }
        });
        if(delete.get()){
            return  fail("当前关联测试用例，请先删除后再执行此操作");
        }

        return success(baseService.removeByIds(ids));
    }

    @Override
    public R<TestRequirementFunctionPoints> handlerUpdate(@Validated TestRequirementFunctionPointsUpdateDTO data)
    {
        TestRequirementFunctionPoints entity = BeanPlusUtil.toBean(data, TestRequirementFunctionPoints.class);
        baseService.updateById(entity);
        return success(entity);
    }

    @Override
    public R<TestRequirementFunctionPoints> get(Long id) {
        TestRequirementFunctionPoints entity = baseService.getById(id);
        if (entity != null) {
            echoService.action(entity);
        }
        if(entity.getFunctionId() != null){
            entity.getEchoMap().put("functionId",productModuleFunctionApi.findProductModuleFunctionById(entity.getFunctionId()));
        }
        if (entity.getIssueTestReqId() != null){
            Requirement requirement = requirementApi.getRequirement(entity.getIssueTestReqId());
            if(requirement!=null){
                entity.getEchoMap().put("issueTestReqId",requirement);
            }
            Testreq testreq = testreqApi.getTestreq(entity.getIssueTestReqId());
            if(testreq!=null){
                entity.getEchoMap().put("issueTestReqId",testreq);
            }
        }
        if(entity.getStateCode()!=null){
            entity.getEchoMap().put("stateCode",stateApi.findByStateCode(entity.getStateCode()));
        }
        return success(entity);
    }

    @Override
    public IPage<TestRequirementFunctionPoints> query(@RequestBody @Validated
    PageParams<TestRequirementFunctionPointsPageQuery> params)
    {
        IPage<TestRequirementFunctionPoints> page = params.buildPage(TestRequirementFunctionPoints.class);
        TestRequirementFunctionPointsPageQuery model = params.getModel();
        List<Long> functionIds = new ArrayList<>();
        if(model.getFunctionAndModuleId()!=null &&model.getFunctionAndModuleId()!=0L ){
            functionIds = baseService.getFunctionIdsByFunctionId(model.getFunctionAndModuleId());

            if(functionIds.isEmpty()){
                return  page;
            }
        }

        LbqWrapper<TestRequirementFunctionPoints> wrapper = Wraps.lbQ();
        wrapper.eq(TestRequirementFunctionPoints::getIssueTestReqId, model.getIssueTestReqId())
                .eq(TestRequirementFunctionPoints::getFunctionId, model.getFunctionId())
                .eq(TestRequirementFunctionPoints::getProjectId, model.getProjectId())
                .eq(TestRequirementFunctionPoints::getStateCode, model.getStateCode())
                .eq(TestRequirementFunctionPoints::getTaskId, model.getTaskId())
                .like(TestRequirementFunctionPoints::getFunctionPoint, model.getFunctionPoint())
                .like(TestRequirementFunctionPoints::getTestPoints, model.getTestPoints())
                .eq(TestRequirementFunctionPoints::getRuleType, model.getRuleType())
                .eq(TestRequirementFunctionPoints::getInvolveAccount, model.getInvolveAccount())
                .eq(TestRequirementFunctionPoints::getInvolveBatch, model.getInvolveBatch())
                .eq(TestRequirementFunctionPoints::getPriority, model.getPriority())
                .in(TestRequirementFunctionPoints::getFunctionId, functionIds);

        baseService.page(page, wrapper);
        echoService.action(page.getRecords());
        page.getRecords().forEach(
                item -> {
                    item.getEchoMap().put("createdBy",userApi.findUserById(item.getCreatedBy()));
                    item.getEchoMap().put("stateCode",stateApi.findByStateCode(item.getStateCode()));
                }
        );
        return page;
    }


    @ApiOperation(value = "测试分析状态状态流转", notes = "测试分析状态状态流转")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "schemeId", value = "测试分析状态id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH),
            @ApiImplicitParam(name = "sourceStateCode", value = "源状态code", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH),
            @ApiImplicitParam(name = "targetStateCode", value = "目标状态code", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @PutMapping("/transitionState/{pointsId}/{sourceStateCode}/{targetStateCode}")
    @SysLog(value = "测试分析状态流转", request = false, optType = OptLogTypeEnum.EDIT)
    public R<Boolean> transitionState(@PathVariable("pointsId") Long pointsId,
            @PathVariable("sourceStateCode") String sourceStateCode,
            @PathVariable("targetStateCode") String targetStateCode)
    {
        TestRequirementFunctionPoints scheme = baseService.getById(pointsId);
        if (scheme == null){
            return R.fail("测试记录不存在");
        }
        StateTransitionDTO stateTransitionDTO = new StateTransitionDTO();
        stateTransitionDTO.setWorkflowId(workflowId);
        stateTransitionDTO.setBizId(pointsId);
        stateTransitionDTO.setSourceStateCode(sourceStateCode);
        stateTransitionDTO.setTargetStateCode(targetStateCode);
        stateTransitionDTO.setTypeCode("TEST_POINTS");
        stateTransitionDTO.setCreateTime(scheme.getCreateTime());
        Boolean result = workflowApi.transitionStateByWorkflowIdAndBizId(stateTransitionDTO);
        if (!result){
            return R.fail("状态流转失败");
        }
        scheme.setStateCode(targetStateCode);
        baseService.updateById(scheme);
        return success();
    }

    @ApiOperation(value = "查询测试分析可流转的状态节点", notes = "查询测试分析可流转的状态节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pointsId", value = "测试分析id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @GetMapping("/findNextNode/{pointsId}")
    public R<List<WorkflowNode>> findNextNode(@PathVariable Long pointsId)
    {
        TestRequirementFunctionPoints points = baseService.getById(pointsId);
        if (points == null){
            return R.fail("目录不存在");
        }
        List<WorkflowNode> nextNodes = workflowApi.findNextNodeByWorkflowIdAndLeadingBy(workflowId, 0L, points.getStateCode());
        return success(nextNodes);
    }
    @ApiOperation(value = "查询测试要点分析树", notes = "查询测试要点分析树")
    @GetMapping("/tree")
    public R<TestPointsTreeVO> getTestPointsTree(@RequestParam(value = "projectId") String projectId,@RequestParam(value = "reqId", required = false) String reqId, @RequestParam(value = "taskId", required = false) String taskId) {
        // 处理参数为非数值的非空字符串场景
        Long prjId = StringUtil.isNumeric(projectId)  ? Long.valueOf(projectId) : null;
        if (prjId == null){
            return R.fail("项目ID不能为空");
        }
        Long rId = StringUtil.isNumeric(reqId) ? Long.valueOf(reqId) : null;
        Long tId = StringUtil.isNumeric(taskId) ? Long.valueOf(taskId) : null;

        return success(baseService.generateTestPointsTree(prjId, rId, tId));
    }
    @ApiOperation(value = "新增修改交易或新增修改功能分析", notes = "新增修改交易或新增修改功能分析")
    @PostMapping("/addOrUpdatePointsAndFunction")
    public R<List<TestPointsTreeNodeVO>> addOrUpdatePointsAndFunction(@RequestBody List<TestPointsTreeNodeVO> vos)
    {
        List<ProductModuleFunction> functions = new ArrayList<>();
        List<TestRequirementFunctionPoints> points = new ArrayList<>();

        vos.forEach(vo -> {
            String type = vo.getData().getField();
            switch (type) {
                case "function":
                    ProductModuleFunction func = new ProductModuleFunction();
                    func.setId(vo.getId());
                    func.setName(vo.getName());
                    func.setParentId(vo.getParentId());
                    func.setProductId(vo.getId());
                    if (vo.getId() == null) {
                        func.setCode(
                                new StringBuilder()
                                        .append(ChineseCharToEnUtil.getFirstLetters(vo.getName()))
                                        .append(ThreadLocalRandom.current().nextInt(90))
                                        .toString()
                        );
                    }
                    functions.add(func);
                    break;
                case "points":
                    TestRequirementFunctionPoints point = new TestRequirementFunctionPoints();
                    point.setId(vo.getId());
                    point.setFunctionId(vo.getParentId());
                    point.setIssueTestReqId(vo.getId());
                    points.add(point);
                    break;
            }
        });

        if (!points.isEmpty()) {
            baseService.saveOrUpdateBatch(points);
        }
        if (!functions.isEmpty()) {
            productModuleFunctionApi.addOrUpdateProductModuleFunction(functions);
        }
        return R.success(vos);
    }
//    @GetMapping("/test/{id}")
//    public void addTest(@PathVariable Long id){
//        baseService.addCaseCount(id);
//    }
}
