package com.jettech.jettong.testm.controller.poi;

import cn.afterturn.easypoi.handler.inter.IExcelDictHandler;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.jettong.alm.api.RequirementApi;
import com.jettech.jettong.alm.issue.entity.Requirement;
import com.jettech.jettong.base.api.DictionaryApi;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.base.enumeration.dictionary.DictionaryRelationBizType;
import com.jettech.jettong.common.constant.DictionaryType;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.entity.ProductCaseLibrary;
import com.jettech.jettong.testm.entity.ProductCaseTree;
import com.jettech.jettong.testm.entity.TestRequirementFunctionPoints;
import com.jettech.jettong.testm.entity.TestTabCase;
import com.jettech.jettong.testm.service.ProductCaseLibraryService;
import com.jettech.jettong.testm.service.ProductCaseTreeService;
import com.google.common.collect.ImmutableMap;
import com.jettech.jettong.testm.service.TestRequirementFunctionPointsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 测试案例导出字典处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例导出字典处理器
 * @projectName jettong
 * @package com.jettech.jettong.testm.controller.poi
 * @className TaskExcelDictHandlerImpl
 * @date 2022/01/17 14:15
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
@RequiredArgsConstructor
public class TestProductCaseExcelDictHandlerImpl implements IExcelDictHandler
{

    private final Map<String, List<Map>> dictMap = new HashMap<>();

    private final Map<Long, List<Dictionary>> dicMap = new HashMap<>();

    /**
     * 用例库
     */
    public static final String  LIBRARY_ID= "libraryId";
    public static final String  PRODUCT_ID= "productId";
    public static final String  MODULE_FUNCTION_ID= "moduleFunctionId";

    /**
     * 关联案例树
     */
    public static final String TREE_ID = "treeId";

    /**
     * 归属需求
     */
    public static final String REQUIREMENT_ID = "requirementId";

    /**
     * 用例等级
     */
    public static final String PRIORITY = "priority";

    /**
     * 测试步骤类型，文本/条目
     */
    public static final String STEP_TYPE = "stepType";

    /**
     * 用户
     */
    public static final String USER_NAME = "userName";

    public static final String LEADING_BY = "leadingBy";

    public static final String FUNCTION_POINTS_ID = "functionPointsId";

    public static final String IS_EXAMPLES = "isExamples";

    public static final String CASE_TYPE = "caseType";

    public static final String TEST_MODE = "testMode";
    public static final String EXEC_BY = "execBy";
    public static final String STATUS = "status";
    public static final String SINGLE_EXEC_RESULT = "singleExecResult";



    private final RequirementApi requirementApi;

    private final UserApi userApi;

    private final ProductCaseLibraryService testCaseLibraryService;
    private final ProductCaseTreeService testCaseTreeService;
    private final TestRequirementFunctionPointsService testRequirementFunctionPointsService;
    private final DictionaryApi dictionaryApi;


    public static final String  priorityNameArray[]={"最低","较低","普通","较高","最高"};
    public static final String  isExamples[]={"正例","反例"};
    public static final String  stepTypeNameArray[]={"文本","条目"};
    public static final String  statusCodeNameArray[]={"成功","失败","阻塞","无效"};

    /**
     * 平台所有等级code和名称map
     */
    private static final ThreadLocal<Map<String, String>> PRIORITY_CODE_NAME = new ThreadLocal<>();
    private static final ThreadLocal<Map<String, String>> STATUS_CODE_NAME = new ThreadLocal<>();

    /**
     * 正反例code和名称map
     */
    private static final ThreadLocal<Map<String, String>> IS_EXAMPLES_CODE_NAME = new ThreadLocal<>();

    /**
     * 平台所有等级名称和code map
     */
    private static final ThreadLocal<Map<String, String>> PRIORITY_NAME_CODE = new ThreadLocal<>();

    /**
     * 测试步骤类型code和名称map
     */
    private static final ThreadLocal<Map<String, String>> STEP_TYPE_CODE_NAME = new ThreadLocal<>();
    /**
     * 测试步骤类型code和名称map
     */
    private static final ThreadLocal<Map<String, String>> STEP_TYPE_NAME_CODE = new ThreadLocal<>();

    /**
     * 用例库id和name map
     */
    private static final ThreadLocal<Map<String, String>> LIBRARY_ID_NAME = new ThreadLocal<>();

    /**
     * 关联案例树id和name map
     */
    private static final ThreadLocal<Map<String, String>> TREE_ID_NAME = new ThreadLocal<>();

    /**
     * 关联案例树name和id map
     */
    private static final ThreadLocal<Map<String, String>> TREE_NAME_ID = new ThreadLocal<>();


    /**
     * 平台所有需求id和code map
     */
    private static final ThreadLocal<Map<String, String>> REQUIREMENT_ID_CODE = new ThreadLocal<>();

    /**
     * 平台所有需求code和id map
     */
    private static final ThreadLocal<Map<String, String>> REQUIREMENT_CODE_ID = new ThreadLocal<>();

    /**
     * 平台所有用户id和name map
     */
    private static final ThreadLocal<Map<String, String>> USER_ID_NAME = new ThreadLocal<>();

    /**
     * 平台所有用户name和id map
     */
    private static final ThreadLocal<Map<String, String>> USER_NAME_ID = new ThreadLocal<>();

    /**
     * 用例类型code和name map
     */
    private static final ThreadLocal<Map<String, String>> CASE_TYPE_CODE_NAME = new ThreadLocal<>();

    /**
     * 用例类型code和name map
     */
    private static final ThreadLocal<Map<String, String>> TEST_MODE_CODE_NAME = new ThreadLocal<>();

    /**
     * 平台所有用户name和id map
     */
    private static final Map<String, String> POINTS_ID_NAME = new HashMap();

    @Override
    public String toName(String dict, Object obj, String name, Object value)
    {
        Map<String, String> userMap = USER_ID_NAME.get();
        Map<String, String> statusMap = STATUS_CODE_NAME.get();
        if (value == null)
        {
            return null;
        }
        switch (dict)
        {
            case PRIORITY:
                Map<String, String> priorityMap = PRIORITY_CODE_NAME.get();
                if (priorityMap == null)
                {
                    priorityMap = getPriorityCodeName();
                    PRIORITY_CODE_NAME.set(priorityMap);
                }
                return priorityMap.getOrDefault(String.valueOf(value), value.toString());
            case SINGLE_EXEC_RESULT:
                if (statusMap == null)
                {
                    statusMap = getStatusCodeName();
                    STATUS_CODE_NAME.set(statusMap);
                }
                return statusMap.getOrDefault(String.valueOf(value), value.toString());
            case STATUS:
                if (statusMap == null)
                {
                    statusMap = getStatusCodeName();
                    STATUS_CODE_NAME.set(statusMap);
                }
                return statusMap.getOrDefault(String.valueOf(value), value.toString());
            case STEP_TYPE:
                Map<String, String> stepTypeMap = STEP_TYPE_CODE_NAME.get();
                if (stepTypeMap == null)
                {
                    stepTypeMap = getStepTypeCodeName();
                    STEP_TYPE_CODE_NAME.set(stepTypeMap);
                }
                return stepTypeMap.getOrDefault(String.valueOf(value), value.toString());
            case LIBRARY_ID:
                Map<String, String> libraryMap = LIBRARY_ID_NAME.get();
                if (libraryMap == null)
                {
                    libraryMap = getLibraryIdName();
                    LIBRARY_ID_NAME.set(libraryMap);
                }
                return libraryMap.getOrDefault(String.valueOf(value), value.toString());
            case TREE_ID:
                Map<String, String> treeMap = TREE_ID_NAME.get();
                if (treeMap == null)
                {
                    treeMap = getTreeIdName();
                    TREE_ID_NAME.set(treeMap);
                }
                return treeMap.getOrDefault(String.valueOf(value), value.toString());
            case REQUIREMENT_ID:
                Map<String, String> requirementMap = REQUIREMENT_ID_CODE.get();
                if (requirementMap == null)
                {
                    requirementMap = getRequirementIdCode();
                    REQUIREMENT_ID_CODE.set(requirementMap);
                }
                return requirementMap.getOrDefault(String.valueOf(value), value.toString());
            case PRODUCT_ID:
                for (Map dictMap : dictMap.get(dict))
                {
                    if (Objects.equals(String.valueOf(dictMap.get("dictKey")), String.valueOf(value)))
                    {
                        return (String) dictMap.get("dictValue");
                    }
                }
            case MODULE_FUNCTION_ID:
                for (Map dictMap : dictMap.get(dict))
                {
                    if (Objects.equals(String.valueOf(dictMap.get("dictKey")), String.valueOf(value)))
                    {
                        return (String) dictMap.get("dictValue");
                    }
                }
            case LEADING_BY:
                if (userMap == null)
                {
                    userMap = getUserIdName();
                    USER_ID_NAME.set(userMap);
                }
                return userMap.getOrDefault(String.valueOf(value), value.toString());
            case EXEC_BY:
                if (userMap == null)
                {
                    userMap = getUserIdName();
                    USER_ID_NAME.set(userMap);
                }
                return userMap.getOrDefault(String.valueOf(value), value.toString());
            case FUNCTION_POINTS_ID:
                return POINTS_ID_NAME.getOrDefault(String.valueOf(value), value.toString());
            case IS_EXAMPLES:
                Map<String, String> isExamplesMap = IS_EXAMPLES_CODE_NAME.get();
                if (isExamplesMap == null)
                {
                    isExamplesMap = getIsExamples();
                    IS_EXAMPLES_CODE_NAME.set(isExamplesMap);
                }
                return isExamplesMap.getOrDefault(String.valueOf(value), value.toString());
            case CASE_TYPE:
                Map<String, String> caseTypeMap = CASE_TYPE_CODE_NAME.get();
                if (caseTypeMap == null)
                {
                    caseTypeMap = getDictionaryCodeName(DictionaryType.CASE_TYPE);
                    CASE_TYPE_CODE_NAME.set(caseTypeMap);
                }
                return caseTypeMap.getOrDefault(String.valueOf(value), value.toString());
            case TEST_MODE:
                Map<String, String> testModeMap = TEST_MODE_CODE_NAME.get();
                if (testModeMap == null)
                {
                    testModeMap = getDictionaryCodeName(DictionaryType.TEST_MODE);
                    TEST_MODE_CODE_NAME.set(testModeMap);
                }
                StringBuilder stringBuilder = new StringBuilder();
                String[] array=String.valueOf(value).split(",");
                for (String arr : array) {
                    String testMode = testModeMap.get(arr);
                    if (stringBuilder.length() > 0) {
                        stringBuilder.append(",");  // 第一个元素前不加逗号
                    }
                    stringBuilder.append(testMode);
                }
                return stringBuilder.toString();
            default:
                return String.valueOf(value);
        }
    }


    @Override
    public String toValue(String dict, Object obj, String name, Object value)
    {
        if (value == null)
        {
            return null;
        }
        switch (dict)
        {
            case PRIORITY:
                Map<String, String> priorityMap = PRIORITY_NAME_CODE.get();
                if (priorityMap == null)
                {
                    priorityMap = getPriorityNameCode();
                    PRIORITY_NAME_CODE.set(priorityMap);
                }
                return priorityMap.getOrDefault(String.valueOf(value), "");
            case STEP_TYPE:
                Map<String, String> stepTypeMap = STEP_TYPE_NAME_CODE.get();
                if (stepTypeMap == null)
                {
                    stepTypeMap = getStepTypeNameCode();
                    STEP_TYPE_NAME_CODE.set(stepTypeMap);
                }
                return stepTypeMap.getOrDefault(String.valueOf(value), "");

            case TREE_ID:
                String[] treeNameId = String.valueOf(value).split("#");
                if (treeNameId.length == 0)
                {
                    return "";
                }
                return String.valueOf(treeNameId[treeNameId.length-1]);
            case REQUIREMENT_ID:
                Map<String, String> requirementMap = REQUIREMENT_CODE_ID.get();
                if (requirementMap == null)
                {
                    requirementMap = getRequirementCodeId();
                    REQUIREMENT_CODE_ID.set(requirementMap);
                }
                String[] requirementCodeName = String.valueOf(value).split(" ");
                if (requirementCodeName.length == 0)
                {
                    return "";
                }
                return requirementMap.getOrDefault(requirementCodeName[0], "");
            case USER_NAME:
                Map<String, String> userMap = USER_NAME_ID.get();
                if (userMap == null)
                {
                    userMap = getUserNameId();
                    USER_NAME_ID.set(userMap);
                }
                return userMap.getOrDefault(String.valueOf(value), "");
            default:
                return value.toString();
        }

    }

    /**
     * 获取测试步骤类型code->name map
     *
     * @return Map<String, String> 测试步骤类型code->name map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getStepTypeCodeName()
    {
        Map<String, String> map = ImmutableMap.<String, String>builder()
                .put("text", stepTypeNameArray[0])
                .put("subclause", stepTypeNameArray[1]).build();
        return map;
    }

    /**
     * 获取测试步骤类型code->name map
     *
     * @return Map<String, String> 测试步骤类型code->name map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getStatusCodeName()
    {
        Map<String, String> map = ImmutableMap.<String, String>builder()
                .put("system", statusCodeNameArray[0])
                .put("smoke", statusCodeNameArray[1])
                .put("block", statusCodeNameArray[2])
                .put("skip", statusCodeNameArray[3])
                .build();
        return map;
    }

    /**
     * 获取测试步骤类型name->code map
     *
     * @return Map<String, String> 测试步骤类型name->code map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getStepTypeNameCode()
    {
        Map<String, String> map = ImmutableMap.<String, String>builder()
                .put(stepTypeNameArray[0],"text")
                .put(stepTypeNameArray[1],"subclause").build();
        return map;
    }

    /**
     * 获取案例等级name->code map
     *
     * @return Map<String, String> 案例等级name->code map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getPriorityNameCode()
    {
        Map<String, String> map = ImmutableMap.<String, String>builder()
                .put(priorityNameArray[0],"1")
                .put(priorityNameArray[1],"2")
                .put(priorityNameArray[2],"3")
                .put(priorityNameArray[3],"4")
                .put(priorityNameArray[4],"5").build();
        return map;
    }

    /**
     * 获取案例等级code->name map
     *
     * @return Map<String, String> 案例等级code->name map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getPriorityCodeName()
    {
        Map<String, String> map = ImmutableMap.<String, String>builder()
                .put("1", priorityNameArray[0])
                .put("2", priorityNameArray[1])
                .put("3", priorityNameArray[2])
                .put("4", priorityNameArray[3])
                .put("5", priorityNameArray[4]).build();
        return map;
    }

    /**
     * 正反例
     *
     * @return Map<String, String> 案例等级code->name map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getIsExamples()
    {
        Map<String, String> map = ImmutableMap.<String, String>builder()
                .put("true", isExamples[0])
                .put("false", isExamples[1]).build();
        return map;
    }

    /**
     * 获取案例库id->name map
     *
     * @return Map<String, String> 产品id->name map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getLibraryIdName()
    {
        List<ProductCaseLibrary> librarys = testCaseLibraryService.list();
        return librarys.stream().collect(Collectors.toMap(item -> String.valueOf(item.getId()),
                item -> item.getName()));
    }

    /**
     * 获取测试案例树id->name map
     *
     * @return Map<String, String> 获取测试案例树id->name map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getTreeIdName()
    {
        List<ProductCaseTree> trees = testCaseTreeService.list();
        return trees.stream().collect(Collectors.toMap(item -> String.valueOf(item.getId()), item -> item.getName() + "#" + item.getId()));
    }

    /**
     * 获取测试案例树id->name map
     *
     * @return Map<String, String> 获取测试案例树id->name map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getDictionaryCodeName(String type)
    {
        List<Dictionary> dictionaryList = dictionaryApi.query(Dictionary.builder().type(type).build());
        return dictionaryList.stream().collect(Collectors.toMap(item -> item.getCode(), item -> item.getName()));
    }

    /**
     * 获取需求id->code map
     *
     * @return Map<String, String> 需求id->code map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getRequirementIdCode()
    {
        List<Requirement> requirements = requirementApi.selectRequirementByCondition(Requirement.builder().build());
        return requirements.stream().collect(Collectors.toMap(item -> String.valueOf(item.getId()), item -> item.getCode() + " " + item.getName()));
    }

    /**
     * 获取需求code->id map
     *
     * @return Map<String, String> 需求code->id map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getRequirementCodeId()
    {
        List<Requirement> requirements = requirementApi.selectRequirementByCondition(Requirement.builder().build());
        return requirements.stream().collect(Collectors.toMap(Requirement::getCode, item -> String.valueOf(item.getId())));
    }

    /**
     * 获取用户id->name map
     *
     * @return Map<String, String> 用户id->name map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getUserIdName()
    {
        List<User> users = userApi.findUserListByQuery(User.builder().build());
        return users.stream().collect(Collectors.toMap(item -> String.valueOf(item.getId()), item -> item.getName() + "(" + item.getAccount() + ")"));
    }

    /**
     * 获取用户name->id map
     *
     * @return Map<String, String> 用户name->id map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    private Map<String, String> getUserNameId()
    {
        List<User> users = userApi.findUserListByQuery(User.builder().build());
        return users.stream().collect(Collectors.toMap(item -> item.getName() + "(" + item.getAccount() + ")", item -> String.valueOf(item.getId())));
    }

    /**
     * 获取用户name->id map
     *
     * @return Map<String, String> 用户name->id map
     * <AUTHOR>
     * @date 2021/12/10 11:42
     * @update zxy 2021/12/10 11:42
     * @since 1.0
     */
    public Map<String, String> setFunctionPoints(List<Long> moduleFunctionIds)
    {
        List<TestRequirementFunctionPoints> pointsList = testRequirementFunctionPointsService.list(Wraps.<TestRequirementFunctionPoints>lbQ().in(TestRequirementFunctionPoints::getFunctionId, moduleFunctionIds));
        Map<String, String> map =pointsList.stream().collect(Collectors.toMap(item -> String.valueOf(item.getId()),
                item -> item.getFunctionPoint()));
        POINTS_ID_NAME.putAll(map);
        return POINTS_ID_NAME;
    }

    /**
     * 清理ThreadLocal
     *
     * <AUTHOR>
     * @date 2021/12/10 11:50
     * @update zxy 2021/12/10 11:50
     * @since 1.0
     */
    public void removeThreadLocal()
    {
        PRIORITY_CODE_NAME.remove();
        PRIORITY_NAME_CODE.remove();

        LIBRARY_ID_NAME.remove();

        TREE_ID_NAME.remove();

        REQUIREMENT_ID_CODE.remove();
        REQUIREMENT_CODE_ID.remove();

        USER_ID_NAME.remove();
        USER_NAME_ID.remove();

        CASE_TYPE_CODE_NAME.remove();
        TEST_MODE_CODE_NAME.remove();
        IS_EXAMPLES_CODE_NAME.remove();
        STATUS_CODE_NAME.remove();
    }

    public void setProductAndFunction(List<ProductInfo> productInfos, List<ProductModuleFunction> moduleFunctions)
    {
        if (productInfos != null && !productInfos.isEmpty())
        {
            List<Map> dict = productInfos.stream().map(productInfo ->
            {
                Map<String, Object> dictMap = new HashMap<>();
                dictMap.put("dictKey", String.valueOf(productInfo.getId()));
                dictMap.put("dictValue", productInfo.getName());
                return dictMap;
            }).collect(Collectors.toList());
            dictMap.put("productId", dict);
        }
        if (moduleFunctions != null && !moduleFunctions.isEmpty())
        {
            List<Map> dict = moduleFunctions.stream().map(moduleFunction ->
            {
                Map<String, Object> dictMap = new HashMap<>();
                dictMap.put("dictKey", String.valueOf(moduleFunction.getId()));
                dictMap.put("dictValue", moduleFunction.getName());
                return dictMap;
            }).collect(Collectors.toList());
            dictMap.put("moduleFunctionId", dict);
        }

    }
}
