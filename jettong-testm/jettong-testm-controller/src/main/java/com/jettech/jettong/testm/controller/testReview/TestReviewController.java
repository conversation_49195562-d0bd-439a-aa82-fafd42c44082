package com.jettech.jettong.testm.controller.testReview;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.exception.code.ExceptionCode;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.alm.api.WorkflowApi;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.workflow.dto.CountersignResultDTO;
import com.jettech.jettong.alm.workflow.dto.WorkflowCountersignRecordDTO;
import com.jettech.jettong.alm.workflow.dto.WorkflowItemDTO;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;
import com.jettech.jettong.base.api.DictionaryApi;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.base.enumeration.dictionary.DictionaryRelationBizType;
import com.jettech.jettong.common.constant.StateCodeConstants;
import com.jettech.jettong.common.enumeration.CountersignResult;
import com.jettech.jettong.base.api.FileApi;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.common.constant.TaskTypeCodeConstants;
import com.jettech.jettong.testm.dto.*;
import com.jettech.jettong.testm.entity.*;
import com.jettech.jettong.testm.service.*;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.R;
import com.jettech.jettong.testm.vo.TestInformVO;
import com.jettech.jettong.testm.vo.TestReviewVO;
import com.jettech.jettong.testm.vo.TestSchemeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.jettech.basic.annotation.security.PreAuth;

import java.util.*;
import java.util.stream.Collectors;

import static com.jettech.jettong.common.constant.SwaggerConstants.DATA_TYPE_LONG;
import static com.jettech.jettong.common.constant.SwaggerConstants.PARAM_TYPE_PATH;


/**
 * 评审管理表控制器
 * <AUTHOR>
 * @version 1.0
 * @description 评审管理表控制器
 * @projectName jettong
 * @package com.jettech.jettong.testm.controller
 * @className TestReviewController
 * @date 2025-09-06
 * @copyright 2020 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/testReview")
@Api(value = "TestReview", tags = "评审管理表")
@PreAuth(replace = "testm:testReview:")
@RequiredArgsConstructor
public class TestReviewController extends SuperController<TestReviewService, Long, TestReview, TestReviewPageQuery, TestReviewSaveDTO, TestReviewUpdateDTO>
{

    private final FileApi fileApi;
    private final TestReviewLinkedDataService testReviewLinkedDataService;
    private final EchoService echoService;
    private final WorkflowApi workflowApi;
    private final TestRequirementFunctionPointsService testRequirementFunctionPointsService;
    private final TestProductCaseService testProductCaseService;
    private final TestSchemeService testSchemeService;
    private final TestInformService testInformService;
    private final DictionaryApi dictionaryApi;

    @Value("${testm.testReview.uploadFile:true}")
    private Boolean uploadReviewFile;

    @Override
    public R<TestReview> handlerSave(TestReviewSaveDTO model)
    {
        TestReview review = BeanPlusUtil.toBean(model, TestReview.class);
        baseService.save(review);

        return success(review);
    }

    @Override
    public IPage<TestReview> query(
            @RequestBody @Validated PageParams<TestReviewPageQuery> params) {

        IPage<TestReview> page = params.buildPage(TestReview.class);
        TestReviewPageQuery model = params.getModel();

        LbqWrapper<TestReview> wrapper = Wraps.lbQ();

        wrapper.eq(TestReview::getCode, model.getCode())
                .like(TestReview::getName, model.getName())
                .eq(TestReview::getTypeCode, model.getTypeCode())
                .eq(TestReview::getReviewMethod, model.getReviewMethod())
                .eq(TestReview::getCurrentStage,model.getReviewContent())
                .like(TestReview::getReviewContent,model.getReviewContent())
                .eq(TestReview::getStateCode,model.getStateCode())
                .eq(TestReview::getReviewType,model.getReviewType())
                .eq(TestReview::getHandleBy,model.getHandleBy())
                .eq(TestReview::getProjectId,model.getProjectId())
                .eq(TestReview::getTestreqId,model.getTestreqId())
                .eq(TestReview::getExpectedDeadline,model.getExpectedDeadline())
                .eq(TestReview::getUpdateAssociatedData,model.getUpdateAssociatedData())
                .exists(model.getCountersignStatus()!=null && model.getCountersignStatus()==1,"select id from workflow_countersign_record where status='PENDING' and biz_id=test_review.id and user_id ="+getUserId())
                .exists(model.getCountersignStatus()!=null && model.getCountersignStatus()==2,"select id from workflow_countersign_record where status !='PENDING' and biz_id=test_review.id and user_id ="+getUserId());

        WorkflowItemDTO workItem = new WorkflowItemDTO();
        workItem.setBizType(TypeClassify.TEST_REVIEW);
        workItem.setTypeCode(model.getTypeCode());

        baseService.page(page, wrapper);
        if(CollUtil.isEmpty(page.getRecords())){
            return page;
        }
        Map<String,String> startCodeMap = new HashMap();
        page.getRecords().stream().map(TestReview::getTypeCode).distinct().forEach(n->{
            workItem.setTypeCode(n);
            startCodeMap.put(n,workflowApi.findStartStateCode(workItem));
        });
        page.getRecords().forEach(item->
        {
            Long userId = getUserId();
            item.setIsProcess(userId + StrUtil.DASHED + TypeClassify.TEST_REVIEW +StrUtil.DASHED+ item.getId());
            if(startCodeMap.get(item.getTypeCode()).equals(item.getStateCode())){
                item.setIsStartNode(Boolean.TRUE);
            }else{
                item.setIsStartNode(Boolean.FALSE);
            }
            item.setIsUploadReviewFile(uploadReviewFile);

        });
        echoService.action(page);

        return page;
    }

    @Override
    public R<TestReview> handlerUpdate(TestReviewUpdateDTO model) {
        TestReview testReview = BeanPlusUtil.toBean(model, TestReview.class);
//        TestReview byId = baseService.getById(model.getId());
        baseService.updateById(testReview);

        // 更新附件文件
        Long testReviewId = model.getId();
        List<File> newFiles = model.getFiles();

        // 注意：这里判空，不能判集合是否有元素。集合无元素代表，删除原来的关联关系
        if (newFiles != null) {
            // 删除原来的附件关系
            List<File> oldFile = fileApi.findByBizTypeAndBizId(FileBizType.TEST_REVIEW_FILE_UPLOAD, testReviewId);
            oldFile.forEach(file -> file.setBizId(null));
            fileApi.updateBatchById(oldFile);

            // 建立新的 附件关系
            newFiles.forEach(file -> file.setBizId(testReviewId).setBizType(FileBizType.TEST_REVIEW_FILE_UPLOAD));
            fileApi.updateBatchById(newFiles);
        }

        List<Long> newIds = model.getBizIds();
        if(newIds == null && newIds.isEmpty()){
            testReviewLinkedDataService.remove(Wraps.<TestReviewLinkedData>lbQ().eq(TestReviewLinkedData::getReviewId,testReviewId));
            return R.success(testReview);
        }

        if(!model.getReviewType().equals(testReview.getReviewType())){
            testReviewLinkedDataService.remove(Wraps.<TestReviewLinkedData>lbQ().eq(TestReviewLinkedData::getReviewId,testReviewId));
            List<TestReviewLinkedData> testReviewLinkedDataList = newIds.stream().map(id->TestReviewLinkedData.builder()
                            .bizId(id).bizType(testReview.getReviewType()).reviewId(testReviewId).build())
                    .collect(Collectors.toList());
            testReviewLinkedDataService.saveBatch(testReviewLinkedDataList,500);
        }else{
            // 查询现有关联记录
            List<Long> existingIds = testReviewLinkedDataService.listObjs(
                    Wraps.<TestReviewLinkedData>lbQ()
                            .select(TestReviewLinkedData::getBizId)
                            .eq(TestReviewLinkedData::getReviewId, testReviewId),
                    Object::toString
            ).stream().map(Long::valueOf).collect(Collectors.toList());
            // 计算需删除的旧记录
            List<Long> idsToRemove = existingIds.stream()
                    .filter(id -> !newIds.contains(id))
                    .collect(Collectors.toList());
            if (!idsToRemove.isEmpty()) {
                testReviewLinkedDataService.remove(Wraps.<TestReviewLinkedData>lbQ()
                        .in(TestReviewLinkedData::getBizId, idsToRemove)
                        .eq(TestReviewLinkedData::getReviewId, testReviewId));
            }

            // 计算需新增的记录
            List<TestReviewLinkedData> itemsToInsert = newIds.stream()
                    .filter(id -> !existingIds.contains(id))
                    .map(id -> TestReviewLinkedData.builder()
                            .bizType(testReview.getReviewType()).bizId(id).reviewId(testReviewId).build())
                    .collect(Collectors.toList());
            if (!itemsToInsert.isEmpty()) {
                // 分批次插入
                testReviewLinkedDataService.saveBatch(itemsToInsert, 500);
            }

        }

        return R.success(testReview);
    }

    @Override
    public R<Boolean> handlerDelete(List<Long> ids)
    {
        baseService.removeByIds(ids);
        testReviewLinkedDataService.remove(Wraps.<TestReviewLinkedData>lbQ()
                .in(TestReviewLinkedData::getReviewId, ids));

        return success();
    }


    @Override
    public R<TestReview> get(Long id)
    {
        TestReview testReview = baseService.getById(id);
        if (testReview == null) {
            throw BizException.wrap(ExceptionCode.NOT_FOUND);
        }
        Long userId = getUserId();
        testReview.setIsProcess(userId + StrUtil.DASHED + TypeClassify.TEST_REVIEW + StrUtil.DASHED + testReview.getId());

        List<File> caseFiles =
                fileApi.findByBizTypeAndBizId(FileBizType.TEST_REVIEW_FILE_UPLOAD, testReview.getId());
        testReview.setFiles(caseFiles);

        List<File> reviewRecordFiles =
                fileApi.findByBizTypeAndBizId(FileBizType.TEST_REVIEW_REPORT_FILE_UPLOAD, testReview.getId());
        testReview.setReviewFiles(reviewRecordFiles);


        List<Long> bizIds = testReviewLinkedDataService.list(Wraps.<TestReviewLinkedData>lbQ()
                .in(TestReviewLinkedData::getReviewId, testReview.getId())).stream()
                .map(TestReviewLinkedData::getBizId).collect(Collectors.toList());

        if(bizIds.isEmpty()){
            echoService.action(testReview);
            return success(testReview);
        }

        if(TaskTypeCodeConstants.TASK_TEST_ANAL.equals(testReview.getReviewType())){

            List<TestRequirementFunctionPoints> pointsList = testRequirementFunctionPointsService.list(Wraps.<TestRequirementFunctionPoints>lbQ()
                    .in(TestRequirementFunctionPoints::getId, bizIds));

            echoService.action(pointsList);
            testReview.setLinkedData(pointsList);

        }else if(TaskTypeCodeConstants.TASK_TEST_PLAN.equals(testReview.getReviewType())){

            List<TestScheme> schemeList = testSchemeService.list(Wraps.<TestScheme>lbQ()
                    .in(TestScheme::getId, bizIds));

            List<TestSchemeVO> list = new ArrayList<>();
            for(TestScheme vo:schemeList){
                TestSchemeVO testSchemeVO= BeanUtil.toBean(vo, TestSchemeVO.class);
                list.add(testSchemeVO);
            }
            echoService.action(list);

            testReview.setLinkedData(list);
        }else if(TaskTypeCodeConstants.TASK_CASE_DESIGN.equals(testReview.getReviewType())){
            List<TestProductCase> caseList = testProductCaseService.list(Wraps.<TestProductCase>lbQ()
                    .in(TestProductCase::getId, bizIds));
            // 批量获取所有ID
            List<Long> caseIds = caseList.stream()
                    .map(TestProductCase::getId)
                    .collect(Collectors.toList());

            // 假设存在批量查询方法
            Map<Long, List<Dictionary>> dictionaryMap = dictionaryApi.batchEchoDictionaryByBizIdsAndType(
                    caseIds,DictionaryRelationBizType.CASE.getCode());

            caseList.forEach(item->{
                List<Dictionary> dictionaries = Optional.ofNullable(
                        dictionaryMap.get(item.getId())
                ).orElse(Collections.emptyList());

                if (!dictionaries.isEmpty()) {
                    // 流式处理获取测试模式列表
                    List<String> testModes = dictionaries.stream()
                            .map(Dictionary::getCode)
                            .collect(Collectors.toList());

                    item.setTestModes(testModes);
                    item.getEchoMap().put("testModes", dictionaries);
                }

            });

            echoService.action(caseList);
            testReview.setLinkedData(caseList);
        }else if(TaskTypeCodeConstants.TASK_TEST_REPORT.equals(testReview.getReviewType())){
            List<TestInform> testInformList = testInformService.list(Wraps.<TestInform>lbQ()
                    .in(TestInform::getId, bizIds));

            List<TestInformVO> list = new ArrayList<>();
            for(TestInform vo:testInformList){
                TestInformVO testInformVO= BeanUtil.toBean(vo, TestInformVO.class);
                list.add(testInformVO);
            }
            echoService.action(list);
            testReview.setLinkedData(list);
        }

        echoService.action(testReview);
        return success(testReview);
    }


    @ApiOperation(value = "查询评审管理可流转的状态节点", notes = "查询评审管理可流转的状态节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "testReviewId", value = "评审id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @GetMapping("/findNextNode/{testReviewId}")
    public R<List<WorkflowNode>> findNextNode(@PathVariable Long testReviewId,
                                              @RequestParam(value = "instanceId",required = false) Long instanceId)
    {

        TestReview review = baseService.getById(testReviewId);
        WorkflowItemDTO workItem = new WorkflowItemDTO();
        workItem.setId(review.getId());
        workItem.setInstanceId(instanceId);
        workItem.setStateCode(review.getStateCode());
        workItem.setBizType(TypeClassify.TEST_REVIEW);
        workItem.setTypeCode(review.getTypeCode());
        workItem.setHandleBy(review.getHandleBy());

        List<WorkflowNode> nextNodes = workflowApi.findNextNode(workItem);
        return success(nextNodes);
    }

    @ApiOperation(value = "评审办理", notes = "评审办理")
    @PostMapping("/testReviewProcess")
    @SysLog(value = "评审办理", request = false)
    public R<TestReview> testReviewProcess(@RequestBody TestReviewVO testReviewVo)
    {

        TestReview review = baseService.getById(testReviewVo.getReviewId());
        WorkflowCountersignRecordDTO recordDTO = new WorkflowCountersignRecordDTO();

        recordDTO.setTypeCode(review.getTypeCode());
        recordDTO.setStateCode(testReviewVo.getStateCode());
        recordDTO.setTargetStateCode(testReviewVo.getTargetStateCode());
        recordDTO.setInstanceId(testReviewVo.getInstanceId());
        recordDTO.setUserIds(testReviewVo.getUserIds());
        recordDTO.setOpinion(testReviewVo.getOpinion());
        recordDTO.setCreateTime(review.getCreateTime());
        recordDTO.setBizId(review.getId());
        recordDTO.setBizType(TypeClassify.TEST_REVIEW);

        if(CountersignResult.AGREE.getCode().equals(testReviewVo.getResult())){
            recordDTO.setResult(CountersignResult.AGREE);
        }else{
            recordDTO.setResult(CountersignResult.REJECT);
        }

        CountersignResultDTO resultDTO = workflowApi.submitCountersign(recordDTO);
        if (resultDTO==null){
            return R.fail("提交会签结果失败");
        }

        if(resultDTO.isCompleted()){
            review.setStateCode(resultDTO.getTargetStateCode());
        }
        if(uploadReviewFile){
            List<File> reviewFiles = testReviewVo.getReviewFiles();
            if (reviewFiles != null && !reviewFiles.isEmpty())
            {
                reviewFiles.forEach(item -> item.setBizType(FileBizType.TEST_REVIEW_REPORT_FILE_UPLOAD).setBizId(testReviewVo.getReviewId()));
                fileApi.updateBatchById(reviewFiles);
            }
        }
        baseService.updateById(review);

        if(review.getUpdateAssociatedData() == true){
            // 查询结束节点状态
            WorkflowItemDTO workItem = new WorkflowItemDTO();
            workItem.setId(review.getId());
            workItem.setBizType(TypeClassify.TEST_REVIEW);
            workItem.setTypeCode(review.getTypeCode());
            String stateCode = workflowApi.findEndStateCode(workItem);
            if(stateCode.equals(resultDTO.getTargetStateCode())){

                List<Long> linkedDataIds = testReviewLinkedDataService.list(Wraps.<TestReviewLinkedData>lbQ()
                        .in(TestReviewLinkedData::getReviewId, review.getId())).stream().map(TestReviewLinkedData::getBizId)
                        .collect(Collectors.toList());

                if(TaskTypeCodeConstants.TASK_TEST_ANAL.equals(review.getReviewType())){

                    testRequirementFunctionPointsService.update(Wraps.<TestRequirementFunctionPoints>lbU().set(TestRequirementFunctionPoints::getStateCode,
                                    StateCodeConstants.REVIEW_DONE).in(TestRequirementFunctionPoints::getId, linkedDataIds));

                }else if(TaskTypeCodeConstants.TASK_TEST_PLAN.equals(review.getReviewType())){

                    testSchemeService.update(Wraps.<TestScheme>lbU().set(TestScheme::getStateCode,
                            StateCodeConstants.REVIEW_DONE).in(TestScheme::getId, linkedDataIds));

                }else if(TaskTypeCodeConstants.TASK_CASE_DESIGN.equals(review.getReviewType())){

                    testProductCaseService.update(Wraps.<TestProductCase>lbU().set(TestProductCase::getReviewStatus,
                            StateCodeConstants.REVIEW_STATUS).in(TestProductCase::getId, linkedDataIds));

                }else if(TaskTypeCodeConstants.TASK_TEST_REPORT.equals(review.getReviewType())){

                    testInformService.update(Wraps.<TestInform>lbU().set(TestInform::getStateCode,
                            StateCodeConstants.REVIEW_DONE).in(TestInform::getId, linkedDataIds));
                }
            }
        }

        return R.success(review);
    }


    @ApiOperation(value = "评审废弃", notes = "评审废弃")
    @PostMapping("/testReviewDisuse")
    @SysLog(value = "评审废弃", request = false)
    public R<TestReview> testReviewDisuse(@RequestBody TestReviewVO testReviewVo)
    {

        TestReview review = baseService.getById(testReviewVo.getReviewId());
        if(!ContextUtil.getUserId().equals(review.getHandleBy()) && !ContextUtil.getUserId().equals(review.getCreatedBy())){
            return fail("对不起，您没有操作权限！");
        }
        WorkflowCountersignRecordDTO recordDTO = new WorkflowCountersignRecordDTO();
        recordDTO.setTypeCode(review.getTypeCode());
        recordDTO.setStateCode(testReviewVo.getStateCode());
        recordDTO.setTargetStateCode(StateCodeConstants.YFQ);
        recordDTO.setSkipCountersign(true);
        recordDTO.setInstanceId(testReviewVo.getInstanceId());
        recordDTO.setUserIds(testReviewVo.getUserIds());
        recordDTO.setOpinion(testReviewVo.getOpinion());
        recordDTO.setCreateTime(review.getCreateTime());
        recordDTO.setBizId(review.getId());
        recordDTO.setBizType(TypeClassify.TEST_REVIEW);

//        if(CountersignResult.AGREE.getCode().equals(testReviewVo.getResult())){
//            recordDTO.setResult(CountersignResult.AGREE);
//        }else{
//            recordDTO.setResult(CountersignResult.REJECT);
//        }

        CountersignResultDTO resultDTO = workflowApi.submitCountersign(recordDTO);
        if (resultDTO==null){
            return R.fail("废弃失败");
        }

        if(resultDTO.isCompleted()){
            review.setStateCode(resultDTO.getTargetStateCode());
        }

        baseService.updateById(review);

        return R.success(review);
    }

}
