package com.jettech.jettong.testm.controller.poi;

import cn.afterturn.easypoi.excel.entity.result.ExcelVerifyHandlerResult;
import cn.afterturn.easypoi.handler.inter.IExcelVerifyHandler;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.vo.TestProductCaseImportExcelVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 测试案例导入验证器，该方法中不要使用大批量的查询数据库操作
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例导入验证器，该方法中不要使用大批量的查询数据库操作
 * @projectName jettong
 * @package com.jettech.jettong.testm.controller.poi
 * @className TestCaseExcelVerifyHandlerImpl
 * @date 2022/01/17 16:00
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Component
@RequiredArgsConstructor
public class TestProductCaseExcelVerifyHandlerImpl implements IExcelVerifyHandler<TestProductCaseImportExcelVO>
{

    private Map<Long, Long> functionToProductMap;
    private Set<String> functionPointNames;

    @Override
    public ExcelVerifyHandlerResult verifyHandler(TestProductCaseImportExcelVO obj)
    {
        StringBuilder builder = new StringBuilder();
        boolean bool = true;

//        if (StringUtil.isEmpty(obj.getTestStep()))
//        {
//            builder.append("步骤不能为空");
//            bool = false;
//        }
//        if (StringUtil.isEmpty(obj.getExpectedResult()))
//        {
//            builder.append("预期结果不能为空");
//            bool = false;
//        }
        return new ExcelVerifyHandlerResult(bool, builder.toString());
    }

    public void init(List<ProductModuleFunction> functions) {
        this.functionToProductMap = functions.stream()
                .collect(Collectors.toMap(ProductModuleFunction::getId, ProductModuleFunction::getProductId));
        this.functionPointNames = new HashSet<>();
    }

}
