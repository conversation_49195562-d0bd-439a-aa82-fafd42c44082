package com.jettech.jettong.testm.controller.testPlan;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.utils.TreeUtil;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.base.api.FileApi;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.testm.dao.TestCaseMapper;
import com.jettech.jettong.testm.dto.*;
import com.jettech.jettong.testm.entity.*;
import com.jettech.jettong.testm.service.*;
import com.jettech.jettong.testm.vo.CaseAndTreeReturnVo;
import com.jettech.jettong.testm.vo.GroupForUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.jettech.jettong.common.constant.SwaggerConstants.DATA_TYPE_LONG_ARRAY;
import static com.jettech.jettong.common.constant.SwaggerConstants.PARAM_TYPE_QUERY;


/**
 * 测试计划用例表控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试计划用例表控制器
 * @projectName jettong
 * @package com.jettech.jettong.testm.controller
 * @className TestPlanCaseController
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/testPlanCase")
@Api(value = "TestPlanCase", tags = "测试计划用例表")
@RequiredArgsConstructor
public class TestPlanCaseController extends
        SuperController<TestPlanCaseService, Long, TestPlanCase, TestPlanCasePageQuery, TestPlanCaseSaveDTO,
                TestPlanCaseUpdateDTO>
{

    private final EchoService echoService;
    private final TestCaseService testCaseService;
    private final TestCaseMapper testCaseMapper;
    private final TestPlanCaseResultService testPlanCaseResultService;
    private final UserApi userApi;
    private final TestProductCaseService testProductCaseService;
    private final ProductCaseTreeService productCaseTreeService;
    private final TestPlanCaseService testPlanCaseService;
    private final TestProductCaseHistoryService testProductCaseHistoryService;
    private final TestPlanService testPlanService;
    private final TestPlanCaseTreeService testPlanCaseTreeService;
    private final TestProductCaseArchiveService testProductCaseArchiveService;
    private final TestPlanGroupTreeService testPlanGroupTreeService;
    private final TestPlanGroupTreeOrgService testPlanGroupTreeOrgService;
    private final FileApi fileApi;
    @Override
    public R<TestPlanCase> handlerSave(TestPlanCaseSaveDTO model)
    {
        if(!Objects.equals(model.getTestcaseTreeIds(), null) && (model.getTestcaseTreeIds().size() != 0)) {
            //当用户选择了叶级树级下的部分用例后又勾选中了叶级树级（选了全部）将进行去重
            LbqWrapper<TestCase> queryWrapTreeLbuWrapper = Wraps.lbQ();
            List<TestCase> testCasesList = testCaseService.list(queryWrapTreeLbuWrapper);
            List<Long> testCaseIdsList = testCasesList.stream().map(TestCase::getId).collect(Collectors.toList());
            testCaseIdsList.addAll(model.getTestcaseIds());
            List<Long> alltestCaseIdsList = testCaseIdsList.stream().distinct().collect(Collectors.toList());
            //去重完毕，现在包含全部非重复的用例
            model.setTestcaseIds(alltestCaseIdsList);
        }
        LbqWrapper<TestPlanCase> queryWrapLbuWrapper = Wraps.lbQ();
        queryWrapLbuWrapper.eq(TestPlanCase::getPlanId, model.getPlanId());
        List<TestPlanCase> list = baseService.list(queryWrapLbuWrapper);
        List<Long> testCaseIds = list.stream().filter(a -> model.getTestcaseIds().contains(a.getTestcaseId()))
                .map(TestPlanCase::getTestcaseId).collect(Collectors.toList());
        if (testCaseIds.size() != 0)
        {
            List<TestCase> testCases = testCaseMapper.selectBatchIds(testCaseIds);
            List<String> testCaseName = testCases.stream().map(TestCase::getName).collect(Collectors.toList());
            String testCaseNameStr = testCaseName.stream().map(String::valueOf).collect(Collectors.joining(","));
            return R.fail(testCaseNameStr + "用例重复添加！");
        }

        List<TestPlanCase> testPlanCaseList = new ArrayList<>();
        for (Long testCaseId : model.getTestcaseIds())
        {
            TestProductCase byId = testProductCaseService.getById(testCaseId);
            TestPlanCase testPlanCase = new TestPlanCase();
            testPlanCase.setTestcaseId(testCaseId);
            testPlanCase.setPlanId(model.getPlanId());
            testPlanCase.setCreatedBy(model.getCreatedBy());
            testPlanCase.setId(UidGeneratorUtil.getId());
            testPlanCase.setName(byId.getName());
            testPlanCase.setPriority(byId.getPriority());
            testPlanCase.setCreateTime(LocalDateTime.now());
            testPlanCase.setVersion(byId.getVersion());
            testPlanCase.setCaseKey(byId.getCaseKey());
            testPlanCase.setTreeId(byId.getTreeId());
            testPlanCaseList.add(testPlanCase);
        }
        baseService.saveBatch(testPlanCaseList);
        return R.success(null, "保存测试计划下用例成功！");
    }

    @ApiOperation(value = "删除测试计划下用例", notes = "删除测试计划下用例")
    @DeleteMapping("/deleteTestPlanCase")
    @SysLog(value = "删除测试计划下用例", request = false)
    @Transactional
    public R<Boolean> deleteTestPlanCase(@RequestBody TestPlanCaseDelDTO testPlanCaseDelDTO)
    {
        //根据testPlanCaseDelDTO中的planId和testcaseIds查询出所有的testPlanCase
        List<TestPlanCase> testPlanCaseListBefore = baseService.list(Wraps.<TestPlanCase>lbQ().eq(TestPlanCase::getPlanId, testPlanCaseDelDTO.getPlanId())
                .in(TestPlanCase::getTestcaseId,testPlanCaseDelDTO.getTestCaseIds()));
        baseService.remove(Wraps.<TestPlanCase>lbQ().eq(TestPlanCase::getPlanId, testPlanCaseDelDTO.getPlanId())
                .in(TestPlanCase::getTestcaseId,testPlanCaseDelDTO.getTestCaseIds()));
        if(!Objects.equals(testPlanCaseDelDTO.getTestCaseIds().size(),0) && !Objects.equals(testPlanCaseDelDTO.getTestCaseIds().size(),null)){
            testPlanCaseResultService.remove(Wraps.<TestPlanCaseResult>lbQ().eq(TestPlanCaseResult::getPlanId, testPlanCaseDelDTO.getPlanId())
                    .in(TestPlanCaseResult::getTestcaseId,testPlanCaseDelDTO.getTestCaseIds()));
        }
        if(testPlanCaseListBefore!=null && testPlanCaseListBefore.size()>0){
            Long treeId = testPlanCaseListBefore.get(0).getTreeId();
            List<Long> treeIds = new ArrayList<>();
            //删除后查看该计划下是否还有该树级下的用例，如果没有记录带删除中
            List<TestPlanCase> testPlanCaseListAfter = baseService.list(Wraps.<TestPlanCase>lbQ().eq(TestPlanCase::getPlanId, testPlanCaseDelDTO.getPlanId())
                    .eq(TestPlanCase::getTreeId, treeId));
            if(testPlanCaseListAfter==null || testPlanCaseListAfter.size()==0){
                boolean flag = true;
                List<TestPlanCaseTree> list1 = testPlanCaseTreeService.list(Wraps.<TestPlanCaseTree>lbQ().eq(TestPlanCaseTree::getParentId, treeId).eq(TestPlanCaseTree::getPlanId, testPlanCaseDelDTO.getPlanId()));
                //若有子目录存在，则不删除，给flag
                if(list1.size()==0){
                    treeIds.add(treeId);
                }else {
                    flag = false;
                }
                while (flag) {
                    TestPlanCaseTree byId = testPlanCaseTreeService.getOne(Wraps.<TestPlanCaseTree>lbQ().eq(TestPlanCaseTree::getTreeId, treeId).eq(TestPlanCaseTree::getPlanId, testPlanCaseDelDTO.getPlanId()));
                    treeId = byId.getParentId();
                    if(byId.getParentId()!=0) {
                        List<TestPlanCase> testPlanCase = baseService.list(Wraps.<TestPlanCase>lbQ().eq(TestPlanCase::getPlanId, testPlanCaseDelDTO.getPlanId())
                                .eq(TestPlanCase::getTreeId, treeId));
                        if(testPlanCase==null||testPlanCase.size()==0){
                            List<TestPlanCaseTree> list = testPlanCaseTreeService.list(Wraps.<TestPlanCaseTree>lbQ().eq(TestPlanCaseTree::getTreeId, treeId).eq(TestPlanCaseTree::getPlanId, testPlanCaseDelDTO.getPlanId()));
                            //若有子目录存在，则不删除，给flag //顶级目录不删除
                            if(list.size()==1&&list.get(0).getParentId()!=0){
                                treeIds.add(treeId);
                            }else {
                                flag = false;
                            }
                        }else {
                            flag = false;
                        }
                    }else {
                        flag = false;
                    }
                }
            }
            if(treeIds.size()>0){
                testPlanCaseTreeService.remove(Wraps.<TestPlanCaseTree>lbQ().eq(TestPlanCaseTree::getPlanId, testPlanCaseDelDTO.getPlanId())
                        .in(TestPlanCaseTree::getTreeId,treeIds));
            }
        }
        return R.success(null, "取消关联用例成功！");
    }


    @ApiOperation(value = "删除测试计划用例树下用例", notes = "删除测试计划用例树下用例")
    @DeleteMapping("/deleteTestPlanCaseByTreeId")
    @SysLog(value = "删除测试计划用例树下用例", request = false)
    @Transactional
    public R<Boolean> deleteTestPlanCaseByTreeId(@RequestParam Long treeId,@RequestParam Long planId)
    {
        List<TestPlanCaseTree> allTreeIds = testPlanCaseTreeService
                .list(Wraps.<TestPlanCaseTree>lbQ().eq(TestPlanCaseTree::getPlanId, planId));
        //allTreeIds根据treeId获取分组为map
        Map<Long, List<TestPlanCaseTree>> map = allTreeIds.stream()
                .collect(Collectors.groupingBy(TestPlanCaseTree::getParentId));
        List<TestPlanCaseTree> treeList = testPlanCaseTreeService.list(Wraps.<TestPlanCaseTree>lbQ().eq(TestPlanCaseTree::getPlanId, planId)
                .eq(TestPlanCaseTree::getTreeId, treeId));
        List<Long> treeIds = new ArrayList<>();
        //查询所有下级目录及下级子目录
        while (treeList!=null && treeList.size()!=0){
            List<TestPlanCaseTree> treeLists = new ArrayList<>();
            for (TestPlanCaseTree testPlanCaseTree : treeList) {
                if(testPlanCaseTree.getParentId()!=0){
                    treeIds.add(testPlanCaseTree.getTreeId());
                }
                if(map.get(testPlanCaseTree.getTreeId())!=null){
                    treeList = map.get(testPlanCaseTree.getTreeId());
                    treeLists.addAll(treeList);
                }else {
                    treeList = new ArrayList<>();
                }
            }
            treeList = treeLists;
        }
        if(treeIds.size()==0){
            return R.fail("无用树目录");
        }

        boolean flag = true;

        while (flag) {
            //查询上级目录
            TestPlanCaseTree byId = testPlanCaseTreeService.getOne(Wraps.<TestPlanCaseTree>lbQ().eq(TestPlanCaseTree::getTreeId, treeId).eq(TestPlanCaseTree::getPlanId, planId));
            if(byId.getParentId()==0) {
                flag = false;
            }else {
                treeId = byId.getParentId();
                List<TestPlanCase> testPlanCase = baseService.list(Wraps.<TestPlanCase>lbQ().eq(TestPlanCase::getPlanId, planId)
                        .eq(TestPlanCase::getTreeId, treeId));
                if (testPlanCase == null || testPlanCase.size() == 0) {
                    List<TestPlanCaseTree> list = testPlanCaseTreeService.list(Wraps.<TestPlanCaseTree>lbQ().eq(TestPlanCaseTree::getTreeId, treeId).eq(TestPlanCaseTree::getPlanId, planId));
                    //若有子目录存在，则不删除，给flag //顶级目录不删除
                    if (list.size() == 1 && list.get(0).getParentId() != 0) {
                        treeIds.add(treeId);
                    } else {
                        flag = false;
                    }
                } else {
                    flag = false;
                }
            }

        }
        //根据treeId查询出所有子集treeId
        LbqWrapper<TestPlanCase> queryWrapTreeLbuWrapper = Wraps.lbQ();
        queryWrapTreeLbuWrapper.in(TestPlanCase::getTreeId, treeIds)
                .eq(TestPlanCase::getPlanId, planId);
        List<TestPlanCase> testCasesList = testPlanCaseService.list(queryWrapTreeLbuWrapper);
        List<Long> testCaseIdsList = testCasesList.stream().map(TestPlanCase::getTestcaseId).collect(Collectors.toList());
        if(testCaseIdsList.size() == 0){
            return R.fail("该用例树下没有用例！");
        }
        //删除测试计划
        baseService.remove(Wraps.<TestPlanCase>lbQ().eq(TestPlanCase::getPlanId, planId)
                .in(TestPlanCase::getTestcaseId,testCaseIdsList));
        //删除测试用例
        testPlanCaseResultService.remove(Wraps.<TestPlanCaseResult>lbQ().eq(TestPlanCaseResult::getPlanId, planId)
                .in(TestPlanCaseResult::getTestcaseId,testCaseIdsList));
        //删除用例目录treeIds
        testPlanCaseTreeService.remove(Wraps.<TestPlanCaseTree>lbQ().eq(TestPlanCaseTree::getPlanId, planId)
                .in(TestPlanCaseTree::getTreeId,treeIds));
        return R.success(null, "取消关联用例成功！");
    }

    @ApiOperation(value = "查询测试计划下用例", notes = "查询测试计划下用例")
    @PostMapping("/queryTestPlanCase")
    @SysLog(value = "查询测试计划下用例", request = false)
    public R<List<TestPlanCase>> queryTestPlanCase(@RequestBody TestPlanCase data)
    {
        LbqWrapper<TestPlanCase> queryWrapLbuWrapper = Wraps.lbQ();
        queryWrapLbuWrapper.eq(TestPlanCase::getPlanId, data.getPlanId())
                .eq(TestPlanCase::getStatus,data.getStatus())
                .eq(TestPlanCase::getPriority,data.getPriority())
                .eq(TestPlanCase::getExecBy,data.getExecBy());
        List<TestPlanCase> testPlanCaseLists = baseService.list(queryWrapLbuWrapper);
        return R.success(testPlanCaseLists, "查询测试计划下用例成功！");
    }

    @ApiOperation(value = "修改测试计划下用例优先级", notes = "修改测试计划下用例优先级")
    @PostMapping("/updatePriority")
    @SysLog(value = "修改测试计划下用例优先级", request = false)
    public R<TestPlanCase> updatePriority(@RequestBody TestPlanCase data)
    {
        LbqWrapper<TestPlanCase> queryWrapLbuWrapper = Wraps.lbQ();
        queryWrapLbuWrapper.eq(TestPlanCase::getTestcaseId,data.getTestcaseId())
                .eq(TestPlanCase::getPlanId, data.getPlanId());
        TestPlanCase testPlanCase = baseService.getOne(queryWrapLbuWrapper);
        testPlanCase.setPriority(data.getPriority());
        baseService.updateById(testPlanCase);

//        TestProductCase testCase = testProductCaseService.getById(data.getTestcaseId());
//        if (testCase!=null){
//            testCase.setPriority(data.getPriority());
//            testProductCaseService.updateById(testCase);
//        }else{
//            TestProductCaseArchive byId = testProductCaseArchiveService.getById(data.getTestcaseId());
//            byId.setPriority(data.getPriority());
//            testProductCaseArchiveService.updateById(byId);
//        }
        return R.success(testPlanCase, "修改测试计划下用例优先级成功！");
    }

    @Override
    public IPage<TestPlanCase> query(PageParams<TestPlanCasePageQuery> params) {
        IPage<TestPlanCase> page = params.buildPage(TestPlanCase.class);
        TestPlanCasePageQuery model = params.getModel();
        LbqWrapper<TestPlanCase> planCaseWrapper = Wraps.lbQ();
        planCaseWrapper.eq(TestPlanCase::getPlanId, model.getPlanId())
                .eq(TestPlanCase::getTreeId, model.getTreeId())
                .eq(TestPlanCase::getPriority, model.getPriority())
                .like(TestPlanCase::getName, model.getName())
                .eq(TestPlanCase::getExecBy,model.getExecBy());
        //筛选无执行人的测试用例
        if(!"".equals(model.getHasExecutor()) && model.getHasExecutor()!=null && model.getHasExecutor() == 0){
            planCaseWrapper.isNotNull(TestPlanCase::getExecBy);
        }else if(!"".equals(model.getHasExecutor()) && model.getHasExecutor()!=null && model.getHasExecutor() == 1){
            planCaseWrapper.isNull(TestPlanCase::getExecBy);
        }
        baseService.page(page, planCaseWrapper);
        echoService.action(page);
        List<TestPlanCase> records = new ArrayList<>();
        page.getRecords().forEach(item ->
        {
            User userById = userApi.findUserById(item.getExecBy());
            item.getEchoMap().put("execBy",userById);
            Long testcaseId = item.getTestcaseId();
            if(testProductCaseService.getById(testcaseId) != null) {
                item.getEchoMap().put("result", testProductCaseService.getById(testcaseId).getTestStep());
            }else{
                item.getEchoMap().put("result", testProductCaseArchiveService.getById(testcaseId).getTestStep());
            }
        });
        return page;
    }

    @ApiOperation(value = "查询用例分页信息", notes = "查询用例分页信息")
    @PostMapping("/pageCase")
    @SysLog(value = "查询用例分页信息", request = false)
    public IPage<TestPlanCase> pageCase(@RequestBody PageParams<TestPlanCasePageQuery> params)
    {
        IPage<TestPlanCase> page = params.buildPage(TestPlanCase.class);
        TestPlanCasePageQuery model = params.getModel();
        LbqWrapper<TestPlanCase> planCaseWrapper = Wraps.lbQ();
        planCaseWrapper.eq(TestPlanCase::getPlanId,model.getPlanId())
                .eq(TestPlanCase::getExecBy,model.getExecBy())
                .eq(TestPlanCase::getPriority, model.getPriority())
                .eq(TestPlanCase::getStatus,model.getStatus());
        baseService.page(page,planCaseWrapper);
        return page;
    }

    @ApiOperation(value = "保存测试计划关联用例", notes = "保存测试计划关联用例")
    @PostMapping("/saveTestPlanCase")
    @SysLog(value = "保存测试计划关联用例", request = false)
    @Transactional
    public R saveCaseOFTestPlanGroup(@RequestBody TestPlanCaseSaveDTO model)
    {

        String message = baseService.isPlanCaseAddedRepeat(model);
        if(message.length() > 0) {
            return fail(message);
        }
        if (model.getTestcaseIds()!=null && model.getTestcaseIds().size()!=0)
        {
            List<TestProductCase> testPlanCases = testProductCaseService.list(
                    Wraps.<TestProductCase>lbQ()
                            .in(TestProductCase::getId, model.getTestcaseIds()));
            List<TestPlanCase> testPlanCaseList = new ArrayList<>();
            testPlanCases.forEach(t ->
            {
                TestPlanCase testPlanCase = new TestPlanCase();
                testPlanCase.setId(UidGeneratorUtil.getId());
                testPlanCase.setPlanId(model.getPlanId());
                testPlanCase.setTreeId(t.getTreeId());
                testPlanCase.setTestcaseId(t.getId());
                testPlanCase.setName(t.getName());
                testPlanCase.setVersion(t.getVersion());
                testPlanCase.setExecBy(model.getExecBy());
                testPlanCase.setPriority(t.getPriority());
                testPlanCase.setCaseKey(t.getCaseKey());
                testPlanCaseList.add(testPlanCase);
            });
            testPlanCaseService.saveBatch(testPlanCaseList);
        }
        Long planId = model.getPlanId();
        List<CaseAndTreeReturnVo> caseAndTreeReturnVo = model.getCaseAndTreeReturnVo();
        ArrayList<TestPlanCaseTree> testPlanCaseTrees = new ArrayList<>();
        //0920在对比是否传入的树级Id已被重复关联时，就不在保存其树级结构
        //接口传参需格式要修改，保存数据格式需要修改
        //TestPlanCaseTree实体类格式需要修改
        //TestPlanCaseTreeXml格式需要修改，补充字段
        List<Long> collect =
                testPlanCaseTreeService.list(Wraps.<TestPlanCaseTree>lbQ().eq(TestPlanCaseTree::getPlanId, planId))
                        .stream()
                        .map(TestPlanCaseTree::getTreeId).collect(Collectors.toList());
        caseAndTreeReturnVo.forEach(item ->
        {
             if (!collect.contains(Long.valueOf(item.getTreeId()))){
                TestPlanCaseTree testPlanCaseTree = TestPlanCaseTree.builder().id(UidGeneratorUtil.getId())
                        .planId(planId)
                        .parentId(Long.valueOf(item.getParentId()))
                        .name(item.getTreeName())
                        .treeId(Long.valueOf(item.getTreeId()))
                        .createdBy(ContextUtil.getUserId()).build();
                testPlanCaseTrees.add(testPlanCaseTree);
            }
        });
        testPlanCaseTreeService.saveBatch(testPlanCaseTrees);
        return success();
    }



    @ApiOperation(value = "分组指派", notes = "分组指派")
    @PostMapping("/groupForUser")
    @SysLog(value = "分组指派", request = false)
    public R groupForUser(@RequestBody @Validated GroupForUser groupForUser)
    {
        Long user = groupForUser.getUser();
        Long planId = groupForUser.getPlanId();
        List<Long> caseId = groupForUser.getCaseId();
//        ArrayList<TestPlanCase> testPlanCases = new ArrayList<>();
        UpdateWrapper<TestPlanCase> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("testcase_id", caseId);
        updateWrapper.eq("plan_id", planId);
        updateWrapper.set("exec_by", user);
        baseService.update(updateWrapper);
//        caseId.forEach(item->
//        {
//            TestPlanCase testPlanCase = baseService.getOne(Wraps.<TestPlanCase>lbQ().eq(TestPlanCase::getPlanId, planId)
//                    .eq(TestPlanCase::getTestcaseId, item));
//            testPlanCase.setExecBy(user);
//            testPlanCases.add(testPlanCase);
//        });
//        baseService.updateBatchById(testPlanCases);

        return R.success();
    }

    @ApiOperation(value = "执行用例", notes = "执行用例")
    @PostMapping("/executePlanCase")
    @SysLog(value = "执行用例", request = false)
    public R executePlanCase(@RequestBody TestPlanCaseExecDTO testPlanCaseExecDTO) {
        Long planId = Long.valueOf(testPlanCaseExecDTO.getPlanIdString());
        String caseId = testPlanCaseExecDTO.getCaseId();
        String status;
        if (Objects.equals(testPlanCaseExecDTO.getStatus(), "undo")){
            status = null;
        }else{
            status = testPlanCaseExecDTO.getStatus();
        }
        String result = testPlanCaseExecDTO.getResult();
        if (caseId == null) {
            return R.fail("请选择需要执行得用例！");
        }
        TestProductCaseHistory testProductCaseHistory = testProductCaseHistoryService
                .getOne(Wraps.<TestProductCaseHistory>lbQ().eq(TestProductCaseHistory::getCaseId, caseId)
                        .eq(TestProductCaseHistory::getVersion, testPlanCaseExecDTO.getVersion()));

        TestPlan byId = testPlanService.getById(planId);

        TestPlanCase one = baseService.getOne(new QueryWrapper<TestPlanCase>().eq("plan_id", planId)
                .eq("testcase_id", caseId));
        baseService.update(new UpdateWrapper<TestPlanCase>().eq("id", one.getId())
                .set("status", status)
                .set("exec_by", getUserId())
                .set("exec_etime",LocalDateTime.now()));

        TestPlanCaseResult testPlanCaseResult = new TestPlanCaseResult();
        testPlanCaseResult.setPlanId(planId);
        testPlanCaseResult.setStatus(status);
        testPlanCaseResult.setTestcaseId(Long.valueOf(caseId));
        testPlanCaseResult.setName(byId.getName());
        if(testProductCaseHistory != null){
            testPlanCaseResult.setHistoryId(testProductCaseHistory.getId());
        }
        testPlanCaseResult.setExecBy(getUserId());
        testPlanCaseResult.setExecTime(LocalDateTime.now());
        testPlanCaseResult.setCreateTime(LocalDateTime.now());
        testPlanCaseResult.setCreatedBy(getUserId());
        testPlanCaseResult.setUpdateTime(LocalDateTime.now());
        testPlanCaseResult.setUpdatedBy(getUserId());
        testPlanCaseResult.setResult(result);
        testPlanCaseResult.setVersion(testPlanCaseExecDTO.getVersion());
        testPlanCaseResult.setTruexecTime(testPlanCaseExecDTO.getTruexecTime());
        testPlanCaseResultService.saveTestPlanCaseResult(testPlanCaseResult);

        List<File> oldFiles = fileApi.findByBizTypeAndBizId(FileBizType.EXCUTPLANCASE_FILE_UPLOAD, testPlanCaseResult.getId());

        List<Long> oldFileIds = oldFiles.stream().map(File::getId).collect(Collectors.toList());

        List<File> newFiles = testPlanCaseExecDTO.getExecutePlanCaseFiles();

        if (newFiles == null || newFiles.isEmpty())
        {
            if (!oldFileIds.isEmpty())
            {
                fileApi.removeByFileBizTypeAndBizIds(FileBizType.EXCUTPLANCASE_FILE_UPLOAD, oldFileIds);
            }
        }
        else
        {
            List<Long> newFileIds = newFiles.stream().map(File::getId).collect(Collectors.toList());

            if (!oldFileIds.isEmpty())
            {
                // 筛选出需要删除的数据
                List<Long> deleteFileIds =
                        oldFileIds.stream().filter(item -> !newFileIds.contains(item)).collect(Collectors.toList());

                fileApi.removeByFileBizTypeAndBizIds(FileBizType.EXCUTPLANCASE_FILE_UPLOAD, deleteFileIds);

            }
            newFiles.forEach(item -> item.setBizId(testPlanCaseResult.getId()).setBizType(FileBizType.EXCUTPLANCASE_FILE_UPLOAD));
            fileApi.updateBatchById(newFiles);
        }
        return success("执行计划用例成功！");
    }

    @ApiOperation(value = "批量执行用例", notes = "批量执行用例")
    @PostMapping("/executePlanCase/{testPlanId}/{status}")
    @SysLog(value = "批量执行用例", request = false)
    @Transactional
    public R executePlanCase(@RequestBody List<TestProductCase> testProductCases,@PathVariable Long testPlanId,@PathVariable String status) {

        if (testProductCases == null || testProductCases.size() == 0) {
            return R.fail("请选择需要执行得用例！");
        }
        for (TestProductCase item : testProductCases)
        {
            Long caseId = item.getId();
            String result = item.getExpectedResult();

            TestProductCaseHistory testProductCaseHistory = testProductCaseHistoryService
                    .getOne(Wraps.<TestProductCaseHistory>lbQ().eq(TestProductCaseHistory::getCaseId, caseId)
                            .eq(TestProductCaseHistory::getVersion, item.getVersion()));

            TestPlan byId = testPlanService.getById(testPlanId);

            TestPlanCase one = baseService.getOne(new QueryWrapper<TestPlanCase>().eq("plan_id", testPlanId)
                    .eq("testcase_id", caseId));

            baseService.update(new UpdateWrapper<TestPlanCase>().eq("id", one.getId())
                    .set("status", status)
                    .set("exec_by", getUserId())
                    .set("exec_etime",LocalDateTime.now()));

            TestPlanCaseResult testPlanCaseResult = new TestPlanCaseResult();
            testPlanCaseResult.setPlanId(testPlanId);
            testPlanCaseResult.setStatus(status);
            testPlanCaseResult.setTestcaseId(Long.valueOf(caseId));
            testPlanCaseResult.setName(byId.getName());
            if(testProductCaseHistory != null){
                testPlanCaseResult.setHistoryId(testProductCaseHistory.getId());
            }
            testPlanCaseResult.setExecBy(getUserId());
            testPlanCaseResult.setExecTime(LocalDateTime.now());
            testPlanCaseResult.setCreateTime(LocalDateTime.now());
            testPlanCaseResult.setCreatedBy(getUserId());
            testPlanCaseResult.setUpdateTime(LocalDateTime.now());
            testPlanCaseResult.setUpdatedBy(getUserId());
            testPlanCaseResult.setResult(result);
            testPlanCaseResult.setVersion(item.getVersion());
            testPlanCaseResultService.saveTestPlanCaseResult(testPlanCaseResult);
        }
        return success("执行计划用例成功！");
    }



    @ApiOperation(value = "执行用例结果", notes = "执行用例结果")
    @GetMapping("/executePlanCaseResult")
    @SysLog(value = "执行用例结果", request = false)
    public R executePlanCaseResult(@RequestParam String planId,@RequestParam String testcaseId) {
        List<TestPlanCaseResult> testPlanCaseResults = testPlanCaseResultService.list(new QueryWrapper<TestPlanCaseResult>()
                .eq("plan_id", Long.valueOf(planId))
                .eq("testcase_id", testcaseId)
                .orderByDesc("create_time"));
        // 用例未执行，没有用例执行结果
        if(testPlanCaseResults.size() == 0) {
            return success();
        }
        // 取出最新的一条执行记录
        TestPlanCaseResult testPlanCaseResult = testPlanCaseResults.get(0);
        TestPlanCaseExecDTO testPlanCaseExecDTO = new TestPlanCaseExecDTO();
        testPlanCaseExecDTO.setPlanId(testPlanCaseResult.getPlanId());
        testPlanCaseExecDTO.setCaseId(String.valueOf(testPlanCaseResult.getTestcaseId()));
        testPlanCaseExecDTO.setResult(testPlanCaseResult.getResult());
        testPlanCaseExecDTO.setStatus(testPlanCaseResult.getStatus());
        testPlanCaseExecDTO.setLatestTime(testPlanCaseResult.getCreateTime());
        testPlanCaseExecDTO.setTruexecTime(testPlanCaseResult.getTruexecTime());
        User userById = userApi.findUserById(testPlanCaseResult.getExecBy());
        testPlanCaseExecDTO.setExecAvatarPath(userById.getAvatarPath());
        testPlanCaseExecDTO.setExecName(userById.getName());
        // 文本/步骤 字段赋值
        TestProductCase testProductCase = testProductCaseService.getById(testPlanCaseResult.getTestcaseId());
        if (testProductCase == null){
            testProductCase = BeanUtil.toBean(testProductCaseArchiveService.getById(testcaseId), TestProductCase.class);
        }
        testPlanCaseExecDTO.setStepType(testProductCase.getStepType());
        testPlanCaseExecDTO.setExecutePlanCaseFiles(fileApi.findByBizTypeAndBizId(FileBizType.EXCUTPLANCASE_FILE_UPLOAD, testPlanCaseResult.getId()));
        return success(testPlanCaseExecDTO);
    }

    @ApiOperation(value = "查询当前测试计划下执行用例历史执行结果", notes = "查询当前测试计划下执行用例历史执行结果")
    @GetMapping("/getHistoryxecutePlanCaseResults")
    @SysLog(value = "查询当前测试计划下执行用例历史执行结果", request = false)
    public R getHistoryExecutePlanCaseResults(@RequestParam(value = "planId") String planId,@RequestParam(value = "testcaseId") String testcaseId) {

        List<TestPlanCaseResult> testPlanCaseResults = testPlanCaseResultService.list(new QueryWrapper<TestPlanCaseResult>()
                .eq("plan_id", Long.valueOf(planId))
                .eq("testcase_id", testcaseId)
                .orderByDesc("create_time"));

        testPlanCaseResults.forEach(testPlanCaseResult -> {
            Map<String, Object> echoMap = testPlanCaseResult.getEchoMap();
            User userById = userApi.findUserById(testPlanCaseResult.getExecBy());
            echoMap.put("execBy",userById);
        });
        return success(testPlanCaseResults);
    }

    @ApiOperation(value = "用例分组树的筛选", notes = "用例分组树的筛选")
    @GetMapping("/queryPlanCaseTreeByNameLike")
    @SysLog(value = "用例分组树的筛选", request = false)
    public R queryPlanCaseTreeByNameLike(@RequestParam Long planId, @RequestParam String name ) {
        List<ProductCaseTree> caseTrees = testPlanCaseService.getTreeByPlanId(planId);
        List<ProductCaseTree> collect = caseTrees.stream().filter(t -> t.getName().contains(name)).collect(Collectors.toList());
        return success(collect);
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "planIds", value = "计划id", dataType = DATA_TYPE_LONG_ARRAY,
                    paramType = PARAM_TYPE_QUERY)
    })
    @ApiOperation(value = "查询项目用例分布状态统计", notes = "查询项目用例分布状态统计")
    @GetMapping("/status")
    @SysLog("查询项目用例分布状态统计")
    public R<Map<String, Object>> status(@RequestParam(value = "planIds", required = false) List<Long> planIds)
    {
        return R.success(baseService.status(planIds));
    }


/*    @ApiOperation(value = "筛选无执行人的计划", notes = "筛选无执行人的计划")
    @PostMapping("/getExecPlan")
    @SysLog(value = "筛选无执行人的计划", request = false)
    public IPage<TestPlanCase> getExecPlan(@RequestBody @Validated PageParams<TestPlanCasePageQuery> params)
    {
        IPage<TestPlanCase> page = params.buildPage(TestPlanCase.class);
        TestPlanCasePageQuery model = params.getModel();
        LbqWrapper<TestPlanCase> wrapper = Wraps.lbQ();
        wrapper.eq(TestPlanCase::getPlanId,model.getPlanId());
        wrapper.isNull(TestPlanCase::getExecBy);
        baseService.page(page, wrapper);
        echoService.action(page);
        return page;
    }*/

    @ApiOperation(value = "测试计划内用例升级", notes = "测试计划内用例升级")
    @PutMapping("/planUpgrade")
    @SysLog(value = "测试计划内用例升级", request = false)
    @Transactional
    public R<Boolean> planUpgrade(@Validated @RequestBody TestPlanCaseUpdateDTO testPlanCaseUpdateDTO)
    {
        TestPlanCase testPlanCase = BeanUtil.toBean(testPlanCaseUpdateDTO, TestPlanCase.class);
        Long planId = testPlanCase.getPlanId();
        Long testcaseId = testPlanCase.getTestcaseId();
        TestProductCase testProductCase = testProductCaseService.getById(testcaseId);
        if (testProductCase == null){
            testProductCase = BeanUtil.toBean(testProductCaseArchiveService.getById(testcaseId), TestProductCase.class);
        }
        TestPlanCase one = baseService.getOne(Wraps.<TestPlanCase>lbQ().eq(TestPlanCase::getPlanId, planId)
                .eq(TestPlanCase::getTestcaseId, testcaseId));
        one.setVersion(testProductCase.getVersion());
        one.setName(testProductCase.getName());
        baseService.updateById(one);
        testPlanCaseResultService.remove(Wraps.<TestPlanCaseResult>lbQ().eq(TestPlanCaseResult::getPlanId, planId)
                .eq(TestPlanCaseResult::getTestcaseId,testcaseId));
        return R.success();
    }

    @ApiOperation(value = "根据当前用户获取全部待需要执行的计划", notes = "根据当前用户获取全部待需要执行的计划")
    @PostMapping("/getAllExecPlan")
    @SysLog(value = "根据当前用户获取全部待需要执行的计划", request = false)
    public R getAllExecPlan(@RequestBody TestPlanGroupTreePageQuery testPlanGroupTreePageQuery) {
        String name = testPlanGroupTreePageQuery.getName();
        Long userId = testPlanGroupTreePageQuery.getParentId();
        List<Long> treeId = new ArrayList<>();
        List<Long> planIds = new ArrayList<>();

        Long orgId = ContextUtil.getOrgId();
        List<TestPlanGroupTreeOrg> testPlanGroupTreeOrgs = testPlanGroupTreeOrgService
                .list(Wraps.<TestPlanGroupTreeOrg>lbQ()
                        .eq(TestPlanGroupTreeOrg::getOrgId, orgId));
        List<Long> collect =
                testPlanGroupTreeOrgs.stream().map(TestPlanGroupTreeOrg::getTestPlanGroupTreeId).collect(
                        Collectors.toList());

        if (userId != null && (name ==null || name=="")){
            planIds = baseService.list(Wraps.<TestPlanCase>lbQ().eq(TestPlanCase::getExecBy, userId)
                    .isNull(TestPlanCase::getStatus)).stream()
                    .map(TestPlanCase::getPlanId).collect(Collectors.toList());
            if (planIds.size() == 0){
                return success(null);
            }
            treeId = testPlanService.list(Wraps.<TestPlan>lbQ().in(TestPlan::getId, planIds)).stream()
                    .map(TestPlan::getTreeId).collect(Collectors.toList());
        }else if (userId == null && name !=null && name != ""){
            treeId =
                    testPlanGroupTreeService.list(Wraps.<TestPlanGroupTree>lbQ()
                            .like(TestPlanGroupTree::getName, name))
                            .stream().map(TestPlanGroupTree::getId).collect(Collectors.toList());
        }else {
            planIds = baseService.list(Wraps.<TestPlanCase>lbQ().eq(TestPlanCase::getExecBy, userId)
                    .isNull(TestPlanCase::getStatus)).stream()
                    .map(TestPlanCase::getPlanId).collect(Collectors.toList());
            treeId = testPlanService.list(Wraps.<TestPlan>lbQ().in(TestPlan::getId, planIds)).stream()
                    .map(TestPlan::getTreeId).collect(Collectors.toList());
            List<Long> tree = testPlanGroupTreeService.list(Wraps.<TestPlanGroupTree>lbQ()
                    .like(TestPlanGroupTree::getName, name))
                    .stream().map(TestPlanGroupTree::getId).collect(Collectors.toList());
            //取交集
            treeId = treeId.stream().filter(tree::contains).collect(Collectors.toList());
        }

        if (treeId.size()==0){
            return success(null);
        }
        List<Long> ids = new ArrayList<>();
        ids.addAll(treeId);
        boolean isFor = true;
        List<Long> treeIds = new ArrayList<>();
        treeIds.addAll(treeId);
        while(isFor)
        {
            List<Long> parentIds =
                    testPlanGroupTreeService.list(Wraps.<TestPlanGroupTree>lbQ().in(TestPlanGroupTree::getId, treeIds))
                            .stream().map(TestPlanGroupTree::getParentId).collect(Collectors.toList());
            if (!parentIds.isEmpty()){
                treeIds = parentIds;
                ids.addAll(parentIds);
            }else{
                isFor = false;
            }
        }
        List<Long> collects = ids.stream().distinct().collect(Collectors.toList());
        Map map = new HashMap();
        List<TestPlanGroupTree> list =
                testPlanGroupTreeService.list(Wraps.<TestPlanGroupTree>lbQ().in(TestPlanGroupTree::getId, collects));
        List<TestPlanGroupTree> testPlanGroupTrees = TreeUtil.buildTree(list);
        List<TestPlanGroupTree> collect1 =
                testPlanGroupTrees.get(0).getChildren().stream().filter(item -> collect.contains(item.getId())).collect(
                        Collectors.toList());
        testPlanGroupTrees.get(0).setChildren(collect1);
        map.put("tree",testPlanGroupTrees);
        map.put("count",planIds.size());
        return success(map);
    }
}
