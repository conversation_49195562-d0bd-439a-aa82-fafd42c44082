package com.jettech.jettong.testm.controller.testCase;

import cn.hutool.core.bean.BeanUtil;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.utils.StringUtil;
import com.jettech.jettong.base.dto.rbac.org.OrgUpdateSortDTO;
import com.jettech.jettong.testm.dao.TestCaseMapper;
import com.jettech.jettong.testm.dao.TestPlanCaseMapper;
import com.jettech.jettong.testm.dao.TestPlanCaseResultMapper;
import com.jettech.jettong.testm.dao.TestPlanMapper;
import com.jettech.jettong.testm.dto.TestCaseTreePageQuery;
import com.jettech.jettong.testm.dto.TestCaseTreeSaveDTO;
import com.jettech.jettong.testm.dto.TestCaseTreeUpdateDTO;

import com.jettech.jettong.testm.entity.*;
import com.jettech.jettong.testm.service.TestCaseTreeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 测试案例树表控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例树表控制器
 * @projectName jettong
 * @package com.jettech.jettong.testm.controller
 * @className TestCaseTreeController
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/testCaseTree")
@Api(value = "TestCaseTree", tags = "测试案例树表")
public class TestCaseTreeController extends
        SuperController<TestCaseTreeService, Long, TestCaseTree, TestCaseTreePageQuery, TestCaseTreeSaveDTO,
                TestCaseTreeUpdateDTO>
{
    @Autowired
    private TestCaseMapper testCaseMapper;
    @Autowired
    private TestPlanCaseMapper testPlanCaseMapper;
    @Autowired
    private TestPlanMapper testPlanMapper;
    @Autowired
    private TestPlanCaseResultMapper testPlanCaseResultMapper;

    @Override
    public R<TestCaseTree> handlerSave(@Validated TestCaseTreeSaveDTO model)
    {
        TestCaseTree testCaseTree = BeanUtil.toBean(model, TestCaseTree.class);
        baseService.saveTestCaseTree(testCaseTree);
        return R.success(testCaseTree, "用例树级新增成功！");
    }

    @ApiOperation(value = "查询用例库下用例树", notes = "查询用例库下用例树")
    @GetMapping("/findTestCaseTreeOfRepository/{libraryId}")
    @SysLog(value = "查询用例库下用例树", request = false)
    public R<List<TestCaseTree>> findTestCaseTreeOfRepository(@PathVariable Long libraryId)
    {
        return R.success(baseService.list(Wraps.<TestCaseTree>lbQ()
                .eq(TestCaseTree::getLibraryId, libraryId).orderByAsc(TestCaseTree::getSort)));
    }

    @ApiOperation(value = "同步添加树结构", notes = "同步添加树结构")
    @GetMapping("/synTrees/{oldLibraryId}")
    @SysLog(value = "同步添加树结构", request = false)
    public R synTrees(@PathVariable Long oldLibraryId, @RequestBody List<TestCaseTree> newProductCaseTree )
    {
        //需要树的实体类转换


        List<TestCaseTree> testCaseTreeOfRepository = baseService.findTestCaseTreeOfRepository(oldLibraryId);
        List<TestCaseTree> caseTreeList = new ArrayList<>();
        for (TestCaseTree productCaseTree : newProductCaseTree){
            List<TestCaseTree> caseTrees = testCaseTreeOfRepository.stream()
                    .filter(testCaseTree -> Objects.equals(testCaseTree.getName(), productCaseTree.getName())).collect(Collectors.toList());
            caseTreeList.addAll(caseTrees);
        }
        newProductCaseTree.removeAll(caseTreeList);
        baseService.saveBatch(newProductCaseTree);
        return R.success("同步添加树结构成功！");
    }

    @Override
    public R<Boolean> handlerDelete(List<Long> ids)
    {
        
        baseService.delTestCaseTree(ids);
        return success();
    }

    @ApiOperation(value = "分组树拖拽", notes = "分组树拖拽")
    @PutMapping("/updateById")
    @SysLog(value = "分组树拖拽", request = false)
    public R<Boolean> updateById(@Validated @RequestBody OrgUpdateSortDTO caseTreeUpdateDTO)
    {
        baseService.updateSortById(caseTreeUpdateDTO.getParentId(),caseTreeUpdateDTO.getOrgId(),caseTreeUpdateDTO.getPreOrgId());
        return R.success();
    }
}
