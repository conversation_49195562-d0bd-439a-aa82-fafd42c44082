package com.jettech.jettong.testm.controller.productCase;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.context.ContextUtil;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;

import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.base.dto.rbac.org.OrgSaveDTO;
import com.jettech.jettong.base.dto.sys.dictionary.SysDictionaryRelationSaveDTO;
import com.jettech.jettong.base.entity.rbac.org.Org;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.base.enumeration.dictionary.DictionaryRelationBizType;
import com.jettech.jettong.common.constant.DictionaryType;
import com.jettech.jettong.common.enumeration.ProductCaseRevisionOperationTypeEnum;
import com.jettech.jettong.product.api.ProductModuleFunctionApi;
import com.jettech.jettong.testm.dto.*;
import com.jettech.jettong.testm.entity.*;
import com.jettech.jettong.testm.service.TestProductCaseRegistryService;
import com.jettech.jettong.testm.service.TestProductCaseService;
import com.jettech.jettong.testm.service.TestTabCaseService;
import com.jettech.jettong.testm.service.TestTaskCaseService;
import com.jettech.jettong.testm.vo.TestCaseLibraryAndCaseNumber;
import com.jettech.jettong.testm.vo.TestProductCaseNumberOfTreeVo;
import com.jettech.jettong.testm.vo.TestSummaryComponent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * TestProductCaseRegistry 控制器
 * <AUTHOR>
 * @version 1.0
 * @description TestProductCaseRegistry 控制器
 * @projectName jettong
 * @package com.jettech.jettong.testm.controller.productCase
 * @className TestProductCaseRegistryController
 * @date 2024-01-01
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@RestController
@RequestMapping("/testProductCaseRegistry")
@Api(value = "TestProductCaseRegistry", tags = "产品用例库表信息")
@PreAuth(replace = "testm:testProductCaseRegistry:")
@RequiredArgsConstructor
public class TestProductCaseRegistryController extends SuperController<TestProductCaseRegistryService, Long, TestProductCaseRegistry, TestProductCasePageQuery, TestProductCaseRegistrySaveDTO, TestProductCaseRegistryUpdateDTO> {


    private final TestTabCaseService  testTabCaseService;


    private final EchoService echoService;

    public final ProductModuleFunctionApi moduleFunctionApi;

    public final TestProductCaseService testProductCaseService;

    @Override
    public IPage<TestProductCaseRegistry> query(@RequestBody @Validated PageParams<TestProductCasePageQuery> params) {
        IPage<TestProductCaseRegistry> page = params.buildPage(TestProductCase.class);
        TestProductCasePageQuery model = params.getModel();
        List<TestTabCase> testTabCaseList = new ArrayList<>();
        if(params.getModel().getTabName()!=null&&params.getModel().getTabName().length > 0){
            for (String tabId : params.getModel().getTabName()){
                if (testTabCaseList.size() == 0) {
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ().eq(TestTabCase::getTabId, Long.valueOf(tabId)));
                }else{
                    List<Long> prodIds = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ()
                            .eq(TestTabCase::getTabId, Long.valueOf(tabId))
                            .in(TestTabCase::getProdId, prodIds));
                }
                if (testTabCaseList.size() == 0){
                    return page;
                }
            }
        }

        LbqWrapper<TestProductCaseRegistry> wrapper = Wraps.lbQ();

        wrapper.eq(TestProductCase::getProductId, model.getProductId())
                .like(TestProductCaseRegistry::getName, model.getName())
                .eq(TestProductCaseRegistry::getLeadingBy, model.getLeadingBy())
                .eq(TestProductCaseRegistry::getLibraryId,model.getLibraryId())
                .eq(TestProductCaseRegistry::getPriority,model.getPriority())
                .like(TestProductCaseRegistry::getCaseKey,model.getCaseKey())
                .eq(TestProductCaseRegistry::getState,model.getState())
                .eq(TestProductCaseRegistry::getDraft,model.getDraft())
                .eq(TestProductCaseRegistry::getTestreqId,model.getTestreqId());
        List<Long> collect = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
        if (collect.size() > 0){
            wrapper.in(TestProductCaseRegistry::getLastCaseId, testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList()));
        }
        if(model.getFunctionIds().size() > 0){
            wrapper.in(TestProductCaseRegistry::getModuleFunctionId,model.getFunctionIds());
        }

        // 添加自定义字段的条件筛选
        model.appendQuery(wrapper);
        baseService.page(page, wrapper);
        echoService.action(page,"requirementId","projectId","testreqId","taskId");
        return page;
    }
    @Override
    public R<TestProductCaseRegistry> handlerSave(@Validated TestProductCaseRegistrySaveDTO model)
    {
        TestProductCaseRegistry testCase = BeanPlusUtil.toBean(model, TestProductCaseRegistry.class);
        String sysCode = moduleFunctionApi.findProductInfoCodeByFunctionId(testCase.getModuleFunctionId());
        String caseKey= testProductCaseService.getProductCaseKey(sysCode);
        testCase.setCaseKey(caseKey);
        Long id = UidGeneratorUtil.getId();
        testCase.setId(id);
        testCase.setLastCaseId(id);
        testCase.setCreateTime(LocalDateTime.now());
//        testCase.setCreatedBy(ContextUtil.getUserId());
        testCase.setUpdateTime(LocalDateTime.now());
//        testCase.setUpdatedBy(ContextUtil.getUserId());
        List<String> testModes = testCase.getTestModes();
        if(!testModes.isEmpty()){
            //子平台赋值
            int sum = testModes.stream()
                    .mapToInt(Integer::parseInt)
                    .sum();
            testCase.setTestMode(String.valueOf(sum));
        } else {
            testCase.setTestMode("0");
        }
        if(testCase.getTaskId() == null){
            testCase.setTaskId(0L);
        }
        baseService.save(testCase);
//        testTabCaseService.save(testCase.getId(), model.getTabInfo(), "product");
        // 维护registry和revision表
        testProductCaseService.createRevision(testCase, 1, ProductCaseRevisionOperationTypeEnum.ADD, "创建用例");
        return success(testCase);
    }

    @Override
    public R<TestProductCaseRegistry> handlerUpdate(@Validated TestProductCaseRegistryUpdateDTO model)
    {
        TestProductCaseRegistry testProductCase = BeanPlusUtil.toBean(model, TestProductCaseRegistry.class);
        TestProductCaseRegistry testProduct = baseService.getById(model.getId());
        List<String> testModes = model.getTestModes();
        if (!testModes.isEmpty()) {
            //子平台赋值
            int sum = testModes.stream()
                    .mapToInt(Integer::parseInt)
                    .sum();
            testProductCase.setTestMode(String.valueOf(sum));
        } else {
            testProductCase.setTestMode("0");
        }
        // 从revision表获取当前case_key的最大版本号并递增
        int newVersionNo = testProductCaseService.getNextVersionNo(testProductCase);
        testProductCase.setUpdateTime(LocalDateTime.now());
//        testProductCase.setUpdatedBy(ContextUtil.getUserId());
        testProductCase.setCaseKey(testProduct.getCaseKey());
        testProductCase.setTaskId(testProduct.getTaskId());
        testProductCase.setCreatedBy(testProduct.getCreatedBy());
        baseService.updateById(testProductCase);
        // 维护revision表
        testProductCaseService.createRevision(testProductCase, newVersionNo, ProductCaseRevisionOperationTypeEnum.UPDATE, "修改用例");

//        testTabCaseService.update(model.getId(), model.getTabInfo(), "product");
        return success(testProductCase);
    }

    @Override
    @Transactional
    public R<Boolean> delete(@RequestBody List<Long> ids)
    {
        if (Objects.equals(ids.size(),0)||Objects.equals(ids,null)){
            return fail("参数传递异常！");
        }
        List<TestProductCaseRegistry> testProductCases =
                baseService.list(Wraps.<TestProductCaseRegistry>lbQ().in(TestProductCaseRegistry::getId, ids));
        for (TestProductCaseRegistry tradeFlowCase : testProductCases) {
            tradeFlowCase.setCaseStatus(0);
            baseService.updateById(tradeFlowCase);
        }
        return success();
    }

}
