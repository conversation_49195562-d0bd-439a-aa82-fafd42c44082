package com.jettech.jettong.testm.controller.echo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.jettech.basic.annotation.base.IgnoreResponseBodyAdvice;
import com.jettech.basic.base.R;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.testm.dto.ProductCaseLibrarySaveDTO;
import com.jettech.jettong.testm.dto.TestCaseLibrarySaveDTO;
import com.jettech.jettong.testm.entity.*;
import com.jettech.jettong.testm.service.*;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 数据注入查询接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 数据注入查询接口
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.testm.controller.echo
 * @className EchoController
 * @date 2021/11/11 10:30
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/testm")
@IgnoreResponseBodyAdvice
@Api(value = "数据注入查询接口", tags = "数据注入查询接口， 不建议前端调用")
@ApiIgnore
public class EchoController {
    private final TestPlanService testPlanService;
    private final TestProductCaseService testProductCaseService;
    private final TestPlanCaseService testPlanCaseService;
    private final TestTaskCaseService testTaskCaseService;
    private final TestCaseService testCaseService;
    private final TestCaseLibraryService testCaseLibraryService;
    private final ProductCaseLibraryService productCaseLibraryService;
    private final ProductCaselibraryConnectService productCaselibraryConnectService;
    private final TestOverviewService testOverviewService;
    private final TestReportService testReportService;
    private final TestSchemeService testSchemeService;
    private final TestRequirementFunctionPointsService testRequirementFunctionPointsService;
    private final TestTaskCaseResultService testTaskCaseResultService;


    /**
     * 根据需求id查询测试计划
     *
     * @param requirementId 需求id
     * <AUTHOR>
     * @date 2021/12/06 20:15
     * @update lxr 2021/12/06 20:15
     * @since 1.0
     */
    @GetMapping("/testPlan/getTestPlanByRequirementId")
    public List<TestPlan> getTestPlanByRequirementId(@RequestParam(value = "requirementId") Long requirementId) {
        //获取需求关联测是测试案例id
        List<Long> testCaseIdList = testProductCaseService.listObjs(
                Wraps.<TestProductCase>lbQ().select(TestProductCase::getId).eq(TestProductCase::getRequirementId, requirementId),
                Convert::toLong);
        if (testCaseIdList.isEmpty()) {
            return new ArrayList<>();
        }
        //根据测试案例id获取关联的测试计划id
        List<Long> testPlanIdList = testPlanCaseService.listObjs(
                Wraps.<TestPlanCase>lbQ().select(TestPlanCase::getPlanId)
                        .in(TestPlanCase::getTestcaseId, testCaseIdList),
                Convert::toLong);
        if (testPlanIdList.isEmpty()) {
            return new ArrayList<>();
        }
        return testPlanService.list(
                Wraps.<TestPlan>lbQ().in(TestPlan::getId, testPlanIdList));
    }


    /**
     * 根据需求id列表查询测试计划列表     *
     *
     * @param requirementId 需求idList
     * <AUTHOR>
     * @date 2021/12/06 20:15
     * @update lxr 2021/12/06 20:15
     * @since 1.0
     */
    @GetMapping("/testPlan/getTestPlanListByRequList")
    public List<TestPlan> getTestPlanListByRequList(@RequestParam(value = "requirementId") List<String> requirementId) {

        List<TestPlan> result = testPlanService.getPlanDetailByRequirementId(requirementId);

        return result;
    }

    /**
     * 根据需求id查询测试计划
     *
     * @param testCaseLibrarySaveDTO
     * <AUTHOR>
     * @date 2021/12/06 20:15
     * @update lxr 2021/12/06 20:15
     * @since 1.0
     */
    @PostMapping("/testCaseRepository/echo/createTestCaseLibrary")
    public TestCaseLibrary createTestCaseLibrary(@RequestBody TestCaseLibrarySaveDTO testCaseLibrarySaveDTO) {
        TestCaseLibrary testCaseLibrary = BeanUtil.toBean(testCaseLibrarySaveDTO, TestCaseLibrary.class);
        testCaseLibrary.setId(UidGeneratorUtil.getId());
        testCaseLibrary.setCreateTime(LocalDateTime.now());
        testCaseLibraryService.saveTestCaseLibrary(testCaseLibrary);
        return testCaseLibrary;
    }

    @PostMapping("/testCaseRepository/echo/createProductCaseLibrary")
    public ProductCaseLibrary createProductCaseLibrary(@RequestBody ProductCaseLibrarySaveDTO productCaseLibrarySaveDTO) {
        ProductCaseLibrary productCaseLibrary = BeanUtil.toBean(productCaseLibrarySaveDTO, ProductCaseLibrary.class);
        productCaseLibrary.setId(UidGeneratorUtil.getId());
        productCaseLibrary.setCreateTime(LocalDateTime.now());
        productCaseLibraryService.saveProductCase(productCaseLibrary);
        return productCaseLibrary;
    }

    @PostMapping("/testCaseRepository/echo/updateTestCaseLibrary")
    public TestCaseLibrary updateTestCaseLibrary(@RequestBody TestCaseLibrarySaveDTO testCaseLibrarySaveDTO) {
        TestCaseLibrary testCaseLibrary = BeanUtil.toBean(testCaseLibrarySaveDTO, TestCaseLibrary.class);
        Long projectId = testCaseLibrary.getProjectId();
        List<TestCaseLibrary> all = testCaseLibraryService
                .list(Wraps.<TestCaseLibrary>lbQ().eq(TestCaseLibrary::getProjectId, projectId));
        all.forEach(item ->
        {
            item.setProductId(testCaseLibrary.getProductId());
        });
        testCaseLibraryService.updateBatchById(all);
        return testCaseLibrary;
    }

    /**
     * 根据产品id查出关联的测试计划
     *
     * @param productIds 产品id集
     * <AUTHOR>
     * @date 2022-04-07 16：08
     * @update
     * @since 1.0
     */
    @GetMapping("/testPlan/summaryBugOnProductId")
    public List<TestPlan> summaryBugOnProductId(@RequestParam(value = "productIds", required = false) List<Long> productIds) {
        List<TestPlan> plans = new ArrayList<>();
        for (Long productId : productIds) {
            // 根据产品找到关联的产品库
            List<ProductCaseLibrary> list = productCaseLibraryService.list(Wraps.<ProductCaseLibrary>lbQ().eq(ProductCaseLibrary::getProductId, productId));
            // 根据产品库找到关联的测试计划
            for (ProductCaseLibrary t : list) {
                plans.addAll(testPlanService.list(Wraps.<TestPlan>lbQ().eq(TestPlan::getLibraryId, t.getId())));
            }
        }

        return plans;
    }

    @GetMapping("/testPlan/summaryBugOnLibrarieIds")
    public List<TestPlan> summaryBugOnLibrarieIds(@RequestParam(value = "librarieIds", required = false) List<Long> librarieIds) {
        List<TestPlan> plans = new ArrayList<>();
        for (Long librarieId : librarieIds) {
            // 根据产品库找到关联的测试计划
            plans.addAll(testPlanService.list(Wraps.<TestPlan>lbQ().eq(TestPlan::getLibraryId, librarieId).isNotNull(TestPlan::getLibraryId)));
        }
        return plans;
    }


    @GetMapping("/productCaseLibraryConnect/echo/deleteByConnectId")
    public R deleteByConnectId(@RequestParam(value = "ids") List<Long> ids)
    {
        if (Objects.equals(ids.size(),0) || Objects.equals(ids, null)){

            return R.fail("参数传递异常！");
        }
        productCaselibraryConnectService.remove(Wraps.<ProductCaselibraryConnect>lbQ().in(ProductCaselibraryConnect::getProductId,ids));
        return R.success();
    }

    @GetMapping("/productCaseLibraryConnect/echo/selectListByConnectId")
    public List<ProductCaselibraryConnect> selectListByConnectId(@RequestParam(value = "ids") List<Long> ids)
    {
        List<ProductCaselibraryConnect> list = productCaselibraryConnectService
                .list(Wraps.<ProductCaselibraryConnect>lbQ().in(ProductCaselibraryConnect::getProductId, ids));
        return list;
    }

    @GetMapping("/testPlan/getOverView")
    public TestOverview getOverView() {
        TestOverview one = testOverviewService.getOne(Wraps.<TestOverview>lbQ());
        return one;
    }

    @PostMapping("/testPlan/updateOverview")
    public void updateOverview(@RequestBody TestOverview testOverview) {
        testOverviewService.updateById(testOverview);
    }

    @GetMapping("/testCaseRepository/echo/deleteLibrary")
    public void deleteLibrary(@RequestParam(value = "ids") List<Long> ids) {
        List<Long> collect = testPlanService.list(Wraps.<TestPlan>lbQ().in(TestPlan::getLibraryId, ids)).stream()
                .map(TestPlan::getId)
                .collect(Collectors.toList());
        if (collect.size() != 0){
            testPlanCaseService.remove(Wraps.<TestPlanCase>lbQ().in(TestPlanCase::getPlanId,collect));
        }
        testPlanService.remove(Wraps.<TestPlan>lbQ().in(TestPlan::getLibraryId,ids));
        testProductCaseService.remove(Wraps.<TestProductCase>lbQ().in(TestProductCase::getLibraryId,ids));
        productCaseLibraryService.remove(Wraps.<ProductCaseLibrary>lbQ().in(ProductCaseLibrary::getProjectId,ids));
    }

    @GetMapping("/testPlan/getById")
    public TestPlan getById(@RequestParam(value = "planId") Long planId) {
        return testPlanService.getById(planId);
    }

    @GetMapping("/testPlan/getTestReportById")
    public TestReport getTestReportById(@RequestParam(value = "planId") Long planId) {
        return testReportService.getById(planId);
    }

    @GetMapping("/testReport/findByIds")
    public Map<Serializable, Object> findByIds(@RequestParam("ids") Set<Serializable> ids)
    {
        return testReportService.findByIds(ids);
    }

    @PutMapping("/case/findCasesByIds")
    public List<TestProductCase> findCasesByIds(@RequestBody List<String> ids)
    {
        List<TestProductCase> caseList = testProductCaseService.list(
                Wraps.<TestProductCase>lbQ().in(TestProductCase::getId, ids));

        Set<Long> libIds = caseList.stream().map(TestProductCase::getLibraryId).collect(Collectors.toSet());
        Map<Long, ProductCaseLibrary> libraryMap = productCaseLibraryService.listByIds(libIds).stream()
                .collect(Collectors.toMap(SuperEntity::getId, Function.identity()));

        caseList.forEach(item -> {
            Long libraryId = item.getLibraryId();
            if (libraryId == null) {
                return;
            }
            item.getEchoMap().put("libraryId", libraryMap.get(libraryId));
        });

        return caseList;
    }

    @GetMapping("/case/findCasesByName")
    public List<TestProductCase> findCasesByName(@RequestParam("name") String name)
    {
        return testProductCaseService.list(Wraps.<TestProductCase>lbQ().like(TestProductCase::getName, name));
    }

    @GetMapping("/testPlan/findByIds")
    public Map<Serializable, Object> findTestPlanByIds(@RequestParam(value = "ids") Set<Serializable> ids){
        Map<Serializable, Object> testPlanMap = testPlanService.findByIds(ids);
        return testPlanMap;
    }

    @GetMapping("/scheme/echo/getById")
    public TestScheme getTestSchemeById(Long id){
        return testSchemeService.getById(id);
    }

    @PutMapping("/scheme/echo")
    boolean updateTestScheme(TestScheme testScheme){
        return testSchemeService.updateById(testScheme);
    }


    @GetMapping("/case/queryTestCaseList")
    public List<TestCase> queryTestCaseList(@RequestParam("testcaseTreeIds") String testcaseTreeIds)
    {
        LbqWrapper<TestCase> queryWrapTreeLbuWrapper = Wraps.lbQ();
        return testCaseService.list(queryWrapTreeLbuWrapper);
    }

    @GetMapping("/testTaskCase/echo/queryByTaskId")
    public List<TestTaskCase> queryByTaskId(@RequestParam("taskId")Long taskId)
    {
        LbqWrapper<TestTaskCase> queryWrapLbuWrapper = Wraps.lbQ();
        queryWrapLbuWrapper.eq(TestTaskCase::getTaskId, taskId);
        return testTaskCaseService.list(queryWrapLbuWrapper);
    }

    @GetMapping("/case/queryTestCaseByIds")
    public List<TestCase> queryTestCaseByIds(@RequestParam("testCaseIds")List<Long> testCaseIds)
    {
        return testCaseService.queryTestCaseByIds(testCaseIds);
    }
    @GetMapping("/testProductCase/findByIds")
    public Map<Serializable, Object> findTestProductCase(@RequestParam(value = "ids") Set<Serializable> ids){
        Map<Serializable, Object> testPlanMap = testProductCaseService.findByIds(ids);
        return testPlanMap;
    }
    @GetMapping("/testProductCase/echo/queryByTestCaseId")
    public TestProductCase queryByTestCaseId(@RequestParam("testCaseId")Long testCaseId)
    {
        return testProductCaseService.getById(testCaseId);
    }

    @GetMapping("/testProductCase/echo/findByIds")
    public Map<Serializable, Object> findTestProductCaseByIds(@RequestParam(value = "ids") Set<Serializable> ids){
        return testProductCaseService.findByIds(ids);
    }

    @GetMapping("/testProductCase/echo/findAll")
    public List<TestProductCase> findAll(){
        return testProductCaseService.list();
    }

    @PostMapping("/testTaskCase/saveTestTaskCaseBatch")
    public boolean saveTestTaskCaseBatch(@RequestParam("testTaskCaseList") List<TestTaskCase> testTaskCaseList)
    {
        return testTaskCaseService.saveBatch(testTaskCaseList);
    }

    @PostMapping("/testTaskCase/echo/delTestTaskCaseBatch")
    public void delTestTaskCaseBatch(@RequestParam("listAll") List<TestTaskCase> listAll)
    {
        testTaskCaseService.delBatch(listAll);
    }

    @GetMapping("/testProductCase/echo/queryProductCaseByIds")
    public List<TestProductCase> queryProductCaseByIds(@RequestParam("testCaseIds")List<Long> testCaseIds)
    {
        return testProductCaseService.queryProductCaseByIds(testCaseIds);
    }

    @GetMapping("/testTaskCase/echo/getByTestCaseIdAndTaskId")
    public TestTaskCase getByTestCaseIdAndTaskId(@RequestParam("testCaseId") Long testCaseId,@RequestParam("taskId") Long taskId)
    {
        return testTaskCaseService.getOne(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getTestcaseId, testCaseId).eq(TestTaskCase::getTaskId,taskId));
    }

    @GetMapping("/requirementFunctionPoints/listByProjectIdAndTestReqId")
    public List<TestRequirementFunctionPoints> listByProjectIdAndTestReqId(@RequestParam(value = "projectId") Long projectId,
                                                                              @RequestParam(value = "testReqId") Long testReqId) {
        if (projectId == null) {
            return Collections.emptyList();
        }
        LbqWrapper<TestRequirementFunctionPoints> wrapper = Wraps.lbQ();
        wrapper.eq(TestRequirementFunctionPoints::getProjectId, projectId);
        if(testReqId != 0){
           wrapper.eq(TestRequirementFunctionPoints::getIssueTestReqId, testReqId);
        }
        List<TestRequirementFunctionPoints> list = testRequirementFunctionPointsService.list(wrapper);
        return list;
    }

    @GetMapping("/testProductCase/echo/queryProductCaseByTaskId")
    public List<TestProductCase> queryProductCaseByTaskId(@RequestParam("testTaskIds") List<Long> testTaskIds) {
        if (CollUtil.isEmpty(testTaskIds)) {
            return Collections.emptyList();
        }
        LbqWrapper<TestProductCase> wrapper = Wraps.lbQ();
        wrapper.in(TestProductCase::getTaskId, testTaskIds);
        List<TestProductCase> testProductCases = testProductCaseService.list(wrapper);
        return testProductCases;
    }

    @GetMapping("/testTaskCase/echo/getByFunctionIdAndTaskId")
    public List<TestTaskCase> getByFunctionIdAndTaskId(@RequestParam("moduleFunctionId") Long moduleFunctionId,@RequestParam("taskId") Long taskId)
    {
        return testTaskCaseService.list(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getModuleFunctionId, moduleFunctionId).eq(TestTaskCase::getTaskId,taskId));
    }

    @GetMapping("/testProductCase/echo/getProductCaseByFunctionIdAndTaskId")
    public List<TestProductCase> getProductCaseByFunctionIdAndTaskId(@RequestParam("moduleFunctionId")Long moduleFunctionId, @RequestParam("taskId")Long taskId)
    {
        return testProductCaseService.list(Wraps.<TestProductCase>lbQ().eq(TestProductCase::getModuleFunctionId, moduleFunctionId).eq(TestProductCase::getTaskId,taskId));
    }

    @GetMapping("/requirementFunctionPoints/echo/getPointsByFunctionIdAndTaskId")
    public List<TestRequirementFunctionPoints> getPointsByFunctionIdAndTaskId(@RequestParam("moduleFunctionId")Long moduleFunctionId, @RequestParam("taskId")Long taskId)
    {
        return testRequirementFunctionPointsService.list(Wraps.<TestRequirementFunctionPoints>lbQ().eq(TestRequirementFunctionPoints::getFunctionId, moduleFunctionId).eq(TestRequirementFunctionPoints::getTaskId,taskId));
    }

    @GetMapping("/testTaskCase/echo/queryByFunctionIds")
    public List<TestTaskCase> queryByFunctionIds(@RequestParam("bizId")Long bizId, @RequestParam("functionIds")List<Long> functionIds)
    {
        return testTaskCaseService.list(Wraps.<TestTaskCase>lbQ().in(TestTaskCase::getModuleFunctionId, functionIds).eq(TestTaskCase::getTaskId,bizId));
    }

    @GetMapping("/requirementFunctionPoints/echo/queryPointsByTaskId")
    public List<TestRequirementFunctionPoints> queryPointsByTaskId(@RequestParam("taskId")Long taskId)
    {
        return testRequirementFunctionPointsService.list(Wraps.<TestRequirementFunctionPoints>lbQ().eq(TestRequirementFunctionPoints::getTaskId,taskId));
    }

    @GetMapping("/testTaskCase/echo/queryByTaskIdAndStatus")
    public List<TestTaskCase> queryByTaskIdAndStatus(@RequestParam("taskId")Long taskId, @RequestParam("status")String status)
    {
        return testTaskCaseService.list(Wraps.<TestTaskCase>lbQ().in(TestTaskCase::getTaskId, taskId).eq(TestTaskCase::getStatus,status));
    }

    @GetMapping("/testTaskCase/echo/findByIds")
    public Map<Serializable, Object> findTestTaskCaseByIds(@RequestParam(value = "ids") Set<Serializable> ids){
        return testTaskCaseService.findByIds(ids);
    }

    @GetMapping("/requirementFunctionPoints/echo/findByIds")
    public Map<Serializable, Object> findPointsByIds(@RequestParam(value = "ids") Set<Serializable> ids){
        return testRequirementFunctionPointsService.findByIds(ids);
    }

}
