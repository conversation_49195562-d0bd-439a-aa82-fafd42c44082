package com.jettech.jettong.testm.controller.testTask;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.repository.CacheOps;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.StringUtil;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.alm.api.ProjectApi;
import com.jettech.jettong.alm.api.RequirementApi;
import com.jettech.jettong.alm.issue.entity.Requirement;
import com.jettech.jettong.alm.project.entity.ProjectUser;
import com.jettech.jettong.base.api.DictionaryApi;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.base.enumeration.dictionary.DictionaryRelationBizType;
import com.jettech.jettong.common.cache.testm.TestMCacheKeyBuilder;
import com.jettech.jettong.common.util.poi.ExcelDownLoadUtil;
import com.jettech.jettong.common.util.poi.ExcelExportPlusUtil;
import com.jettech.jettong.product.api.ProductInfoApi;
import com.jettech.jettong.product.api.ProductModuleFunctionApi;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.controller.poi.TestCaseExcelDictHandlerImpl;
import com.jettech.jettong.testm.controller.poi.TestProductCaseExcelDictHandlerImpl;
import com.jettech.jettong.testm.controller.poi.TestProductCaseExcelVerifyHandlerImpl;
import com.jettech.jettong.testm.dto.TestProductCaseExportQuery;
import com.jettech.jettong.testm.dto.TestTaskCaseExportQuery;
import com.jettech.jettong.testm.entity.*;
import com.jettech.jettong.testm.service.*;
import com.jettech.jettong.testm.vo.TestProductCaseExportCataExcelVo;
import com.jettech.jettong.testm.vo.TestProductCaseExportExcelVO;
import com.jettech.jettong.testm.vo.TestProductCaseImportExcelVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Base64Utils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Size;
import javax.validation.groups.Default;
import java.io.*;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.jettech.jettong.common.constant.SwaggerConstants.DATA_TYPE_LONG;
import static com.jettech.jettong.common.constant.SwaggerConstants.PARAM_TYPE_QUERY;
import static org.apache.poi.ss.usermodel.Font.COLOR_RED;


/**
 * 测试案例导入导出信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例导入导出信息控制器
 * @projectName jettong
 * @package com.jettech.jettong.alm.controller.issue
 * @className TaskExcelController
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/testTaskCase/excel")
@Api(value = "Task", tags = "案例库导入导出")
@RequiredArgsConstructor
public class TestTaskCaseExcelController extends
        SuperSimpleController<TestTaskCaseService, TestTaskCase> {
    private final UserApi userApi;

    private final TestProductCaseExcelDictHandlerImpl testProductCaseExcelDictHandlerImpl;

    private final TestProductCaseExcelVerifyHandlerImpl testProductCaseExcelVerifyHandlerImpl;

    private final CacheOps cacheOps;

    private final ProductInfoApi productInfoApi;

    public final ProductModuleFunctionApi productModuleFunctionApi;


    private static final String SHEETNAME = "测试执行";


    private final DictionaryApi dictionaryApi;


    @ApiOperation(value = "导出Excel")
    @PostMapping(value = "/exportTestTaskCaseExcel", produces = "application/octet-stream")
    @SysLog(value = "'导出Excel", optType = OptLogTypeEnum.EXPORT)
    public void exportTestTaskCaseExcel(@Validated @RequestBody TestTaskCaseExportQuery model, HttpServletResponse response)
    {

        try {
            List<List<ProductModuleFunction>> cascadeData = initializeHandlers(testProductCaseExcelDictHandlerImpl, null,
                    model.getProjectId(), model.getTaskId(), model.getTestreqId());

            // 根据条件查询待导出数据
            List<Long> functionIds = new ArrayList<>();
            if (model.getFunctionAndModuleId() != null && model.getFunctionAndModuleId() != 0L) {
                functionIds = productInfoApi.findByFunctionId(model.getFunctionAndModuleId());

                if (functionIds.isEmpty()) {
                    // 如果没有匹配的功能ID，则导出空文件
                    try (Workbook workbook = buildExcelExportWorkbook(testProductCaseExcelDictHandlerImpl, new ArrayList<>(), cascadeData)) {
                        ExcelDownLoadUtil.export(response, workbook, "测试执行信息.xlsx");
                    }
                    return;
                }
            }

            // 1.查询导出数据
            LbqWrapper<TestTaskCase> wrapper = Wraps.lbQ();
            wrapper.eq(model.getStatus()!=null,TestTaskCase::getStatus, model.getStatus())
                    .like(model.getName()!=null,TestTaskCase::getName, model.getName())
                    .eq(model.getExecBy()!=null,TestTaskCase::getExecBy, model.getExecBy())
                    .eq(model.getPriority()!=null,TestTaskCase::getPriority,model.getPriority())
                    .like(model.getCaseKey()!=null,TestTaskCase::getCaseKey,model.getCaseKey())
                    .eq(model.getTaskId()!=null,TestTaskCase::getTaskId,model.getTaskId())
                    .eq(model.getTestcaseId()!=null,TestTaskCase::getTestcaseId,model.getTestcaseId())
                    .eq(model.getModuleFunctionId()!=null,TestTaskCase::getModuleFunctionId,model.getModuleFunctionId())
                    .eq(model.getProjectId()!=null,TestTaskCase::getProjectId,model.getProjectId())
                    .eq(model.getTestreqId()!=null,TestTaskCase::getTestreqId,model.getTestreqId())
                    .like(model.getVersion()!=null,TestTaskCase::getVersion,model.getVersion())
                    .in(!functionIds.isEmpty(),TestTaskCase::getModuleFunctionId,functionIds);

            List<TestTaskCase> list = baseService.list(wrapper);

            List<ProductModuleFunction> functions = cascadeData.get(1);
            // 构建功能id 和 productId map
            Map<Long, Long> functionIdToProductId = functions.stream()
                    .collect(Collectors.toMap(ProductModuleFunction::getId, ProductModuleFunction::getProductId));

            List<Long> caseIds = list.stream().map(TestTaskCase::getTestcaseId).collect(Collectors.toList());
            Map<Long, List<Dictionary>> dictionaryMap = dictionaryApi.batchEchoDictionaryByBizIdsAndType(
                    caseIds, DictionaryRelationBizType.CASE.getCode());
            for (TestTaskCase datum : list) {
                // Safely handle potential null in map lookup
                Long productId = functionIdToProductId.get(datum.getModuleFunctionId());
                if (productId != null) {
                    datum.setProductId(productId);
                }

                List<Dictionary> dictionaries = Optional.ofNullable(
                        dictionaryMap.get(datum.getId())
                ).orElse(Collections.emptyList());
                if (!dictionaries.isEmpty()) {
                    // 流式处理获取测试模式列表
                    List<String> testModes = dictionaries.stream()
                            .map(Dictionary::getCode)
                            .collect(Collectors.toList());

                    datum.setTestMode(String.join(",",testModes));
                }

            }
            // 3.执行excel导出
            try (Workbook workbook = buildExcelExportWorkbook(testProductCaseExcelDictHandlerImpl, list, cascadeData)) {
                testProductCaseExcelDictHandlerImpl.removeThreadLocal();
                ExcelDownLoadUtil.export(response, workbook, "测试执行信息.xlsx");
            }
        } catch (Exception e){
            log.error("导出测试用例数据失败", e);
            throw new BizException("导出测试用例数据失败: " + e.getMessage());
        }
    }

    /**
     * 设置字典及验证处理器并返回级联数据
     * @param dicHandler
     * @param verifyHandler
     * @param projectId
     * @param taskId
     * @param testReqId
     * @return {@link List< List< ProductModuleFunction>>}
     * @throws
     * <AUTHOR>
     * @date 2025/9/11 15:35
     * @update wzj 2025/9/11 15:35
     * @since 1.0
     */
    private List<List<ProductModuleFunction>> initializeHandlers(TestProductCaseExcelDictHandlerImpl dicHandler, TestProductCaseExcelVerifyHandlerImpl verifyHandler, Long projectId, Long taskId, Long testReqId) {

        // 设置字典处理器
        List<ProductModuleFunction> moduleFunctions = productModuleFunctionApi.findModuleFunctionByProjectId(
                projectId, taskId, testReqId, null);

        List<Long> productIds = moduleFunctions.stream()
                .map(ProductModuleFunction::getProductId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        List<ProductInfo> productInfos = productInfoApi.selectProductInfoByIds(productIds);
        dicHandler.setProductAndFunction(productInfos, moduleFunctions);
        List<Long> moduleFunctionIds = moduleFunctions.stream().map(ProductModuleFunction::getId).collect(Collectors.toList());
        dicHandler.setFunctionPoints(moduleFunctionIds);

        // 如果校验处理器存在，则初始化
        if (verifyHandler != null) {
            verifyHandler.init(moduleFunctions);
        }

        // 准备并返回级联数据
        List<ProductModuleFunction> tradeFunctions = moduleFunctions.stream()
                .filter(mf -> mf.getNodeType() == 2)
                .peek(tf -> tf.setParentId(tf.getProductId()))
                .collect(Collectors.toList());

        List<ProductModuleFunction> systemEntities = productInfos.stream()
                .map(pi -> {
                    ProductModuleFunction treeEntity = new ProductModuleFunction();
                    treeEntity.setId(pi.getId());
                    treeEntity.setName(pi.getName());
                    treeEntity.setParentId(null);
                    return treeEntity;
                })
                .collect(Collectors.toList());

        return Arrays.asList(systemEntities, tradeFunctions);
    }

    private Workbook buildExcelExportWorkbook(TestProductCaseExcelDictHandlerImpl dicHandler,
                                              List<TestTaskCase> data, List<List<ProductModuleFunction>> sysAndTradeData)
    {
        ExportParams exportParams = new ExportParams();



        exportParams.setSheetName(SHEETNAME);
        exportParams.setDictHandler(testProductCaseExcelDictHandlerImpl);
        exportParams.setType(ExcelType.XSSF);
        exportParams.setTitle("测试执行数据");
        List<TestTaskCase> exportData = new ArrayList<>();
        if (data == null)
        {
            exportData = new ArrayList<>();
        }else{
            for(TestTaskCase testTaskCase:data){
                String stepType = testTaskCase.getStepType();
                if ("text".equals(stepType)) {
                    TestTaskCase productCase = BeanUtil.toBean(testTaskCase,TestTaskCase.class);
                    exportData.add(productCase);

                } else {
                    String testStep = testTaskCase.getTestStep();
                    // Check for null before processing
                    if (testStep == null) {
                        continue;
                    }
                    boolean jsonType = getJsonType(testStep);
                    JSONArray jsonArray = JSONArray.parseArray(testStep);

                    // 步骤不是json数组时候逻辑
                    if (!jsonType || jsonArray == null || jsonArray.size() == 0) {
                        TestTaskCase productCase = BeanUtil.toBean(testTaskCase,TestTaskCase.class);
                        productCase.setTestStep(testTaskCase.getTestStep());
                        productCase.setExpectedResult(testTaskCase.getExpectedResult());
                        exportData.add(productCase);
                        continue;
                    }

                    for (int j = 0; j < jsonArray.size(); j++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(j);

                        String caseStepDes = jsonObject.getString("caseStepDes");
                        String expectResult = jsonObject.getString("expectResult");
                        String status = jsonObject.getString("status");
                        if (j == 0) {
                            TestTaskCase productCase = BeanUtil.toBean(testTaskCase,TestTaskCase.class);
                            productCase.setTestStep(caseStepDes);
                            productCase.setExpectedResult(expectResult);
                            productCase.setSingleExecResult(status == null?"":status);
                            exportData.add(productCase);
                        } else {
                            TestTaskCase productCase = new TestTaskCase();
                            productCase.setTestStep(caseStepDes);
                            productCase.setExpectedResult(expectResult);
                            productCase.setSingleExecResult(status == null?"":status);
                            exportData.add(productCase);
                        }

                    }
                }
            }
        }


        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, TestTaskCase.class, exportData);

        Sheet sheet = workbook.getSheetAt(0);
        Row row = sheet.getRow(0);
        if (row != null) {
            // 设置title单元格行高
            row.setHeightInPoints(47);
            Cell cell = row.getCell(0);
            if (cell != null) {
                CellStyle cellStyle = cell.getCellStyle();
                // 设置title单元格\n强制换行
                cellStyle.setWrapText(true);
                Font font = workbook.createFont();
                font.setBold(true);
                font.setColor(COLOR_RED);
                cellStyle.setFont(font);
                // 设置title单元格居左
                cellStyle.setAlignment(HorizontalAlignment.LEFT);
            }
        }

        // 设置级联
        ExcelExportPlusUtil.createMultiLevelCascade(workbook, SHEETNAME, sysAndTradeData,
                new ExcelExportPlusUtil.CascadeConfig(0, 2, 9999));

        return workbook;
    }

    /**
     * 根据json特征判断是都是json字符串，判断是都是json或者json数组
     *
     * @param str
     * @return
     */
    public static boolean getJsonType(String str) {
        boolean result = false;
        if (StringUtils.isNotBlank(str)) {
            str = str.trim();
            if (str.startsWith("{") && str.endsWith("}")) {
                result = true;
            } else if (str.startsWith("[") && str.endsWith("]")) {
                result = true;
            }
        }
        return result;
    }

}
