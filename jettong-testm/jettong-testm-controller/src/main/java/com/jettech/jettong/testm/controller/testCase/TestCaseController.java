package com.jettech.jettong.testm.controller.testCase;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.StringUtil;
import com.jettech.jettong.base.api.DictionaryApi;
import com.jettech.jettong.base.api.FileApi;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.dto.sys.dictionary.SysDictionaryRelationSaveDTO;
import com.jettech.jettong.base.entity.file.File;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.base.enumeration.dictionary.DictionaryRelationBizType;
import com.jettech.jettong.base.enumeration.file.FileBizType;
import com.jettech.jettong.common.constant.DictionaryType;
import com.jettech.jettong.product.api.ProductModuleFunctionApi;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.dao.TestPlanCaseMapper;
import com.jettech.jettong.testm.dao.TestPlanCaseResultMapper;
import com.jettech.jettong.testm.dao.TestPlanMapper;
import com.jettech.jettong.testm.dto.TestCasePageQuery;
import com.jettech.jettong.testm.dto.TestCaseSaveDTO;
import com.jettech.jettong.testm.dto.TestCaseUpdateDTO;
import com.jettech.jettong.testm.entity.*;
import com.jettech.jettong.testm.service.TestCaseService;
import com.jettech.jettong.testm.service.TestProductCaseTestModeService;
import com.jettech.jettong.testm.service.TestTabCaseService;
import com.jettech.jettong.testm.vo.TestCaseTypeComponentResult;
import com.jettech.jettong.testm.vo.TestProductCaseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

import static com.jettech.jettong.common.constant.SwaggerConstants.DATA_TYPE_LONG;
import static com.jettech.jettong.common.constant.SwaggerConstants.PARAM_TYPE_PATH;


/**
 * 测试案例表控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例表控制器
 * @projectName jettong
 * @package com.jettech.jettong.testm.controller
 * @className TestCaseController
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/testCase")
@Api(value = "TestCase", tags = "测试案例表")
@RequiredArgsConstructor
public class TestCaseController
        extends SuperController<TestCaseService, Long, TestCase, TestCasePageQuery, TestCaseSaveDTO, TestCaseUpdateDTO>
{

    private final TestPlanCaseResultMapper testPlanCaseResultMapper;
    private final TestPlanCaseMapper testPlanCaseMapper;
    private final TestPlanMapper testPlanMapper;
    private final TestTabCaseService testTabCaseService;
    private final UserApi userApi;
    private final TestProductCaseTestModeService testProductCaseTestModeService;
    private final FileApi fileApi;
    private final DictionaryApi dictionaryApi;
    private final ProductModuleFunctionApi productModuleFunctionApi;
    private final EchoService echoService;



    @Override
    public R<Boolean> delete(List<Long> longs)
    {
        baseService.removeByIds(longs);
        return R.success(true, "删除用例成功！");
    }

    @Override
    public R<TestCase> handlerSave(@Validated TestCaseSaveDTO model)
    {
        TestCase testCase = BeanUtil.toBean(model, TestCase.class);
        List<String> testModes = testCase.getTestModes();
        if(!testCase.getTestModes().isEmpty()){
            //子平台赋值
            int sum = testModes.stream()
                    .mapToInt(Integer::parseInt)
                    .sum();
            testCase.setTestMode(String.valueOf(sum));
        } else {
            testCase.setTestMode("0");
        }
        baseService.saveTestCase(testCase);

        //添加项目和测试方式的关联
        SysDictionaryRelationSaveDTO sysDictionaryRelation = new SysDictionaryRelationSaveDTO()
                .setBizId(testCase.getId())
                .setBizType(DictionaryRelationBizType.CASE.getCode())
                .setDictionaryType(DictionaryType.PROJECT_TESTMODE)
                .setDictionaryCodes(testCase.getTestModes());
        dictionaryApi.saveOrUpdateDictionaryRelation(sysDictionaryRelation);
        testTabCaseService.save(testCase.getId(), model.getTabInfo(), "case");
        return success(testCase);
    }

    @ApiOperation(value = "查询条件下用例", notes = "查询条件下用例")
    @PostMapping("/findTestCaseOfCondition")
    @SysLog(value = "查询条件下用例", request = false)
    public R<List<TestCase>> findTestCaseOfCondition(@RequestBody TestCase testCase)
    {
        LbqWrapper<TestCase> queryWrapLbuWrapper = Wraps.lbQ();


        if (!Objects.equals(testCase.getName(), null)) {
            queryWrapLbuWrapper.like(TestCase::getName, testCase.getName());
        }
        if (!Objects.equals(testCase.getCaseKey(), null)) {
            queryWrapLbuWrapper.like(TestCase::getCaseKey, testCase.getCaseKey());
        }
        if (!Objects.equals(testCase.getPriority(), null)) {
            queryWrapLbuWrapper.eq(TestCase::getPriority, testCase.getPriority());
        }
        if (!Objects.equals(testCase.getTabName(), null)) {
            List<Long> caseIDs = testTabCaseService.getCasesOfTabNames(testCase.getTabName()).stream().map(t -> t.getId()).collect(Collectors.toList());
            if(caseIDs.size() > 0) {
                queryWrapLbuWrapper.in(TestCase::getId, caseIDs);
            }
        }
        List<TestCase> list = baseService.list(queryWrapLbuWrapper);
        list.forEach(t -> t.getEchoMap().put("defender", null == t.getLeadingBy() ? null : userApi.findUserById(t.getLeadingBy()).getName()));
        return R.success(list, "查询用例库下的用例成功！");
    }

    @ApiOperation(value = "批量插入用例", notes = "批量插入用例")
    @PostMapping("/insertCaseBath/{isSyn}/{treeId}")
    @SysLog(value = "批量插入用例", request = false)
    public R insertCaseBath(@RequestBody List<TestCase> testCaseList, @PathVariable Long treeId, @PathVariable String isSyn)
    {
        if (Objects.equals(isSyn, "0")){


            baseService.saveBatchSomeColumn(testCaseList);
        }else{
            baseService.saveBatchSomeColumn(testCaseList);
        }
        return R.success("批量插入批量插入用例成功！");
    }

    @ApiOperation(value = "批量修改用例", notes = "批量修改用例")
    @PutMapping("/updateCaseBath/{treeId}")
    @SysLog(value = "批量修改用例", request = false)
    public R updateCaseBath(@RequestBody List<String> testCaseIdList, @PathVariable String treeId)
    {
        List<TestCase> testCaseList = baseService.listByIds(testCaseIdList);

        baseService.updateBatchById(testCaseList, testCaseList.size());
        return R.success(null, "批量插入批量插入用例成功！");
    }

    @ApiOperation(value = "查询测试计划下用例", notes = "查询测试计划下用例")
    @GetMapping("/findTestCaseOfTestPlan/{planId}")
    @SysLog(value = "查询测试计划下用例", request = false)
    public R<List<TestCase>> findTestCaseOfTestPlan(@PathVariable Long planId)
    {
        List<TestCase> testCaseOfTestPlan = baseService.findTestCaseOfTestPlan(planId);
        return R.success(testCaseOfTestPlan, "查询测试计划下用例成功！");
    }

//    @ApiOperation(value = "查询测试计划下用例", notes = "查询测试计划下用例")
//    @PostMapping("/findTestCasesOfPlan")
//    @SysLog(value = "查询测试计划下用例", request = false)
//    @PreAuth("hasAnyPermission('{}view')")
//    public R<List<TestCase>> findTestCasesOfPlan(@RequestBody TestPlanCase data) {
//
//        LbqWrapper<TestPlanCase> queryWrapLbuWrapper = Wraps.lbQ();
//        queryWrapLbuWrapper.eq(TestPlanCase::getPlanId, data.getPlanId());
//        List<TestPlanCase> lists = testPlanCaseService.list(queryWrapLbuWrapper);
//        List<Long> testcaseIds = lists.stream().map(TestPlanCase::getTestcaseId).collect(Collectors.toList());
//        List<TestCase> testCases = baseService.listByIds(testcaseIds);
//        return success(testCases);
//    }

    @ApiOperation(value = "复制用例树下用例", notes = "复制用例树下用例")
    @PutMapping("/copyTreeById")
    @SysLog(value = "复制用例树下用例", request = false)
    public R<Boolean> copyTreeById(@RequestBody TestProductCaseVO param)
    {
        Long treeId = param.getTreeId();
        List<Long> caseId = param.getCaseId();
        if(caseId.size()==0 || caseId.get(0)==null){
            throw BizException.validFail("请勾选测试用例");
        }
        List<TestCase> testCases = new ArrayList<>();
        caseId.forEach(item->
        {
            baseService.copyCaseById(item,treeId);

        });
        baseService.saveBatch(testCases);
        return R.success();
    }

    @Override
    public R<TestCase> get(Long id) {
        TestCase byId = baseService.getById(id);
        List<File> caseFiles =
                fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_FILE_UPLOAD, byId.getId());
        byId.setFiles(caseFiles);
        byId.setIntentFiles(fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_INTENT_FILE_UPLOAD, byId.getId()));
        byId.setPrerequisiteFiles(fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_PREREQUISITE_FILE_UPLOAD, byId.getId()));
        byId.setTestStepFiles(fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_TESTSTEP_FILE_UPLOAD, byId.getId()));

        byId.getEchoMap().put("leadingBy",userApi.findUserById(byId.getLeadingBy()));
        byId.setTabs(testTabCaseService.getTabsOfCase(byId.getId(), "product"));
        ProductModuleFunction moduleFunction = productModuleFunctionApi.findProductModuleFunctionById(byId.getModuleFunctionId());
        byId.setModuleFunctionName(moduleFunction.getName());
        //回显
        List<Dictionary> dictionaries = dictionaryApi.echoDictionaryByBizIdAndType(
                id, DictionaryRelationBizType.CASE.getCode());
        if (dictionaries != null && !dictionaries.isEmpty()) {
            List<String> testModes = dictionaries.stream()
                    .map(Dictionary::getCode)
                    .collect(Collectors.toList());
            byId.setTestModes(testModes);
            byId.getEchoMap().put("testModes",dictionaries);
        }
        return success(byId);
    }

    @Override
    public R<TestCase> handlerUpdate(TestCaseUpdateDTO model) {
        TestCase testCase = BeanPlusUtil.toBean(model, TestCase.class);
        TestCase byId = baseService.getById(model.getId());
        List<String> testModes = testCase.getTestModes();
        if (!testModes.isEmpty()) {
            // 保存字典关系
            SysDictionaryRelationSaveDTO sysDictionaryRelation = new SysDictionaryRelationSaveDTO()
                    .setBizId(testCase.getId())
                    .setBizType(DictionaryRelationBizType.CASE.getCode())
                    .setDictionaryType(DictionaryType.PROJECT_TESTMODE)
                    .setDictionaryCodes(testModes);
            dictionaryApi.saveOrUpdateDictionaryRelation(sysDictionaryRelation);
            //子平台赋值
            int sum = testModes.stream()
                    .mapToInt(Integer::parseInt)
                    .sum();
            testCase.setTestMode(String.valueOf(sum));
        } else {
            testCase.setTestMode("0");
        }
        baseService.updateById(testCase);
        testTabCaseService.update(model.getId(), model.getTabInfo(), "case");
        // 更新附件文件
        Long caseId = model.getId();
        List<File> newFiles = model.getFiles();

        // 注意：这里判空，不能判集合是否有元素。集合无元素代表，删除原来的关联关系
        if (newFiles != null) {
            // 删除原来的附件关系
            List<File> oldFile = fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_FILE_UPLOAD, caseId);
            oldFile.forEach(file -> file.setBizId(null));
            fileApi.updateBatchById(oldFile);

            // 建立新的 附件关系
            newFiles.forEach(file -> file.setBizId(caseId).setBizType(FileBizType.TEST_PRODUCT_CASE_FILE_UPLOAD));
            fileApi.updateBatchById(newFiles);
        }

        //更新用例意图附件
        if (model.getIntentFiles() != null){
            List<File> files = model.getIntentFiles();
            // 删除原来的附件关系
            List<File> oldFile = fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_INTENT_FILE_UPLOAD, caseId);
            oldFile.forEach(file -> file.setBizId(null));
            fileApi.updateBatchById(oldFile);

            // 建立新的 附件关系
            files.forEach(file -> file.setBizId(caseId).setBizType(FileBizType.TEST_PRODUCT_CASE_INTENT_FILE_UPLOAD));
            fileApi.updateBatchById(files);
        }

        //更新用例前置条件附件
        if (model.getPrerequisiteFiles() != null){
            List<File> files = model.getPrerequisiteFiles();
            // 删除原来的附件关系
            List<File> oldFile = fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_PREREQUISITE_FILE_UPLOAD, caseId);
            oldFile.forEach(file -> file.setBizId(null));
            fileApi.updateBatchById(oldFile);

            // 建立新的 附件关系
            files.forEach(file -> file.setBizId(caseId).setBizType(FileBizType.TEST_PRODUCT_CASE_PREREQUISITE_FILE_UPLOAD));
            fileApi.updateBatchById(files);
        }

        //更新用例测试步骤附件
        if (model.getTestStepFiles() != null){
            List<File> files = model.getTestStepFiles();
            // 删除原来的附件关系
            List<File> oldFile = fileApi.findByBizTypeAndBizId(FileBizType.TEST_PRODUCT_CASE_TESTSTEP_FILE_UPLOAD, caseId);
            oldFile.forEach(file -> file.setBizId(null));
            fileApi.updateBatchById(oldFile);

            // 建立新的 附件关系
            files.forEach(file -> file.setBizId(caseId).setBizType(FileBizType.TEST_PRODUCT_CASE_TESTSTEP_FILE_UPLOAD));
            fileApi.updateBatchById(files);
        }
        return success(BeanUtil.toBean(model, TestCase.class));
    }

    @ApiOperation(value = "批量打标签", notes = "批量打标签")
    @GetMapping("/tabBatch")
    @SysLog(value = "批量打标签", request = false)
    public R tabBatch(@RequestParam List<Long> caseIds, @RequestParam String[] tabInfo)
    {
        for (Long t : caseIds) {
            testTabCaseService.update(t, tabInfo, "case");
        }
        return success();
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @ApiOperation(value = "查询项目用例概览组件", notes = "查询项目用例概览组件")
    @GetMapping("/findTypeByProjectId/{projectId}")
    @SysLog("查询项目用例概览组件")
    public R<TestCaseTypeComponentResult> findTypeByProjectId(@PathVariable("projectId") Long projectId)
    {
        return R.success(baseService.findTypeByProjectId(projectId));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @ApiOperation(value = "查询成员用例数量", notes = "查询成员用例数量")
    @GetMapping("/findCaseUserById/{projectId}")
    @SysLog("查询成员用例数量")
    public R<HashMap<String, Object>> findCaseUserById(@PathVariable("projectId") Long projectId)
    {
        HashMap<String, Object> caseUserById = baseService.findCaseUserById(projectId);
        return R.success(caseUserById);
    }

    @ApiOperation(value = "从过程用例资产入库到用例库", notes = "从过程用例资产入库到用例库")
    @PostMapping("/saveToLibrary")
    @SysLog(value = "从过程用例资产入库到用例库")
    public R<Boolean> saveToLibrary(@RequestBody List<Long> registryIds) {
        baseService.saveToLibrary(registryIds);
        return R.success(true);
    }

    @ApiOperation(value = "从修订记录还原", notes = "从修订记录还原")
    @PutMapping("/restore/{revisionId}")
    @SysLog(value = "从修订记录还原")
    public R<Boolean> restoreFromRevision(@PathVariable Long revisionId) {
        baseService.restoreFromRevision(revisionId);
        return R.success(true);
    }

    /**
     * 重写需要：根据条件筛选 测试用例
     * @param params 分页参数
     * @return
     */
    @Override
    public IPage<TestCase> query(PageParams<TestCasePageQuery> params) {
//        IPage<TestCase> page = params.buildPage(TestCase.class);
//        TestCasePageQuery model = params.getModel();
//        LbqWrapper<TestCase> queryWrapLbuWrapper = Wraps.lbQ();
//        if (!Objects.equals(model.getTreeId(), null))
//        {
//            queryWrapLbuWrapper.eq(TestCase::getTreeId, model.getTreeId());
//        }
//        if (!Objects.equals(model.getLibraryId(), null) && Objects.equals(model.getTreeId(), null))
//        {
//            queryWrapLbuWrapper.eq(TestCase::getLibraryId, model.getLibraryId());
//        }
//        if(!Objects.equals(model.getName(), null)) {
//            queryWrapLbuWrapper.like(TestCase::getName, model.getName());
//        }
//        if(!Objects.equals(model.getCaseKey(), null)) {
//            queryWrapLbuWrapper.like(TestCase::getCaseKey, model.getCaseKey());
//        }
//        if(!Objects.equals(model.getPriority(), null)) {
//            queryWrapLbuWrapper.eq(TestCase::getPriority, model.getPriority());
//        }
//        if(!Objects.equals(model.getTabName(), null)) {
//            List<Long> caseIDs = testTabCaseService.getCasesOfTabNames(model.getTabName()).stream().map(t -> t.getId()).collect(Collectors.toList());
//            if(caseIDs.size() > 0) {
//                queryWrapLbuWrapper.in(TestCase::getId, caseIDs);
//            }
//        }
//        baseService.page(page, queryWrapLbuWrapper);
        IPage<TestCase> page = params.buildPage(TestCase.class);
        TestCasePageQuery model = params.getModel();
        List<TestTabCase> testTabCaseList = new ArrayList<>();
        if(params.getModel().getTabName()!=null&&params.getModel().getTabName().length > 0){
            for (String tabId : params.getModel().getTabName()){
                if (testTabCaseList.size() == 0) {
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ().eq(TestTabCase::getTabId, Long.valueOf(tabId)));
                }else{
                    List<Long> prodIds = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
                    testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ()
                            .eq(TestTabCase::getTabId, Long.valueOf(tabId))
                            .in(TestTabCase::getProdId, prodIds));
                }
                if (testTabCaseList.size() == 0){
                    return page;
                }
            }
        }
        LbqWrapper<TestCase> wrapper = Wraps.lbQ();

        wrapper.eq(TestCase::getProductId, model.getProductId())
                .like(TestCase::getName, model.getName())
                .eq(TestCase::getLeadingBy, model.getLeadingBy())
                .eq(TestCase::getPriority,model.getPriority())
                .like(TestCase::getCaseKey,model.getCaseKey());
        List<Long> collect = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
        if (!collect.isEmpty()){
            wrapper.in(TestCase::getId, testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList()));
        }
        if(model.getFunctionIds().size() > 0){
            wrapper.in(TestCase::getModuleFunctionId,model.getFunctionIds());
        }
        baseService.page(page, wrapper);
        page.getRecords().forEach(item->
        {
            Long leadingBy = item.getLeadingBy();
            User userById = userApi.findUserById(leadingBy);
            item.getEchoMap().put("leadingBy",userById);
        });
        echoService.action(page);
        return page;
    }

}
