package com.jettech.jettong.testm.controller.productCase;

import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.jettong.common.util.poi.ExcelDownLoadUtil;
import com.jettech.jettong.testm.dto.TestProductCasePageQuery;
import com.jettech.jettong.testm.service.TestProductCaseRegistryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

@RestController
@RequestMapping("/testProductCaseRegistry/excel")
@Api(value = "TestProductCaseRegistryExcel", tags = "用例仓库Excel导出")
@PreAuth(replace = "testm:testProductCaseRegistry:")
@RequiredArgsConstructor
public class TestProductCaseRegistryExcelController {

    private final TestProductCaseRegistryService testProductCaseRegistryService;

    @ApiOperation(value = "导出Excel")
    @PostMapping("/export")
    @SysLog(value = "导出Excel", optType = OptLogTypeEnum.EXPORT)
    public void export(@RequestBody @Validated TestProductCasePageQuery query, HttpServletResponse response) {
        Map<String, Object> result = testProductCaseRegistryService.export(query);
        try (Workbook workbook = (Workbook) result.get("workbook")) {
            String fileName = (String) result.get("fileName");
            ExcelDownLoadUtil.export(response, workbook, fileName);
        } catch (IOException e) {
            throw new RuntimeException("导出Excel失败", e);
        }
    }
}
