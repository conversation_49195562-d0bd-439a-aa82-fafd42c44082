package com.jettech.jettong.testm.controller.testReq;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.jettong.testm.entity.TestRequirementAnalysisConfig;
import com.jettech.jettong.testm.dto.TestRequirementAnalysisConfigSaveDTO;
import com.jettech.jettong.testm.dto.TestRequirementAnalysisConfigUpdateDTO;
import com.jettech.jettong.testm.dto.TestRequirementAnalysisConfigPageQuery;
import com.jettech.jettong.testm.service.TestRequirementAnalysisConfigService;


import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



/**
 * 测试分析配置表控制器
 * <AUTHOR>
 * @version 1.0
 * @description 测试分析配置表控制器
 * @projectName jettong
 * @package com.jettech.jettong.testm.controller
 * @className TestRequirementAnalysisConfigController
 * @date 2025-07-24
  * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/testRequirementAnalysisConfig")
@Api(value = "TestRequirementAnalysisConfig", tags = "测试分析配置表")
@PreAuth(replace = "testm:testRequirementAnalysisConfig:")
public class TestRequirementAnalysisConfigController extends SuperController<TestRequirementAnalysisConfigService, Long, TestRequirementAnalysisConfig, TestRequirementAnalysisConfigPageQuery, TestRequirementAnalysisConfigSaveDTO, TestRequirementAnalysisConfigUpdateDTO>
{

    private final EchoService echoService;

}
