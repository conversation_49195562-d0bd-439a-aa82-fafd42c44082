package com.jettech.jettong.testm.controller.productCase;

import cn.hutool.core.bean.BeanUtil;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.alm.api.BugApi;

import com.jettech.jettong.alm.api.ProjectApi;
import com.jettech.jettong.alm.api.StateApi;
import com.jettech.jettong.alm.issue.entity.Bug;
import com.jettech.jettong.alm.issue.entity.State;
import com.jettech.jettong.alm.project.entity.ProjectUser;
import com.jettech.jettong.alm.project.entity.ProjectUserRole;
import com.jettech.jettong.base.api.DictionaryApi;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.common.constant.DictionaryBaseType;
import com.jettech.jettong.testm.dto.TestOverviewSaveDTO;
import com.jettech.jettong.testm.dto.TestOverviewUpdateDTO;
import com.jettech.jettong.testm.dto.TestOverviewPageQuery;

import java.io.Serializable;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.base.R;
import com.jettech.jettong.testm.entity.*;
import com.jettech.jettong.testm.service.*;
import com.jettech.jettong.testm.vo.TestSummaryComponent;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.jettong.testm.service.TestTaskCaseService;

/**
 * 控制器
 * <AUTHOR>
 * @version 1.0
 * @description 控制器
 * @projectName jettong
 * @package com.jettech.jettong.testm.controller
 * @className TestOverviewController
 * @date 2022-05-23
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/testOverview")
@Api(value = "TestOverview", tags = "测试模块概览")
//@PreAuth(replace = "testm:testOverview:")
@RequiredArgsConstructor
public class TestOverviewController extends SuperController<TestOverviewService, Long, TestOverview, TestOverviewPageQuery, TestOverviewSaveDTO, TestOverviewUpdateDTO> {

    private final TestProductCaseService testProductCaseService;

    private final TestPlanService testPlanService;

    private final BugApi bugApi;

    private final TestPlanArchiveService testPlanArchiveService;

    private final TestProductCaseArchiveService testProductCaseArchiveService;

    private final ProductCaseLibraryService productCaseLibraryService;

    private final TestRequirementFunctionPointsService testRequirementFunctionPointsService;

    private final TestTaskCaseService testTaskCaseService;
    private final TestBugService testBugService;
    private final StateApi stateApi;
    private final ProjectApi projectApi;
    private final UserApi userApi;
    private final DictionaryApi dictionaryApi;

    @ApiOperation(value = "更新概览数据信息", notes = "更新概览数据信息")
    @GetMapping("/setOverView")
    @SysLog(value = "更新概览数据信息")
    @PreAuth("hasAnyPermission('{}view')")
    @Transactional
    public R setOverView()
    {
        ArrayList<TestSummaryComponent> testSummaryComponents = new ArrayList<>();
        List<TestProductCase> casesOnProductId = new ArrayList<>();
        //筛选出所有测试管理模块用例库
        List<Long> libraryIds = productCaseLibraryService
                .list(Wraps.<ProductCaseLibrary>lbQ().ne(ProductCaseLibrary::getType, "product"))
                .stream().map(ProductCaseLibrary::getId).collect(Collectors.toList());
        //筛选出所有测试管理模块的用例
        casesOnProductId.addAll(testProductCaseService
                .list(Wraps.<TestProductCase>lbQ().isNotNull(TestProductCase::getPriority)
                        .in(TestProductCase::getLibraryId,libraryIds)));
        List<TestProductCaseArchive> list1 = testProductCaseArchiveService.list();
        list1.forEach(item ->
        {
            if(productCaseLibraryService.getById(item.getLibraryId())!=null){
                if (!productCaseLibraryService.getById(item.getLibraryId()).getType().equals("product")){
                    TestProductCase testProductCase = BeanUtil.toBean(item, TestProductCase.class);
                    casesOnProductId.add(testProductCase);
                }
            }
        });
        Map<Integer, Long> map = casesOnProductId.stream().collect(Collectors.groupingBy(TestProductCase::getPriority, Collectors.counting()));
        if((map.isEmpty()) || (!map.containsKey(3))){
            long count = testProductCaseService.count(Wraps.<TestProductCase>lbQ().in(TestProductCase::getLibraryId,libraryIds));
            if (count-casesOnProductId.size() != 0){
                map.put(3,count-casesOnProductId.size());
            }
        }else{
            long count = testProductCaseService.count(Wraps.<TestProductCase>lbQ().in(TestProductCase::getLibraryId,libraryIds));
            map.put(3,map.get(3)+count-casesOnProductId.size());
        }
        TestOverview testOverview = new TestOverview();
        testOverview.setCaseHighest(map.get(5)).setCaseHigher(map.get(4)).setCaseOrdinary(map.get(3))
                .setCaseLower(map.get(2)).setCaseMinimum(map.get(1));


        List<TestPlan> plans = new ArrayList<>();
        plans.addAll(testPlanService.list(Wraps.<TestPlan>lbQ().isNotNull(TestPlan::getLibraryId).isNotNull(TestPlan::getTreeId)
                .eq(TestPlan::getState,false)));
        List<TestPlanArchive> list = testPlanArchiveService.list(Wraps.<TestPlanArchive>lbQ().isNotNull(TestPlanArchive::getTreeId));
        list.forEach(item ->
        {
            TestPlan testPlan = BeanUtil.toBean(item, TestPlan.class);
            plans.add(testPlan);
        });
        for (TestPlan t : plans) {
            if (null == t.getStateId()) {
                t.setStateId(0L);
            }
        }
        Map<Long, Long> map1 = plans.stream().collect(Collectors.groupingBy(TestPlan::getStateId, Collectors.counting()));
        Map<Integer, Long> collect = map1.entrySet().stream()
                .collect(Collectors.toMap(e -> e.getKey().intValue(), Map.Entry::getValue));
        testOverview.setPlanTodo(collect.get(0)).setPlanProgress(collect.get(1)).setPlanDone(collect.get(2));


        Map<String, Long> overview = bugApi.getOverview();
        testOverview.setBugHighest(overview.get("HIGHEST")).setBugHigher(overview.get("HIGH")).setBugOrdinary(overview.get("MEDIUM"))
                .setBugLower(overview.get("LOW")).setBugMinimun(overview.get("LOWEST"));
        if(baseService.list().isEmpty()){
            baseService.save(testOverview);
        }else {
            TestOverview one = baseService.getOne(Wraps.<TestOverview>lbQ());
            testOverview.setId(one.getId());
            baseService.updateById(testOverview);
        }

        return R.success(testSummaryComponents);
    }

    @ApiOperation(value = "查询测试用例概览信息-用例、计划、缺陷统计", notes = "查询测试用例概览信息-用例、计划、缺陷统计")
    @GetMapping("/getOverview")
    @SysLog(value = "查询测试用例概览信息-用例、计划、缺陷统计")
    @PreAuth("hasAnyPermission('{}view')")
    @Transactional
    public R<List<TestSummaryComponent>> getOverview()
    {
        ArrayList<TestSummaryComponent> testSummaryComponents = new ArrayList<>();
        TestOverview one = baseService.getOne(Wraps.<TestOverview>lbQ());

        //计划统计
        TestSummaryComponent testSummaryComponent = new TestSummaryComponent();
        ArrayList<TestSummaryComponent.Pie> pies = new ArrayList<>();
        pies.add(new TestSummaryComponent.Pie().setName("未开始").setValue(one.getPlanTodo()==null ? 0:one.getPlanTodo().intValue()));
        pies.add(new TestSummaryComponent.Pie().setName("进行中").setValue(one.getPlanProgress()==null ? 0:one.getPlanProgress().intValue()));
        pies.add(new TestSummaryComponent.Pie().setName("已完成").setValue(one.getPlanDone()==null ? 0:one.getPlanDone().intValue()));
        testSummaryComponent.setData(pies);
        int todo = one.getPlanTodo() == null ? 0 : one.getPlanTodo().intValue();
        int progress = one.getPlanProgress() == null ? 0 : one.getPlanProgress().intValue();
        int done = one.getPlanDone() == null ? 0 : one.getPlanDone().intValue();
        testSummaryComponent.setCount(todo+progress+done);
        testSummaryComponents.add(testSummaryComponent);

        //缺陷统计
        Map<String, Long> bugData = bugApi.getBugData();

        TestSummaryComponent bugComponent = new TestSummaryComponent();
        ArrayList<TestSummaryComponent.Pie> bugPies = new ArrayList<>();
//            bugPies.add(new TestSummaryComponent.Pie().setName("HIGHEST").setValue(one.getBugHighest()==null ? 0:one.getBugHighest().intValue()));
//            bugPies.add(new TestSummaryComponent.Pie().setName("HIGH").setValue(one.getBugHigher()==null ? 0:one.getBugHigher().intValue()));
//            bugPies.add(new TestSummaryComponent.Pie().setName("MEDIUM").setValue(one.getBugOrdinary()==null ? 0:one.getBugOrdinary().intValue()));
//            bugPies.add(new TestSummaryComponent.Pie().setName("LOW").setValue(one.getBugLower()==null ? 0:one.getBugLower().intValue()));
//            bugPies.add(new TestSummaryComponent.Pie().setName("LOWEST").setValue(one.getBugMinimun()==null ? 0:one.getBugMinimun().intValue()));
        bugPies.add(bugData.get("HIGHEST") == null ? new TestSummaryComponent.Pie().setName("HIGHEST").setValue(0) : new TestSummaryComponent.Pie().setName("HIGHEST").setValue(bugData.get("HIGHEST").intValue()));
        bugPies.add(bugData.get("HIGH") == null ? new TestSummaryComponent.Pie().setName("HIGH").setValue(0) : new TestSummaryComponent.Pie().setName("HIGH").setValue(bugData.get("HIGH").intValue()));
        bugPies.add(bugData.get("MEDIUM") == null ? new TestSummaryComponent.Pie().setName("MEDIUM").setValue(0) : new TestSummaryComponent.Pie().setName("MEDIUM").setValue(bugData.get("MEDIUM").intValue()));
        bugPies.add(bugData.get("LOW") == null ? new TestSummaryComponent.Pie().setName("LOW").setValue(0) : new TestSummaryComponent.Pie().setName("LOW").setValue(bugData.get("LOW").intValue()));
        bugPies.add(bugData.get("LOWEST") == null ? new TestSummaryComponent.Pie().setName("LOWEST").setValue(0) : new TestSummaryComponent.Pie().setName("LOWEST").setValue(bugData.get("LOWEST").intValue()));
        bugComponent.setData(bugPies);
//        int highest = one.getBugHighest() == null ? 0 : one.getBugHighest().intValue();
//        int higher = one.getBugHigher() == null ? 0 : one.getBugHigher().intValue();
//        int ordinary = one.getBugOrdinary() == null ? 0 : one.getBugOrdinary().intValue();
//        int lower = one.getBugLower() == null ? 0 : one.getBugLower().intValue();
//        int minimun = one.getBugMinimun() == null ? 0 : one.getBugMinimun().intValue();
        int highest = bugData.get("HIGHEST") == null ? 0 : bugData.get("HIGHEST").intValue();
        int higher = bugData.get("HIGH") == null ? 0 : bugData.get("HIGH").intValue();
        int ordinary = bugData.get("MEDIUM") == null ? 0 : bugData.get("MEDIUM").intValue();
        int lower = bugData.get("LOW") == null ? 0 : bugData.get("LOW").intValue();
        int minimun = bugData.get("LOWEST") == null ? 0 : bugData.get("LOWEST").intValue();
        bugComponent.setCount(higher+highest+ordinary+lower+minimun);
        testSummaryComponents.add(bugComponent);

        //用例统计
        TestSummaryComponent caseComponent = new TestSummaryComponent();
        ArrayList<TestSummaryComponent.Pie> casePies = new ArrayList<>();
        casePies.add(new TestSummaryComponent.Pie().setName("Lowest").setValue(one.getCaseHighest()==null?0:one.getCaseHighest().intValue()));
        casePies.add(new TestSummaryComponent.Pie().setName("Low").setValue(one.getCaseHigher()==null ? 0 : one.getCaseHigher().intValue()));
        casePies.add(new TestSummaryComponent.Pie().setName("Medium").setValue(one.getCaseOrdinary()==null ? 0 : one.getCaseOrdinary().intValue()));
        casePies.add(new TestSummaryComponent.Pie().setName("High").setValue(one.getCaseLower()==null ? 0:one.getCaseLower().intValue()));
        casePies.add(new TestSummaryComponent.Pie().setName("Highest").setValue(one.getCaseMinimum()==null ? 0:one.getCaseMinimum().intValue()));
        caseComponent.setData(casePies);
        int highest1= one.getCaseHighest() == null ? 0 : one.getCaseHighest().intValue();
        int higher1 = one.getCaseHigher() == null ? 0 : one.getCaseHigher().intValue();
        int ordinary1 = one.getCaseOrdinary() == null ? 0 : one.getCaseOrdinary().intValue();
        int lower1 = one.getCaseLower() == null ? 0 : one.getCaseLower().intValue();
        int minimun1 = one.getCaseMinimum() == null ? 0 : one.getCaseMinimum().intValue();
        caseComponent.setCount(higher1+highest1+ordinary1+lower1+minimun1);
        testSummaryComponents.add(caseComponent);

        return R.success(testSummaryComponents);
    }

    @ApiOperation(value = "获取项目概览统计", notes = "获取项目概览统计-测试点、用例设计、执行用例、缺陷总数")
    @GetMapping("/getProjectOverview")
    @SysLog(value = "获取项目概览统计")
    @PreAuth("hasAnyPermission('{}view')")
    public R<Map<String,Object>> getProjectOverview(@RequestParam(value = "projectId") Long projectId
            ,@RequestParam(value = "reqId",required = false)Long reqId) {
        Map<String,Object> result = new HashMap<>();
        // 测试点总数 - test_requirement_function_points
        List<TestRequirementFunctionPoints> points = testRequirementFunctionPointsService.
                list(Wraps.<TestRequirementFunctionPoints>lbQ().eq(TestRequirementFunctionPoints::getProjectId,projectId)
                                .eq(TestRequirementFunctionPoints::getIssueTestReqId,reqId)
                        );
        // 用例设计总数 - test_product_case
        List<TestProductCase> cases = testProductCaseService.
                list(Wraps.<TestProductCase>lbQ().eq(TestProductCase::getProjectId,projectId)
                        .eq(TestProductCase::getTestreqId,reqId)
                );
        // 执行用例总数 - test_task_case
        List<TestTaskCase> taskCases = testTaskCaseService.
                list(Wraps.<TestTaskCase>lbQ().eq(TestTaskCase::getProjectId,projectId)
                        .eq(TestTaskCase::getTestreqId,reqId)
                );
        // 缺陷总数 - issue_bug
        List<Bug> bugs = testBugService.list(Wraps.<Bug>lbQ().eq(Bug::getProjectId,projectId)
                .eq(Bug::getTestreqId,reqId)
        );
        //测试点总数
        result.put("CSDS",points.size());
        //用例设计总数
        result.put("YLSJS",cases.size());
        //执行用例总数
        result.put("ZXYLS",taskCases.size());
        //缺陷总数
        result.put("QXS",bugs.size());
        //用例覆盖度(已关联用例的测试点/总测试）
        //过滤出已关联用例的测试点
        List<TestRequirementFunctionPoints> filterPoints = points.stream().filter(item->item.getCaseCount()!=null && item.getCaseCount()>0).collect(Collectors.toList());
       //计算用例覆盖度
        double coverage = calculatePercentage(filterPoints.size(),points.size());
        result.put("YLFGD",coverage);
        // 执行通过率（成功用例数/（已执行的用例总数-N/A用例数））
        //过滤出已执行的用例总数
        List<TestTaskCase> filterTaskCases = taskCases.stream().filter(item->item.getStatus()!=null && !item.getStatus().equals("skip")).collect(Collectors.toList());
        //过滤出成功用例数
        List<TestTaskCase> filterSuccessTaskCases = taskCases.stream().filter(item->item.getStatus()!=null && item.getStatus().equals("system")).collect(Collectors.toList());
        //通过的/（总数-跳过）
        int YLZS = filterTaskCases.size();
        Double successRate = calculatePercentage(filterSuccessTaskCases.size(),YLZS);
        result.put("ZXTGL",successRate);
        // 缺陷率（有效缺陷/（已执行的用例总数-N/A用例数））
        //有效缺陷，状态不等于拒绝和丢弃的
        List<String> names = new ArrayList<>();
        names.add("拒绝");
        names.add("丢弃");
        names.add("无效");
        List<State> byStateName = stateApi.findByStateName(names);
        //有效bug总数
        int YXBugZS = bugs.size();
        if(!byStateName.isEmpty()){
            //获取byStateName 的所有code
            List<String> codes = byStateName.stream().map(State::getName).collect(Collectors.toList());
            YXBugZS = bugs.stream().filter(item->item.getStateCode()!=null && !codes.contains(item.getStateCode())).collect(Collectors.toList()).size();
        }

        Double bugRate = calculatePercentage(YXBugZS,YLZS);
        result.put("QXL",bugRate);
        // 自动化用例占比（项目下关联了UI自动化脚本或API自动化脚本的用例数/用例总数）
        // 假设testMode是从业务中获取的字段值
        int testMode = 0;
        //自动化测试  //
//                && (Integer.parseInt(item.getTestMode())&2)!=0||(Integer.parseInt(item.getTestMode())&4)!=0
        List<TestProductCase> filterCases = cases.stream().filter(item->item.getTestMode()!=null).collect(Collectors.toList());
        Double autoCaseRate = calculatePercentage(filterCases.size(),cases.size());
        result.put("ZDYLZB",autoCaseRate);
        return R.success(result);
    }


    /**
     * 计算百分比，保留两位小数，处理除零情况
     */
    private double calculatePercentage(int numerator, int denominator) {
        long f = Long.valueOf(numerator);
        long m = Long.valueOf(denominator);
        if (denominator == 0) {
            return 0.00;
        }
        // 计算百分比并保留两位小数
        return Math.round((f * 100.0 / m) * 100) / 100.0;
    }
    // 提取测试模式判断逻辑为单独方法
    private boolean isAutoTestMode(String testModeStr) {
        try {
            int testMode = Integer.parseInt(testModeStr);
            // 检查是否为UI自动化(2)或API自动化(4)
            return (testMode & 2) != 0 || (testMode & 4) != 0;
        } catch (NumberFormatException e) {
            // 非数字格式的测试模式视为不符合条件
            return false;
        }
    }

    @ApiOperation(value = "获取测试人员情况统计", notes = "获取测试人员情况统计-人员名称、角色、级别、测试点数量、用例数、执行用例数量、提交缺陷数")
    @GetMapping("/getTestPersonnelStats")
    @SysLog(value = "获取测试人员情况统计")
    @PreAuth("hasAnyPermission('{}view')")
    public R<List<Map<String,Object>>> getTestPersonnelStats(@RequestParam(value = "projectId") Long projectId) {
        List<Map<String,Object>> result = new ArrayList<>();

        // 获取项目下的所有用户
        ProjectUserRole projectUser = ProjectUserRole.builder().projectId(projectId).build();
        List<ProjectUserRole> projectUsers = projectApi.selectProjectUserRoleByProjectId(projectUser);
        List<Dictionary> dictionaries = dictionaryApi.query(Dictionary.builder().type(DictionaryBaseType.USER_TYPE).build());
        Map<String, Dictionary> dictionarieMap = dictionaries.stream()
                .collect(Collectors.toMap(Dictionary::getCode, Function.identity(), (key1, key2) -> key2));
        for (ProjectUserRole pu : projectUsers) {
            Map<String,Object> userStats = new HashMap<>();
            Long userId = pu.getUserId();
            // 获取用户基本信息
            Map<Long, User> userMap = userApi.selectByIds(Collections.singleton(userId));
            if (userMap.isEmpty())
            {
                continue;
            }

            User user = BeanPlusUtil.toBean(userMap.get(userId), User.class);
            userStats.put("userName", user);
            userStats.put("userRole", dictionarieMap.get(user.getType()).getName()); // todo 后续修改
            userStats.put("userLevel", "高"); // 用户级别

            // 测试点数量（创建人是本人的）
            List<TestRequirementFunctionPoints> userPoints = testRequirementFunctionPointsService
                    .list(Wraps.<TestRequirementFunctionPoints>lbQ()
                            .eq(TestRequirementFunctionPoints::getProjectId, projectId)
                            .eq(TestRequirementFunctionPoints::getCreatedBy, userId));
            userStats.put("testPointCount", userPoints.size());

            // 用例数（创建人是本人的）
            List<TestProductCase> userCases = testProductCaseService
                    .list(Wraps.<TestProductCase>lbQ()
                            .eq(TestProductCase::getProjectId, projectId)
                            .eq(TestProductCase::getCreatedBy, userId));
            userStats.put("caseCount", userCases.size());

            // 执行用例数量（最后一次执行人为当前用户的执行用例数）
            List<TestTaskCase> userExecutedCases = testTaskCaseService
                    .list(Wraps.<TestTaskCase>lbQ()
                            .eq(TestTaskCase::getProjectId, projectId)
                            .eq(TestTaskCase::getExecBy, userId));
            userStats.put("executedCaseCount", userExecutedCases.size());

            // 提交缺陷数（创建人为当前用户的缺陷）
            List<Bug> userBugs = testBugService
                    .list(Wraps.<Bug>lbQ()
                            .eq(Bug::getProjectId, projectId)
                            .eq(Bug::getCreatedBy, userId));
            userStats.put("bugCount", userBugs.size());

            result.add(userStats);
        }

        return R.success(result);
    }

}
