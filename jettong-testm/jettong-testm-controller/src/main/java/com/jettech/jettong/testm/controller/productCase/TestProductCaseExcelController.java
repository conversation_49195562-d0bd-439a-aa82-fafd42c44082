package com.jettech.jettong.testm.controller.productCase;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperSimpleController;
import com.jettech.basic.cache.model.CacheKey;
import com.jettech.basic.cache.repository.CacheOps;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.basic.utils.StringUtil;
import com.jettech.basic.uuid.UidGeneratorUtil;
import com.jettech.jettong.alm.api.ProjectApi;
import com.jettech.jettong.alm.api.RequirementApi;
import com.jettech.jettong.alm.issue.entity.Requirement;
import com.jettech.jettong.alm.project.entity.ProjectUser;
import com.jettech.jettong.base.api.DictionaryApi;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.base.enumeration.dictionary.DictionaryRelationBizType;
import com.jettech.jettong.common.cache.testm.TestMCacheKeyBuilder;
import com.jettech.jettong.common.util.poi.ExcelDownLoadUtil;
import com.jettech.jettong.common.util.poi.ExcelExportPlusUtil;
import com.jettech.jettong.product.api.ProductInfoApi;
import com.jettech.jettong.product.api.ProductModuleFunctionApi;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.controller.poi.TestCaseExcelDictHandlerImpl;
import com.jettech.jettong.testm.controller.poi.TestProductCaseExcelDictHandlerImpl;
import com.jettech.jettong.testm.controller.poi.TestProductCaseExcelVerifyHandlerImpl;
import com.jettech.jettong.testm.dto.TestProductCasePageQuery;
import com.jettech.jettong.testm.poi.dict.TestProductCaseDictHandler;
import com.jettech.jettong.testm.dto.TestProductCaseExportQuery;
import com.jettech.jettong.testm.dto.TestProductCaseImportTaskSaveDTO;
import com.jettech.jettong.testm.entity.*;
import com.jettech.jettong.testm.poi.TestRequirementFunctionPointDicHandlerImpl;
import com.jettech.basic.fileimport.model.ImportResult;
import com.jettech.jettong.testm.poi.TestRequirementFunctionPointVerifyHandler;
import com.jettech.jettong.testm.service.*;
import com.jettech.jettong.testm.vo.TestProductCaseExportCataExcelVo;
import com.jettech.jettong.testm.vo.TestProductCaseExportExcelVO;
import com.jettech.jettong.testm.vo.TestProductCaseImportExcelVO;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.usermodel.*;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Base64Utils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.Size;
import javax.validation.groups.Default;
import java.io.*;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.jettech.jettong.common.constant.SwaggerConstants.DATA_TYPE_LONG;
import static com.jettech.jettong.common.constant.SwaggerConstants.PARAM_TYPE_QUERY;
import static org.apache.poi.ss.usermodel.Font.COLOR_RED;


/**
 * 测试案例导入导出信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试案例导入导出信息控制器
 * @projectName jettong
 * @package com.jettech.jettong.alm.controller.issue
 * @className TaskExcelController
 * @date 2021-11-11
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/testProductCase/excel")
@Api(value = "Task", tags = "案例库导入导出")
@RequiredArgsConstructor
public class TestProductCaseExcelController extends
        SuperSimpleController<TestProductCaseService, TestProductCase> {
    private final UserApi userApi;

    private final TestProductCaseExcelDictHandlerImpl testProductCaseExcelDictHandlerImpl;

    private final TestProductCaseExcelVerifyHandlerImpl testProductCaseExcelVerifyHandlerImpl;

    private final TestProductCaseDictHandler testProductCaseDictHandler;

    private final CacheOps cacheOps;

    private final RequirementApi requirementApi;

    private final ProjectApi projectApi;

    private final ProductInfoApi productInfoApi;

    public final ProductModuleFunctionApi productModuleFunctionApi;

    private final ProductCaseLibraryService productCaseLibraryService;

    private final ProductCaseTreeService productCaseTreeService;

    private static final String SHEETNAME = "测试用例";

    private final TestProductCaseHistoryService testProductCaseHistoryService;

    private final TestProductCaseService testProductCaseService;

    private final TestTabCaseService testTabCaseService;

    private final TestProductCaseTestModeService testProductCaseTestModeService;

    private final DictionaryApi dictionaryApi;



    @ApiOperation(value = "导出Excel展示字段")
    @GetMapping(value = "/export/fields")
    @SysLog(value = "导出Excel展示字段", request = false)
    public R<Map<String, String>> exportExcelFields() {
        LinkedHashMap<String, String> resultMap = new LinkedHashMap<>();
        Field[] fields = TestProductCaseExportExcelVO.class.getDeclaredFields();
        for (Field f : fields) {
            Excel excel = f.getAnnotation(Excel.class);
            resultMap.put(f.getName(), excel.name());
        }
        return R.success(resultMap);
    }

    /**
     * 生成excel的sheet
     *
     * @param model        查询参数
     * @param map 数据集合
     * @return List<Map < String, Object>> sheet
     * <AUTHOR>
     * @date 2021/12/1 19:55
     * @update zxy 2021/12/1 19:55
     * @since 1.0
     */
    private List<Map<String, Object>> getSheetsMap(Map<Long, ProductCaseTree>  model,
                                                   Map<Long,List<TestProductCase>> map) {
        List<Map<String, Object>> sheets = new ArrayList<>();
        // 动态获取字段开关信息
        List<ExcelExportEntity> entityList = new ArrayList<>();
        Field[] fields = TestProductCaseExportExcelVO.class.getDeclaredFields();

        for (Field f : fields) {
            Excel excel = f.getAnnotation(Excel.class);
            ExcelExportEntity entity = new ExcelExportEntity(excel.name(), f.getName(), 30);
            entity.setFormat(excel.format());
            entity.setReplace(excel.replace());
            entity.setDict(excel.dict());
            entityList.add(entity);
        }
        Map<String,Integer> sheetNameMap = new HashMap<>();
        //对map进行遍历
        for (Map.Entry<Long, ProductCaseTree> entry : model.entrySet()) {
            List<TestProductCaseExportExcelVO> list = new ArrayList<>();
            Map<String, Object> sheetMap = Maps.newHashMapWithExpectedSize(7);
            ExportParams exportParams = new ExportParams();
            if(sheetNameMap.get(entry.getValue().getName())==null){
                exportParams.setSheetName(model.get(entry.getKey()).getName());
                sheetNameMap.put(entry.getValue().getName(),1);
            }else{
                exportParams.setSheetName(model.get(entry.getKey()).getName()+"("+sheetNameMap.get(entry.getValue().getName())+")");
                sheetNameMap.put(entry.getValue().getName(),sheetNameMap.get(entry.getValue().getName())+1);
            }
            // 使用新的字典处理器
            exportParams.setDictHandler(testProductCaseDictHandler);
            exportParams.setType(ExcelType.XSSF);
            exportParams.setTitle("1、导入模板不同项目不能通用\n2、带*的列必须填写\n3、测试步骤类型为文本类型时，直接填入内容；当测试步骤类型为条目时，测试步骤输入如下格式：1,xxxx,xxxx;2,xxxx,xxxx;");
            sheetMap.put("exportParams", exportParams);
            //获取map的值
            List<TestProductCase> testProductCaseList = map.get(entry.getKey());
            if(testProductCaseList != null){
                //遍历value
                for (int i = 0; i < testProductCaseList.size(); i++) {
                    TestProductCase testCase = testProductCaseList.get(i);
                    String stepType = testCase.getStepType();
                    if ("text".equals(stepType)) {
                        TestProductCaseExportExcelVO testCaseExportExcelVO = new TestProductCaseExportExcelVO();
//                testCaseExportExcelVO.setCaseKey(testCase.getCaseKey());
//                testCaseExportExcelVO.setPriority(testCase.getPriority());
//                testCaseExportExcelVO.setVersion(testCase.getVersion());
                        testCaseExportExcelVO.setName(testCase.getName());
                        testCaseExportExcelVO.setRunMond("手动");
                        testCaseExportExcelVO.setSummary(testCase.getIntent());
                        testCaseExportExcelVO.setPrerequisite(testCase.getPrerequisite());
                        testCaseExportExcelVO.setExpectedResult(testCase.getExpectedResult());
                        testCaseExportExcelVO.setTestStep(testCase.getTestStep());
                        list.add(testCaseExportExcelVO);
                    } else {
                        String testStep = testCase.getTestStep();
                        boolean jsonType = getJsonType(testStep);
                        JSONArray jsonArray = JSONArray.parseArray(testStep);
                        //步骤不是json数组时候逻辑
                        if (!jsonType || (jsonArray.size() == 0)) {
                            TestProductCaseExportExcelVO testCaseExportExcelVO = new TestProductCaseExportExcelVO();
//                    testCaseExportExcelVO.setCaseKey(testCase.getCaseKey());
//                    testCaseExportExcelVO.setPriority(testCase.getPriority());
//                    testCaseExportExcelVO.setVersion(testCase.getVersion());
                            testCaseExportExcelVO.setRunMond("手动");
                            testCaseExportExcelVO.setSummary(testCase.getIntent());
                            testCaseExportExcelVO.setPrerequisite(testCase.getPrerequisite());
                            testCaseExportExcelVO.setName(entry.getValue().getName()+"——"+testCase.getName());
                            testCaseExportExcelVO.setTestStep(testCase.getTestStep());
                            testCaseExportExcelVO.setExpectedResult(testCase.getExpectedResult());
                            list.add(testCaseExportExcelVO);
                            continue;
                        }
                        for (int j = 0; j < jsonArray.size(); j++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(j);
                            String caseStepDes = jsonObject.getString("caseStepDes");
                            String expectResult = jsonObject.getString("expectResult");
                            TestProductCaseExportExcelVO testCaseExportExcelVO = new TestProductCaseExportExcelVO();
                            if (j == 0) {
//                        testCaseExportExcelVO.setCaseKey(testCase.getCaseKey());
//                        testCaseExportExcelVO.setPriority(testCase.getPriority());
//                        testCaseExportExcelVO.setVersion(testCase.getVersion());
                                testCaseExportExcelVO.setRunMond("手动");
                                testCaseExportExcelVO.setSummary(testCase.getIntent());
                                testCaseExportExcelVO.setPrerequisite(testCase.getPrerequisite());
                                testCaseExportExcelVO.setName(entry.getValue().getName()+"——"+testCase.getName());
                                testCaseExportExcelVO.setTestStep(caseStepDes);
                                testCaseExportExcelVO.setExpectedResult(expectResult);
                            } else {
                                testCaseExportExcelVO.setTestStep(caseStepDes);
                                testCaseExportExcelVO.setExpectedResult(expectResult);
                            }
                            list.add(testCaseExportExcelVO);
                        }
                    }
                }
            }
            sheetMap.put("entityList", entityList);
            sheetMap.put("data", list);
            sheets.add(sheetMap);
        }
        return sheets;
    }
    /**
     * 生成excel的sheet
     *
     * @param model        查询参数
     * @param map 数据集合
     * @return List<Map < String, Object>> sheet
     * <AUTHOR>
     * @date 2021/12/1 19:55
     * @update zxy 2021/12/1 19:55
     * @since 1.0
     */
    private List<Map<String, Object>> getOneSheetsMap(Map<Long, ProductCaseTree>  model,
                                                      Map<Long,List<TestProductCase>> map,List<Long> treeIds) {
        List<Map<String, Object>> sheets = new ArrayList<>();
        // 动态获取字段开关信息
        List<ExcelExportEntity> entityList = new ArrayList<>();
        Field[] fields = TestProductCaseExportCataExcelVo.class.getDeclaredFields();

        for (Field f : fields) {
            Excel excel = f.getAnnotation(Excel.class);
            ExcelExportEntity entity = new ExcelExportEntity(excel.name(), f.getName(), 30);
            entity.setFormat(excel.format());
            entity.setReplace(excel.replace());
            entity.setDict(excel.dict());
            entityList.add(entity);
        }
        Map<String,Integer> sheetNameMap = new HashMap<>();

        List<TestProductCaseExportCataExcelVo> list = new ArrayList<>();
        Map<String, Object> sheetMap = Maps.newHashMapWithExpectedSize(7);
        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName("测试用例");
        exportParams.setType(ExcelType.XSSF);
        sheetNameMap.put("测试用例",1);

        // 使用新的字典处理器
        exportParams.setDictHandler(testProductCaseDictHandler);
        exportParams.setTitle("1、导入模板不同项目不能通用\n2、带*的列必须填写\n3、测试步骤类型为文本类型时，直接填入内容；当测试步骤类型为条目时，测试步骤输入如下格式：1,xxxx,xxxx;2,xxxx,xxxx;");
        sheetMap.put("exportParams", exportParams);
        //treeIds
        for (Long treeId : treeIds) {
            //获取map的值
            List<TestProductCase> testProductCaseList = map.get(treeId);
            if (testProductCaseList != null) {
                //遍历value
                for (int i = 0; i < testProductCaseList.size(); i++) {
                    TestProductCase testCase = testProductCaseList.get(i);
                    String stepType = testCase.getStepType();
                    if ("text".equals(stepType)) {
                        TestProductCaseExportCataExcelVo testCaseExportExcelVO = new TestProductCaseExportCataExcelVo();
                        testCaseExportExcelVO.setCata(getTreeName(testCase.getTreeId(),model));
                        testCaseExportExcelVO.setPriority(getPriority(testCase.getPriority()));
                        testCaseExportExcelVO.setName(testCase.getName());
                        testCaseExportExcelVO.setRunMond("手动");
                        testCaseExportExcelVO.setSummary(testCase.getIntent());
                        testCaseExportExcelVO.setPrerequisite(testCase.getPrerequisite());
                        testCaseExportExcelVO.setExpectedResult(testCase.getExpectedResult());
                        testCaseExportExcelVO.setTestStep(testCase.getTestStep());
                        list.add(testCaseExportExcelVO);
                    } else {
                        String testStep = testCase.getTestStep();
                        boolean jsonType = getJsonType(testStep);
                        JSONArray jsonArray = JSONArray.parseArray(testStep);
                        //步骤不是json数组时候逻辑
                        if (!jsonType || (jsonArray.size() == 0)) {
                            TestProductCaseExportCataExcelVo testCaseExportExcelVO = new TestProductCaseExportCataExcelVo();
//                    testCaseExportExcelVO.setCaseKey(testCase.getCaseKey());
//                    testCaseExportExcelVO.setPriority(testCase.getPriority());
//                    testCaseExportExcelVO.setVersion(testCase.getVersion());
                            testCaseExportExcelVO.setCata(getTreeName(testCase.getTreeId(),model));
                            testCaseExportExcelVO.setPriority(getPriority(testCase.getPriority()));
                            testCaseExportExcelVO.setRunMond("手动");
                            testCaseExportExcelVO.setSummary(testCase.getIntent());
                            testCaseExportExcelVO.setPrerequisite(testCase.getPrerequisite());
                            testCaseExportExcelVO.setName(testCase.getName());
                            testCaseExportExcelVO.setTestStep(testCase.getTestStep());
                            testCaseExportExcelVO.setExpectedResult(testCase.getExpectedResult());
                            list.add(testCaseExportExcelVO);
                            continue;
                        }
                        for (int j = 0; j < jsonArray.size(); j++) {
                            JSONObject jsonObject = jsonArray.getJSONObject(j);
                            String caseStepDes = jsonObject.getString("caseStepDes");
                            String expectResult = jsonObject.getString("expectResult");
                            TestProductCaseExportCataExcelVo testCaseExportExcelVO = new TestProductCaseExportCataExcelVo();
                            if (j == 0) {
//                        testCaseExportExcelVO.setCaseKey(testCase.getCaseKey());
//                        testCaseExportExcelVO.setPriority(testCase.getPriority());
//                        testCaseExportExcelVO.setVersion(testCase.getVersion());
                                testCaseExportExcelVO.setCata(getTreeName(testCase.getTreeId(),model));
                                testCaseExportExcelVO.setPriority(getPriority(testCase.getPriority()));
                                testCaseExportExcelVO.setRunMond("手动");
                                testCaseExportExcelVO.setSummary(testCase.getIntent());
                                testCaseExportExcelVO.setPrerequisite(testCase.getPrerequisite());
                                testCaseExportExcelVO.setName( testCase.getName());
                                testCaseExportExcelVO.setTestStep(caseStepDes);
                                testCaseExportExcelVO.setExpectedResult(expectResult);
                            } else {
                                testCaseExportExcelVO.setTestStep(caseStepDes);
                                testCaseExportExcelVO.setExpectedResult(expectResult);
                            }
                            list.add(testCaseExportExcelVO);
                        }
                    }
                }
            }
        }
        sheetMap.put("entityList", entityList);
        sheetMap.put("data", list);
        sheets.add(sheetMap);
        return sheets;
    }

    /**
     * 根据json特征判断是都是json字符串，判断是都是json或者json数组
     *
     * @param str
     * @return
     */
    public static boolean getJsonType(String str) {
        boolean result = false;
        if (StringUtils.isNotBlank(str)) {
            str = str.trim();
            if (str.startsWith("{") && str.endsWith("}")) {
                result = true;
            } else if (str.startsWith("[") && str.endsWith("]")) {
                result = true;
            }
        }
        return result;
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_QUERY)
    })
    @ApiOperation(value = "下载测试用例导入模板")
    @GetMapping(value = "/downloadImportTemplate/{projectId}", produces = "application/octet-stream")
    @SysLog(value = "下载测试用例导入模板", optType = OptLogTypeEnum.OTHER)
    public void downloadImportTemplate(@PathVariable("projectId") Long projectId, HttpServletResponse   response) {

        List<Map<String, Object>> sheets = new ArrayList<>();
        Map<Integer, Map<Integer, String[]>> dropDownMap = Maps.newHashMapWithExpectedSize(1);

        String[] requirementCodeArray = null;

        String[] projectUserNameArray = null;

        String[] stepTypeArray = null;

        String[] priorityArray = null;

        String[] treeNameArray = null;

        int sheetNum = 0;

        Map<String, Object> sheetMap = Maps.newHashMapWithExpectedSize(7);
        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName(SHEETNAME);
        // 使用新的字典处理器
        exportParams.setDictHandler(testProductCaseDictHandler);
        exportParams.setType(ExcelType.XSSF);
        exportParams.setTitle("1、导入模板不同项目不能通用\n2、带*的列必须填写\n3、测试步骤类型为文本类型时，直接填入内容；当测试步骤类型为条目时，测试步骤输入如下格式：1,xxxx,xxxx;2,xxxx,xxxx;");
        sheetMap.put("exportParams", exportParams);
        // 动态获取字段开关信息
        List<ExcelExportEntity> entityList = new ArrayList<>();

        Field[] fields = TestProductCaseImportExcelVO.class.getDeclaredFields();

        int columnNum = 0;
        Map<Integer, String[]> columnMap = Maps.newHashMapWithExpectedSize(7);

        for (Field filed : fields) {
            Excel excel = filed.getAnnotation(Excel.class);
            if (excel == null) {
                break;
            }
            ExcelExportEntity entity = new ExcelExportEntity(excel.name(), filed.getName(), 30);
            entity.setFormat(excel.format());
            entityList.add(entity);
            switch (filed.getName()) {
                case "stepType":
                    if (stepTypeArray == null) {
                        stepTypeArray = TestCaseExcelDictHandlerImpl.stepTypeNameArray;
                    }
                    if (stepTypeArray.length != 0) {
                        columnMap.put(columnNum, stepTypeArray);
                        dropDownMap.put(sheetNum, columnMap);
                    }
                    break;
                case "priority":
                    if (priorityArray == null) {
                        priorityArray = TestCaseExcelDictHandlerImpl.priorityNameArray;
                    }
                    if (priorityArray.length != 0) {
                        columnMap.put(columnNum, priorityArray);
                        dropDownMap.put(sheetNum, columnMap);
                    }
                    break;
                case "treeId":
                    if (treeNameArray == null) {
                        treeNameArray = getTreeNameArray(projectId);
                    }
                    if (treeNameArray.length != 0) {
                        columnMap.put(columnNum, treeNameArray);
                        dropDownMap.put(sheetNum, columnMap);
                    }
                    break;
                case "requirementId":
                    if (requirementCodeArray == null) {
                        requirementCodeArray = getRequirementCodeAndNameArray(projectId);
                    }
                    if (requirementCodeArray.length != 0) {
                        columnMap.put(columnNum, requirementCodeArray);
                        dropDownMap.put(sheetNum, columnMap);
                    }
                    break;
                case "leadingBy":
                    if (projectUserNameArray == null) {
                        projectUserNameArray = getProjectUserNameAndAccountArray(projectId);
                    }
                    if (projectUserNameArray.length != 0) {
                        columnMap.put(columnNum, projectUserNameArray);
                        dropDownMap.put(sheetNum, columnMap);
                    }
                    break;
                default:
                    break;
            }
            columnNum++;
        }

        sheetNum++;
        sheetMap.put("entityList", entityList);
        sheetMap.put("data", Collections.emptyList());

        sheets.add(sheetMap);


        Workbook workbook = ExcelExportPlusUtil.exportExcel(sheets);
        for (Map.Entry<Integer, Map<Integer, String[]>> sheetEntry : dropDownMap.entrySet()) {
            int sheetIndex = sheetEntry.getKey();
            for (Map.Entry<Integer, String[]> dropDownEntry : sheetEntry.getValue().entrySet()) {
                ExcelExportPlusUtil.addDropDownList(workbook, workbook.getSheetAt(sheetIndex), dropDownEntry.getValue(),
                        2,
                        65535, dropDownEntry.getKey());
            }
            Row row = workbook.getSheetAt(sheetIndex).getRow(0);
            // 设置title单元格行高
            row.setHeightInPoints(47);
            CellStyle cellStyle = row.getCell(0).getCellStyle();
            // 设置title单元格\n强制换行
            cellStyle.setWrapText(true);
            Font font = workbook.createFont();
            font.setBold(true);
            font.setColor(COLOR_RED);
            cellStyle.setFont(font);
            // 设置title单元格居左
            cellStyle.setAlignment(HorizontalAlignment.LEFT);
        }

        // 下载导入模板
        try {
            ExcelDownLoadUtil.export(response, workbook, "测试用例导入模板.xls");
        } catch (IOException e) {
            log.error("下载测试用例导入模板失败，原因:{}", e.getMessage(), e);
        }
    }

    private String[] getRequirementCodeAndNameArray(Long projectId) {
        Requirement requirement = Requirement.builder().projectId(projectId).build();
        List<Requirement> requirements = requirementApi.selectRequirementByCondition(requirement);
        return requirements.stream().map(item -> item.getCode() + " " + item.getName()).toArray(String[]::new);
    }

    private String[] getTreeNameArray(Long projectId) {
        List<ProductCaseLibrary> libraryList = productCaseLibraryService.list(Wraps.<ProductCaseLibrary>lbQ().eq(ProductCaseLibrary::getProductId, projectId));
        if (libraryList != null && libraryList.size() > 0) {
            List<ProductCaseTree> trees = productCaseTreeService.list(Wraps.<ProductCaseTree>lbQ().eq(ProductCaseTree::getLibraryId, libraryList.get(0).getId()));
            return trees.stream().map(item -> item.getName() + "#" + item.getId()).toArray(String[]::new);
        }
        return null;
    }

    private String[] getProjectUserNameAndAccountArray(Long projectId) {
        ProjectUser projectUser = ProjectUser.builder().projectId(projectId).build();
        List<ProjectUser> projectUsers = projectApi.selectProjectUserByCondition(projectUser);
        Set<Serializable> userIds = projectUsers.stream().map(ProjectUser::getUserId).collect(Collectors.toSet());

        if (userIds.isEmpty()) {
            return new String[0];
        }
        Map<Serializable, Object> userMap = userApi.findByIds(userIds);
        List<User> users =
                userMap.values().stream().map(o -> BeanPlusUtil.toBean(o, User.class)).collect(Collectors.toList());
        return users.stream().map(item -> item.getName() + "(" + item.getAccount() + ")").toArray(String[]::new);

    }

    @ApiOperation(value = "导入Excel, 错误excel在extra中，fileName为文件名，fileStream为错误excel的base64编码字符串")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "libraryId", value = "案例库id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_QUERY)
    })
    @PostMapping(value = "/import/{libraryId}")
    @Transactional
    @SysLog(value = "'导入Excel:' + #simpleFile?.originalFilename", request = false, optType = OptLogTypeEnum.IMPORT)
    public R<Boolean> importExcel(@PathVariable("libraryId") Long libraryId, @RequestParam(value = "treeId") Long treeId, @RequestParam(value = "file") MultipartFile simpleFile) throws Exception {
        Workbook workbook = getWorkBook(simpleFile);
        Sheet sheetAt = workbook.getSheetAt(0);
        if (Objects.equals(sheetAt.getRow(0).getCell(0),"1、导入模板不同项目不能通用\n2、带*的列必须填写\n3、测试步骤类型为文本类型时，直接填入内容；当测试步骤类型为条目时，测试步骤输入如下格式：1,xxxx,xxxx;2,xxxx,xxxx;")){

            return fail("Excel用例列表首行未参照用例模板!");
        }
        sheetAt.removeRow(sheetAt.getRow(0));
        int sheetIndex = workbook.getNumberOfSheets();
        List<TestProductCaseImportExcelVO> list = new ArrayList<>();
        List<TestProductCaseImportExcelVO> failList = new ArrayList<>();
        boolean verifyFail = false;
        Workbook failWb = null;
        for (int i = 0; i < sheetIndex; i++) {
            if (!workbook.isSheetHidden(i)) {
                ImportParams params = new ImportParams();
                params.setTitleRows(1);//大标题
                params.setHeadRows(1);//表头
                params.setNeedVerify(true);
                params.setStartSheetIndex(i);
                params.setSheetNum(1);
                params.setVerifyGroup(new Class[]{Default.class});
                params.setVerifyHandler(testProductCaseExcelVerifyHandlerImpl);
                // 使用新的字典处理器
                params.setDictHandler(testProductCaseDictHandler);
                InputStream inputStream = simpleFile.getInputStream();
                ExcelImportResult<TestProductCaseImportExcelVO> result =
                        ExcelImportUtil.importExcelMore(inputStream, TestProductCaseImportExcelVO.class,
                                params);
                List<TestProductCaseImportExcelVO> successList = result.getList();
                list.addAll(successList);
                failList.addAll(result.getFailList());
                if (result.isVerifyFail()) {
                    if (!verifyFail) {
                        verifyFail = result.isVerifyFail();
                    }
                    failWb = result.getFailWorkbook();
                }
            }
        }
        if(list.size() == 0  ){
            return fail("Excel用例列表为空!");
        }
        if(list.size()>0 && StringUtils.isEmpty(list.get(0).getName())){
            return fail("Excel用例填写第一列为空!");
        }
        // 清理新字典处理器的缓存
        testProductCaseDictHandler.clear();
        //组装数据
        List<TestProductCaseImportExcelVO> addList = assembleTestCaselist(list);
        Long allList = (long) addList.size();
        List<TestProductCaseImportExcelVO> remove = new ArrayList<>();
        addList.forEach(item ->
        {
            if (item.getName() == null && item.getName() == "")
            {
                remove.add(item);
                return;
            }
        });
        addList.removeAll(remove);
        List<TestProductCase> testCaselist  = new ArrayList<>();
        if (addList.size() != 0) {
            testCaselist = baseService.list(Wraps.<TestProductCase>lbQ().eq(TestProductCase::getTreeId, treeId).eq(TestProductCase::getState, 0)
                    .in(TestProductCase::getName, addList.stream().map(TestProductCaseImportExcelVO::getName).collect(Collectors.toList())));
            List<String> testCaseName = testCaselist.stream().map(TestProductCase::getName).collect(Collectors.toList());
            addList = addList.stream().filter(a -> !testCaseName.contains(a.getName())).collect(Collectors.toList());
        }
        //案例条数
        int nameCount = addList==null?0:addList.size();
        addList = deleteRepetitionTreesList(addList, testCaselist);
        //去重后条数
        int distinctCount = addList==null?0:addList.size();
        List<TestProductCase> taskList = new ArrayList<>();
        List<TestProductCaseHistory> testProductCaseHistories = new ArrayList<>();
        taskList = creatTaskList(taskList,addList,treeId,libraryId);
        if(!taskList.isEmpty())
        {
            baseService.saveBatch(taskList);
            taskList.forEach(item ->
            {
                TestProductCaseHistory testProductCaseHistory = TestProductCaseHistory.builder()
                        .caseId(item.getId())
                        .caseKey(item.getCaseKey())
                        .productId(item.getProductId())
                        .createdBy(item.getCreatedBy())
                        .name(item.getName())
                        .intent(item.getIntent())
                        .testStep(item.getTestStep())
                        .expectedResult(item.getExpectedResult())
                        .createTime(item.getCreateTime())
                        .leadingBy(item.getLeadingBy())
                        .libraryId(item.getLibraryId())
                        .prerequisite(item.getPrerequisite())
                        .priority(item.getPriority())
//                        .requirementId(item.getRequirementId())
                        .stateId(item.getStateId())
                        .stepType(item.getStepType())
                        .treeId(item.getTreeId())
                        .createdBy(item.getCreatedBy())
                        .updatedBy(item.getCreatedBy())
                        .updateTime(item.getUpdateTime())
                        .version(item.getVersion())
                        .execTime(item.getExecTime()).build();
                testProductCaseHistories.add(testProductCaseHistory);
            });
            testProductCaseHistoryService.saveBatch(testProductCaseHistories);
            baseService.saveAllOverView(taskList);
        }
        R<Boolean> r = success();
        r.setMsg("测试案例导入完成，共导入成功" + taskList.size() + "条，共" + allList + "条数据，" + "重复数据" + testCaselist.size() + "条,脏数据或失败" + (allList - testCaselist.size() - taskList.size()) + "条");
        if (verifyFail) {
            ByteArrayOutputStream bos = null;
            try {
                bos = new ByteArrayOutputStream();
                failWb.write(bos);
                Map<Object, Object> extra = Maps.newHashMapWithExpectedSize(7);
                extra.put("fileName", "测试案例导入错误信息.xls");
                byte[] bytes = bos.toByteArray();
                extra.put("fileStream", Base64Utils.encodeToString(bytes));
                r.setExtra(extra);
                bos.flush();
            } catch (Exception e) {
                log.error("获取导入测试案例错误文件流失败,原因:{}", e.getMessage(), e);
            } finally {
                IoUtil.close(bos);
            }
        }
        return r;
    }

    private List<TestProductCase> creatTaskList(List<TestProductCase> taskList, List<TestProductCaseImportExcelVO> addList,long treeId,long libraryId) throws Exception{
        //TODO 测试用例编号获取
        String caseKey = testProductCaseService.getProductCaseKey("");
        if (addList != null && !addList.isEmpty()) {
            for (int i = 0; i < addList.size(); i++) {
                TestProductCaseImportExcelVO testCaseImportExcelVO = addList.get(i);
                String name = testCaseImportExcelVO.getName();
                String runMond = testCaseImportExcelVO.getRunMond();
                String testStep = testCaseImportExcelVO.getTestStep();
                String expectedResult = testCaseImportExcelVO.getExpectedResult();
                TestProductCase testCase = new TestProductCase();
                testCase.setIntent(testCaseImportExcelVO.getSummary());
                testCase.setPrerequisite(testCaseImportExcelVO.getPrerequisite());
                testCase.setName(name);
                testCase.setTreeId(treeId);
                testCase.setId(UidGeneratorUtil.getId());
                testCase.setLibraryId(libraryId);
                testCase.setCreateTime(LocalDateTime.now());
                testCase.setCreatedBy(getUserId());
                testCase.setLeadingBy(getUserId());
                //当前默认导入用例等级为3普通
                testCase.setPriority(3);
                testCase.setVersion("v1.0");
                testCase.setUpdateTime(LocalDateTime.now());
                testCase.setUpdatedBy(getUserId());
                testCase.setState(false);
                testCase.setDraft(false);
                testCase.setCaseKey(caseKey);
                long l = StringUtil.getLongToStr(caseKey) + 1;
                caseKey = caseKey.split("-")[0] +"-"+ l;
                JSONArray jsonArray = new JSONArray();
                if (isValidFormat(testStep)) {
                    testCase.setStepType("subclause");
                    // 1. 按分号切割字符串，得到每个条目（如 "1,xxxx,xxxx"）
                    String[] testStepArr = testStep.split(";");
                    String[] expectedResultArr = StrUtil.isEmpty(expectedResult)? new String[]{""} :expectedResult.split(";");
                    for (int i1 = 0; i1 < testStepArr.length; i1++) {
                        String[] tsfields = testStepArr[i1].split(",");
                        // 处理 expectedResultArr 可能越界的情况
                        String erItem = (i1 < expectedResultArr.length) ? expectedResultArr[i1] : "";
                        String[] erfields = erItem.isEmpty() ? new String[]{"",""} : erItem.split(",");
                        if (tsfields.length < 2) {
                            System.err.println("警告：条目格式错误 - " + testStepArr[i1]);
                            continue;
                        }
                        //String field1 = tsfields[1].trim();
                        int caseStepNum = i1+1;
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("caseStepNum", caseStepNum);
                        jsonObject.put("caseStepDes", tsfields[1].trim());
                        if(StrUtil.isNotEmpty(erfields[1])){
                            jsonObject.put("expectResult", erfields[1].trim());
                        }
                        jsonArray.add(jsonObject);
                    }
                    testCase.setTestStep(jsonArray.toJSONString());
                } else {
                    testCase.setStepType("text");
                    testCase.setTestStep(testStep);
                    testCase.setExpectedResult(expectedResult);
                }
                Field field = TestProductCase.class.getDeclaredField("name");
                Size declaredAnnotation = field.getDeclaredAnnotation(Size.class);
                if ((name != null) && (name.length() < declaredAnnotation.max()) ) {
                    taskList.add(testCase);
                }
            }
        }
        return taskList;
    }
    public static boolean isValidFormat(String input) {
        if (input == null || input.isEmpty()) {
            return false;
        }

        // 正则表达式解释：
        // ^          - 字符串开始
        // \d+        - 一个或多个数字（序号）
        // ,          - 逗号分隔符
        // [^,]+      - 一个或多个非逗号字符（xxxx部分）
        // (?:        - 开始非捕获组
        //     ,[^,]+ - 逗号后跟一个或多个非逗号字符
        // )*         - 重复零次或多次
        // ;          - 分号结尾
        // (?:        - 开始非捕获组（用于重复整个模式）
        //     \d+,[^,]+(?:,[^,]+)*;
        // )*         - 重复零次或多次
        // $          - 字符串结束
        String regex = "^(\\d+,[^,]+(?:,[^,]+)*;)*$";

        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);

        // 检查整个字符串是否匹配
        if (!matcher.matches()) {
            return false;
        }

        // 进一步检查每个部分是否有至少一个xxxx字段
        String[] parts = input.split(";");
        for (String part : parts) {
            if (!part.isEmpty()) { // 跳过空部分（如果字符串以;结尾）
                String[] fields = part.split(",");
                if (fields.length < 2) { // 至少需要序号和一个xxxx字段
                    return false;
                }
                // 检查序号是否是数字
                try {
                    Integer.parseInt(fields[0]);
                } catch (NumberFormatException e) {
                    return false;
                }
            }
        }

        return true;
    }

    private static List<TestProductCaseImportExcelVO> assembleTestCaselist(List<TestProductCaseImportExcelVO> list) {
        if (list == null && list.isEmpty()) {
            return list;
        }
        //组装后数据
        List<TestProductCaseImportExcelVO> assembleTestCaselist = new ArrayList<>();
        //组装步骤和结果组合的list，方便后续好组装数据
        ArrayList<TestProductCaseImportExcelVO> testCaseImportExcelVOS = null;
        //中间转换类
        TestProductCaseImportExcelVO testCaseImportExcel = null;
        for (int i = 0; i < list.size(); i++) {
            TestProductCaseImportExcelVO testCaseImportExcelVO = list.get(i);
            String runMond = testCaseImportExcelVO.getRunMond();
//            //添加步骤
//            if (testCaseImportExcelVOS != null && testCaseImportExcelVOS.size() > 0) {
//                if ((i + 1) < list.size()) { //不是最后一行
//                    if (StringUtils.isNotEmpty(list.get(i + 1).getName())) {
//                        testCaseImportExcelVOS.add(testCaseImportExcelVO);
//                        testCaseImportExcel.setList(testCaseImportExcelVOS);
//                        assembleTestCaselist.add(testCaseImportExcel);
//                        testCaseImportExcelVOS = null;
//                    }
//                } else if ((i + 1) == list.size()) {//最后一行的逻辑
//                    testCaseImportExcelVOS.add(testCaseImportExcelVO);
//                    testCaseImportExcel.setList(testCaseImportExcelVOS);
//                    assembleTestCaselist.add(testCaseImportExcel);
//                    testCaseImportExcelVOS = null;
//                }
//            }
            if (StringUtils.isNotEmpty(runMond)) {
//                if ("手动".equals(runMond)) {
                testCaseImportExcel = testCaseImportExcelVO;
                //if(((i + 1) == list.size()) ||  (list.get(i + 1).getRunMond() != null)){
                assembleTestCaselist.add(testCaseImportExcel);
                //}
                testCaseImportExcelVOS = new ArrayList<>();
                continue;
//                } else {
//                    //添加手动
//                    assembleTestCaselist.add(testCaseImportExcelVO);
//                }
            } else {
                if (testCaseImportExcelVOS != null) {
                    testCaseImportExcelVOS.add(testCaseImportExcelVO);
                    if (assembleTestCaselist.get(assembleTestCaselist.size() - 1).getList() == null) {
                        testCaseImportExcelVOS.add(testCaseImportExcelVO);
                        assembleTestCaselist.get(assembleTestCaselist.size() - 1).setList(testCaseImportExcelVOS);
                    }
                }
            }
        }
        //如果assembleTestCaselist.getlist的size为2或者2以上，说明有重复的用例，删除list的第二个
        for (int i = 0; i < assembleTestCaselist.size(); i++) {
            TestProductCaseImportExcelVO testCaseImportExcelVO = assembleTestCaselist.get(i);
            List<TestProductCaseImportExcelVO> list1 = testCaseImportExcelVO.getList();
            if (list1 != null && list1.size() > 1) {
                list1.remove(1);
            }
        }
        return assembleTestCaselist;
    }

    private static List<TestProductCaseImportExcelVO> deleteRepetitionTreesList(List<TestProductCaseImportExcelVO> list, List<TestProductCase> testCaselist) {
        if (list == null || list.size() == 0) {
            return null;
        }
        if (testCaselist == null && testCaselist.size() == 0) {
            return null;
        }
//        boolean flag = false;
        for (int i = 0; list.size() > i; i++) {
            TestProductCaseImportExcelVO testCaseTree = list.get(i);
            String name = testCaseTree.getName();
            if (StringUtils.isNotEmpty(name)) {
                for (int j = 0; testCaselist.size() > j; j++) {
                    TestProductCase testCaseTree1 = testCaselist.get(j);
                    String name1 = testCaseTree1.getName();
                    if (StringUtils.isNoneEmpty(name) && name.equals(name1)) {
                        list.remove(testCaseTree);
//                        flag = true;
                        i--;
                        break;
                    }
                }
            }
//            else {
//                if (flag) {
//                    if ((i + 1) < list.size()) {
//                        if (StringUtils.isNotEmpty(list.get(i + 1).getName())) {
//                            flag = false;
//                        }
//                    } else if ((i + 1) == list.size()) {
//                        flag = false;
//                    }
//                    list.remove(testCaseTree);
//                    i--;
//                }
//            }
        }
        return list;
    }

    private Workbook getWorkBook(MultipartFile file) throws IOException {
        //这样写excel能兼容03和07
        InputStream is = file.getInputStream();
        Workbook hssfWorkbook;
        try {
            //用来支持Excel 2003
            hssfWorkbook = new HSSFWorkbook(is);
        } catch (Exception ex) {
            is = file.getInputStream();
            //用来支持Excel 2007
            hssfWorkbook = new XSSFWorkbook(is);
        }
        return hssfWorkbook;
    }

    /**
     * 获取任务的Code，从redis中获取
     *
     * @param prefix code前缀
     * @return String code
     * <AUTHOR>
     * @date 2022/01/17 17:55
     * @update lxr 2022/01/17 17:55
     * @since 1.0
     */
    private synchronized String getCaseKey(String prefix) {
        CacheKey cacheKey = new TestMCacheKeyBuilder().key(prefix);
        Long codeNum = cacheOps.incr(cacheKey);
        // 当codeNum为null或者0时，从数据库获取最大的num并存入redis中
        if (codeNum == null || codeNum == 1) {
            // 根据时间排序，获取最新一条数据
            TestProductCase testCase =
                    baseService.getOne(Wraps.<TestProductCase>lbQ().orderByDesc(TestProductCase::getCreateTime), false);
            long num = 1L;
            if (testCase != null) {
                num = StringUtil.getLongToStr(testCase.getCaseKey()) + 1;
            }
            cacheOps.incrBy(cacheKey, num);
            return prefix + (num + 1);
        }
        return prefix + codeNum;
    }



    @ApiOperation(value = "导出Word")
    @PostMapping(value = "/exportWord", produces = "application/octet-stream")
    @SysLog(value = "'导出Word", optType = OptLogTypeEnum.EXPORT)
    public void exportWord(@Validated @RequestBody TestProductCaseExportQuery model, HttpServletResponse response)
    {

        // 1.查询导出数据
        LbqWrapper<TestProductCase> wrapper = Wraps.lbQ();

        ProductCaseTree productCaseTree = productCaseTreeService.getById(model.getTreeId());
        wrapper.like(TestProductCase::getName, model.getName())
                .eq(TestProductCase::getRequirementId, model.getRequirementId())
                .eq(TestProductCase::getLeadingBy, model.getLeadingBy())
                .eq(TestProductCase::getDraft, 0)
                .eq(TestProductCase::getState, 0)
                .eq(TestProductCase::getLibraryId, model.getLibraryId());
        if (productCaseTree.getParentId() != 0){
            wrapper.eq(TestProductCase::getTreeId, model.getTreeId());
        }
        List<TestProductCase> list = baseService.list(wrapper);
        if (list.size()>5000){
            throw BizException.validFail("单次导出用例数量最多为5000");
        }
        XWPFDocument document = new XWPFDocument();
        XWPFParagraph p1 = document.createParagraph(); // 创建段落
        XWPFRun r1 = p1.createRun();// 创建段落文本
        r1.setText("测试用例信息");
        r1.addBreak();
        r1.setBold(true);
        r1.setFontSize(20);// 设置文本
        XWPFTable table = document.createTable(list.size()+1,6);
        table.setWidthType(TableWidthType.AUTO);
        int center = 0;
        XWPFTableRow row = table.getRow(center++);
        row.getCell(0).setText("用例名称");
        row.getCell(1).setText("执行方式");
        row.getCell(2).setText("测试意图");
        row.getCell(3).setText("前置条件");
        row.getCell(4).setText("步骤动作");
        row.getCell(5).setText("预期结果");


        for (TestProductCase item : list)
        {
            String stepType = item.getStepType();
            if ("text".equals(stepType))
            {
                XWPFTableRow row1 = table.getRow(center++);
                row1.getCell(0).setText(item.getName());
                row1.getCell(1).setText("手动");
                row1.getCell(2).setText(item.getIntent());
                row1.getCell(3).setText(item.getPrerequisite());
                row1.getCell(4).setText(item.getTestStep());
                row1.getCell(5).setText(item.getExpectedResult());
            }
            else
            {
                String testStep = item.getTestStep();
                boolean jsonType = getJsonType(testStep);
                JSONArray jsonArray = JSONArray.parseArray(testStep);
                //步骤不是json数组时候逻辑
                if (!jsonType || (jsonArray.size() == 0))
                {
                    XWPFTableRow row1 = table.getRow(center++);
                    row1.getCell(0).setText(item.getName());
                    row1.getCell(1).setText("手动");
                    row1.getCell(2).setText(item.getIntent());
                    row1.getCell(3).setText(item.getPrerequisite());
                    row1.getCell(4).setText(item.getTestStep());
                    row1.getCell(5).setText(item.getExpectedResult());
                }else {
                    for (int j = 0; j < jsonArray.size(); j++)
                    {
                        JSONObject jsonObject = jsonArray.getJSONObject(j);
                        String caseStepDes = jsonObject.getString("caseStepDes");
                        String expectResult = jsonObject.getString("expectResult");
                        if (j == 0)
                        {
                            XWPFTableRow row1 = table.getRow(center);
                            row1.getCell(0).setText(item.getName());
                            row1.getCell(1).setText("手动");
                            row1.getCell(2).setText(item.getIntent());
                            row1.getCell(3).setText(item.getPrerequisite());
                            row1.getCell(4).setText(caseStepDes);
                            row1.getCell(5).setText(expectResult);
                        }
                        else
                        {
                            XWPFTableRow row1 = table.getRow(center);
                            String text = row1.getCell(4).getText();
                            row1.getCell(4).setText(text+";"+caseStepDes);
                        }
                    }
                    center++;
                }
            }
        }

        document.setTable(0, table);
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("utf-8");
        try
        {
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + URLEncoder.encode("测试用例信息.docx", "UTF-8"));
            OutputStream out = response.getOutputStream();
            document.write(out);
            BufferedOutputStream bos = new BufferedOutputStream(out);
            bos.flush();
            out.close();
        }
        catch (IOException e)
        {
            e.printStackTrace();
        }


    }

    //用例目录名称拼接
    private String getTreeName(Long treeId,Map<Long, ProductCaseTree> productCaseTreeMap){
        String treeName = "";
        while (productCaseTreeMap.containsKey(treeId)){
            ProductCaseTree productCaseTree = productCaseTreeMap.get(treeId);
            if ("".equals(treeName)){
                treeName = productCaseTree.getName();
            }else {
                treeName = productCaseTree.getName() + ">" + treeName;
            }
            treeId = productCaseTree.getParentId();
        }
        return treeName;
    }
    //用例等级priority转换为中文 1:最高 2:较高 3:普通 4:较低 5:最低
    private String getPriority(Integer priority){
        String priorityName = "";
        switch (priority){
            case 1:
                priorityName = "最高";
                break;
            case 2:
                priorityName = "较高";
                break;
            case 3:
                priorityName = "普通";
                break;
            case 4:
                priorityName = "较低";
                break;
            case 5:
                priorityName = "最低";
                break;
        }
        return priorityName;
    }


    @ApiOperation(value = "导出Excel")
    @PostMapping(value = "/exportTestProductCaseExcel", produces = "application/octet-stream")
    @SysLog(value = "'导出Excel", optType = OptLogTypeEnum.EXPORT)
    public void exportTestProductCaseExcel(@Validated @RequestBody TestProductCaseExportQuery model, HttpServletResponse response)
    {

        try {
            List<List<ProductModuleFunction>> cascadeData = initializeHandlers(testProductCaseExcelDictHandlerImpl, null,
                    model.getProjectId(), model.getTaskId(), model.getTestreqId());

            // 根据条件查询待导出数据
            List<Long> functionIds = new ArrayList<>();
            if (model.getFunctionAndModuleId() != null && model.getFunctionAndModuleId() != 0L) {
                functionIds = productInfoApi.findByFunctionId(model.getFunctionAndModuleId());

                if (functionIds.isEmpty()) {
                    // 如果没有匹配的功能ID，则导出空文件
                    try (Workbook workbook = buildExcelExportWorkbook(testProductCaseExcelDictHandlerImpl, new ArrayList<>(), cascadeData)) {
                        ExcelDownLoadUtil.export(response, workbook, "测试用例信息.xlsx");
                    }
                    return;
                }
            }

            List<TestTabCase> testTabCaseList = new ArrayList<>();
            if (model.getTabName() != null && model.getTabName().length > 0) {
                // Optimize the tab processing logic
                Set<Long> prodIds = new HashSet<>();
                for (String tabId : model.getTabName()) {
                    Long id = Long.valueOf(tabId);
                    List<TestTabCase> cases = testTabCaseService.list(Wraps.<TestTabCase>lbQ().eq(TestTabCase::getTabId, id));

                    if (testTabCaseList.isEmpty()) {
                        testTabCaseList.addAll(cases);
                    } else {
                        cases.stream()
                                .filter(c -> prodIds.contains(c.getProdId()))
                                .forEach(testTabCaseList::add);
                    }

                    // Collect product IDs for next iteration
                    cases.stream()
                            .map(TestTabCase::getProdId)
                            .filter(Objects::nonNull)
                            .forEach(prodIds::add);
                }
            }
            // 1.查询导出数据
            LbqWrapper<TestProductCase> wrapper = Wraps.lbQ();
            wrapper.eq(model.getProductId() != null, TestProductCase::getProductId, model.getProductId())
                    .like(model.getName() != null, TestProductCase::getName, model.getName())
                    .eq(model.getLeadingBy() != null, TestProductCase::getLeadingBy, model.getLeadingBy())
                    .eq(model.getLibraryId() != null, TestProductCase::getLibraryId, model.getLibraryId())
                    .eq(model.getPriority() != null, TestProductCase::getPriority, model.getPriority())
                    .like(model.getCaseKey() != null, TestProductCase::getCaseKey, model.getCaseKey())
                    .eq(model.getCaseType() != null, TestProductCase::getCaseType, model.getCaseType())
                    .eq(TestProductCase::getState, 0)
                    .eq(TestProductCase::getDraft, 0)
                    .eq(model.getTaskId() != null, TestProductCase::getTaskId, model.getTaskId())
                    .eq(model.getRequirementId() != null, TestProductCase::getRequirementId, model.getRequirementId())
                    .eq(model.getProjectId() != null, TestProductCase::getProjectId, model.getProjectId())
                    .eq(model.getTestreqId() != null, TestProductCase::getTestreqId, model.getTestreqId())
                    .in(!functionIds.isEmpty(), TestProductCase::getModuleFunctionId, functionIds);

            if (!testTabCaseList.isEmpty()) {
                List<Long> prodIds = testTabCaseList.stream()
                        .map(TestTabCase::getProdId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                if (!prodIds.isEmpty()) {
                    wrapper.in(TestProductCase::getId, prodIds);
                }
            }

            List<TestProductCase> list = baseService.list(wrapper);

            List<ProductModuleFunction> functions = cascadeData.get(1);
            // 构建功能id 和 productId map
            Map<Long, Long> functionIdToProductId = functions.stream()
                    .collect(Collectors.toMap(ProductModuleFunction::getId, ProductModuleFunction::getProductId));

            List<Long> caseIds = list.stream().map(TestProductCase::getId).collect(Collectors.toList());
            Map<Long, List<Dictionary>> dictionaryMap = dictionaryApi.batchEchoDictionaryByBizIdsAndType(
                    caseIds, DictionaryRelationBizType.CASE.getCode());
            for (TestProductCase datum : list) {
                // Safely handle potential null in map lookup
                Long productId = functionIdToProductId.get(datum.getModuleFunctionId());
                if (productId != null) {
                    datum.setProductId(productId);
                }

                List<Dictionary> dictionaries = Optional.ofNullable(
                        dictionaryMap.get(datum.getId())
                ).orElse(Collections.emptyList());
                if (!dictionaries.isEmpty()) {
                    // 流式处理获取测试模式列表
                    List<String> testModes = dictionaries.stream()
                            .map(Dictionary::getCode)
                            .collect(Collectors.toList());

                    datum.setTestMode(String.join(",",testModes));
                }

            }
            // 3.执行excel导出
            try (Workbook workbook = buildExcelExportWorkbook(testProductCaseExcelDictHandlerImpl, list, cascadeData)) {
                testProductCaseExcelDictHandlerImpl.removeThreadLocal();
                ExcelDownLoadUtil.export(response, workbook, "测试用例信息.xlsx");
            }
        } catch (Exception e){
            log.error("导出测试用例数据失败", e);
            throw new BizException("导出测试用例数据失败: " + e.getMessage());
        }
    }

    /**
     * 设置字典及验证处理器并返回级联数据
     * @param dicHandler
     * @param verifyHandler
     * @param projectId
     * @param taskId
     * @param testReqId
     * @return {@link List< List< ProductModuleFunction>>}
     * @throws
     * <AUTHOR>
     * @date 2025/9/11 15:35
     * @update wzj 2025/9/11 15:35
     * @since 1.0
     */
    private List<List<ProductModuleFunction>> initializeHandlers(TestProductCaseExcelDictHandlerImpl dicHandler, TestProductCaseExcelVerifyHandlerImpl verifyHandler, Long projectId, Long taskId, Long testReqId) {

        // 设置字典处理器
        List<ProductModuleFunction> moduleFunctions = productModuleFunctionApi.findModuleFunctionByProjectId(
                projectId, taskId, testReqId, null);

        List<Long> productIds = moduleFunctions.stream()
                .map(ProductModuleFunction::getProductId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        List<ProductInfo> productInfos = productInfoApi.selectProductInfoByIds(productIds);
        dicHandler.setProductAndFunction(productInfos, moduleFunctions);
        List<Long> moduleFunctionIds = moduleFunctions.stream().map(ProductModuleFunction::getId).collect(Collectors.toList());
        dicHandler.setFunctionPoints(moduleFunctionIds);

        // 如果校验处理器存在，则初始化
        if (verifyHandler != null) {
            verifyHandler.init(moduleFunctions);
        }

        // 准备并返回级联数据
        List<ProductModuleFunction> tradeFunctions = moduleFunctions.stream()
                .filter(mf -> mf.getNodeType() == 2)
                .peek(tf -> tf.setParentId(tf.getProductId()))
                .collect(Collectors.toList());

        List<ProductModuleFunction> systemEntities = productInfos.stream()
                .map(pi -> {
                    ProductModuleFunction treeEntity = new ProductModuleFunction();
                    treeEntity.setId(pi.getId());
                    treeEntity.setName(pi.getName());
                    treeEntity.setParentId(null);
                    return treeEntity;
                })
                .collect(Collectors.toList());

        return Arrays.asList(systemEntities, tradeFunctions);
    }

    private Workbook buildExcelExportWorkbook(TestProductCaseExcelDictHandlerImpl dicHandler,
                                              List<TestProductCase> data, List<List<ProductModuleFunction>> sysAndTradeData)
    {
        ExportParams exportParams = new ExportParams();

//        if (data != null && !data.isEmpty()) {
//            Set<Serializable> userIds = data.stream()
//                    .map(TestProductCase::getCreatedBy)
//                    .filter(Objects::nonNull)
//                    .collect(Collectors.toSet());

//            if (!userIds.isEmpty()) {
//                Map<Serializable, Object> userMap = userApi.findByIds(userIds);
//                data.forEach(item -> {
//                    Object userObj = userMap.get(String.valueOf(item.getCreatedBy()));
//                    if (userObj != null) {
//                        User user = BeanPlusUtil.toBean(userObj, User.class);
//                        item.setLeader(user.getName());
//                    }
//                });
//            }
//        } else {
//            String[] exclusion = new String[]{"handler", "caseCount"}; // 导入模板不需要这两个字段
//            exportParams.setExclusions(exclusion);
//        }


        exportParams.setSheetName(SHEETNAME);
        exportParams.setDictHandler(testProductCaseExcelDictHandlerImpl);
        exportParams.setType(ExcelType.XSSF);
        exportParams.setTitle("1、导入模板不同项目不能通用\n2、带*的列必须填写\n3、测试步骤类型为文本类型时，直接填入内容；当测试步骤类型为条目时，测试步骤输入如下格式：1,xxxx,xxxx;2,xxxx,xxxx;");
        List<TestProductCase> exportData = new ArrayList<>();
        if (data == null)
        {
            exportData = new ArrayList<>();
        }else{
            for(TestProductCase testProductCase:data){
                String stepType = testProductCase.getStepType();
                if ("text".equals(stepType)) {
                    TestProductCase productCase = BeanUtil.toBean(testProductCase,TestProductCase.class);
                    exportData.add(productCase);

                } else {
                    String testStep = testProductCase.getTestStep();
                    // Check for null before processing
                    if (testStep == null) {
                        continue;
                    }
                    boolean jsonType = getJsonType(testStep);
                    JSONArray jsonArray = JSONArray.parseArray(testStep);

                    // 步骤不是json数组时候逻辑
                    if (!jsonType || jsonArray == null || jsonArray.size() == 0) {
                        TestProductCase productCase = BeanUtil.toBean(testProductCase,TestProductCase.class);
                        productCase.setTestStep(testProductCase.getTestStep());
                        productCase.setExpectedResult(testProductCase.getExpectedResult());
                        exportData.add(productCase);
                        continue;
                    }

                    for (int j = 0; j < jsonArray.size(); j++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(j);

                        String caseStepDes = jsonObject.getString("caseStepDes");
                        String expectResult = jsonObject.getString("expectResult");
                        if (j == 0) {
                            TestProductCase productCase = BeanUtil.toBean(testProductCase,TestProductCase.class);
                            productCase.setTestStep(caseStepDes);
                            productCase.setExpectedResult(expectResult);
                            exportData.add(productCase);
                        } else {
                            TestProductCase productCase = new TestProductCase();
                            productCase.setTestStep(caseStepDes);
                            productCase.setExpectedResult(expectResult);
                            exportData.add(productCase);
                        }

                    }
                }
            }
        }


        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, TestProductCase.class, exportData);

        Sheet sheet = workbook.getSheetAt(0);
        Row row = sheet.getRow(0);
        if (row != null) {
            // 设置title单元格行高
            row.setHeightInPoints(47);
            Cell cell = row.getCell(0);
            if (cell != null) {
                CellStyle cellStyle = cell.getCellStyle();
                // 设置title单元格\n强制换行
                cellStyle.setWrapText(true);
                Font font = workbook.createFont();
                font.setBold(true);
                font.setColor(COLOR_RED);
                cellStyle.setFont(font);
                // 设置title单元格居左
                cellStyle.setAlignment(HorizontalAlignment.LEFT);
            }
        }

        // 设置级联
        ExcelExportPlusUtil.createMultiLevelCascade(workbook, SHEETNAME, sysAndTradeData,
                new ExcelExportPlusUtil.CascadeConfig(0, 2, 9999));

        return workbook;
    }

    @ApiOperation(value = "下载测试过程用例导入模板")
    @GetMapping(value = "/template/{taskId}", produces = "application/octet-stream")
    @SysLog(value = "下载测试过程用例导入模板", optType = OptLogTypeEnum.OTHER)
    public void downloadTestProductCaseImportTemplate(@PathVariable("taskId") Long taskId, HttpServletResponse response) {
        Workbook workbook = testProductCaseService.downloadTestProductCaseImportTemplate(taskId);
        try {
            ExcelDownLoadUtil.export(response, workbook, "测试过程用例导入模板.xls");
        } catch (IOException e) {
            log.error("下载测试过程用例导入模板失败，原因:{}", e.getMessage(), e);
        }
    }

    @ApiOperation(value = "导出测试过程用例")
    @GetMapping(value = "/exportTestProductCases/{taskId}", produces = "application/octet-stream")
    @SysLog(value = "导出测试过程用例", optType = OptLogTypeEnum.OTHER)
    public void exportTestProductCases(@PathVariable("taskId") Long taskId, HttpServletResponse response) {
        Workbook workbook = testProductCaseService.exportTestProductCases(taskId);
        try {
            ExcelDownLoadUtil.export(response, workbook, "测试过程用例.xls");
        } catch (IOException e) {
            log.error("导出测试过程用例失败，原因:{}", e.getMessage(), e);
        }
    }

    @ApiOperation(value = "根据查询条件导出测试用例")
    @PostMapping(value = "/export", produces = "application/octet-stream")
    @SysLog(value = "根据查询条件导出测试用例", optType = OptLogTypeEnum.EXPORT)
    public void exportTestProductCasesByQuery(@Validated @RequestBody TestProductCasePageQuery model, HttpServletResponse response) {
        try {
            // 使用与分页查询相同的查询逻辑
            List<TestTabCase> testTabCaseList = new ArrayList<>();
            if(model.getTabName()!=null&&model.getTabName().length > 0){
                for (String tabId : model.getTabName()){
                    if (testTabCaseList.size() == 0) {
                        testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ().eq(TestTabCase::getTabId, Long.valueOf(tabId)));
                    }else{
                        List<Long> prodIds = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
                        testTabCaseList = testTabCaseService.list(Wraps.<TestTabCase>lbQ()
                                .eq(TestTabCase::getTabId, Long.valueOf(tabId))
                                .in(TestTabCase::getProdId, prodIds));
                    }
                    if (testTabCaseList.size() == 0){
                        // 如果交集为空，则导出空文件
                        Workbook emptyWorkbook = testProductCaseService.buildExcelExportWorkbook(new ArrayList<>());
                        ExcelDownLoadUtil.export(response, emptyWorkbook, "测试用例信息.xlsx");
                        return;
                    }
                }
            }

            LbqWrapper<TestProductCase> wrapper = Wraps.lbQ();

            wrapper.eq(TestProductCase::getProductId, model.getProductId())
                    .like(TestProductCase::getName, model.getName())
                    .eq(TestProductCase::getLeadingBy, model.getLeadingBy())
                    .eq(TestProductCase::getLibraryId,model.getLibraryId())
                    .eq(TestProductCase::getPriority,model.getPriority())
                    .like(TestProductCase::getCaseKey,model.getCaseKey())
                    .eq(TestProductCase::getState,model.getState())
                    .eq(TestProductCase::getDraft,model.getDraft())
                    .eq(TestProductCase::getTaskId,model.getTaskId())
                    .eq(TestProductCase::getRequirementId,model.getRequirementId())
                    .eq(TestProductCase::getProjectId,model.getProjectId())
                    .eq(TestProductCase::getTestreqId,model.getTestreqId());
            List<Long> collect = testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList());
            if (collect.size() > 0){
                wrapper.in(TestProductCase::getId, testTabCaseList.stream().map(TestTabCase::getProdId).collect(Collectors.toList()));
            }
            if(!model.getFunctionIds().isEmpty()){
                wrapper.in(TestProductCase::getModuleFunctionId,model.getFunctionIds());
            }

            // 添加自定义字段的条件筛选
            model.appendQuery(wrapper);

            List<TestProductCase> list = baseService.list(wrapper);

            // 3.执行excel导出
            Workbook workbook = testProductCaseService.buildExcelExportWorkbook(list);
            ExcelDownLoadUtil.export(response, workbook, "测试用例信息.xlsx");
        } catch (Exception e){
            log.error("导出测试用例数据失败", e);
            throw new BizException("导出测试用例数据失败: " + e.getMessage());
        }
    }


}
