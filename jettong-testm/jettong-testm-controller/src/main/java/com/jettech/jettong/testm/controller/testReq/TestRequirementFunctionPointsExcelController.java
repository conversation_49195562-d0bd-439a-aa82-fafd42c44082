package com.jettech.jettong.testm.controller.testReq;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Maps;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.QueryWrap;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.StringUtil;
import com.jettech.jettong.alm.api.RequirementApi;
import com.jettech.jettong.alm.issue.entity.Requirement;
import com.jettech.jettong.alm.issue.entity.Testreq;
import com.jettech.jettong.common.util.poi.ExcelDownLoadUtil;
import com.jettech.jettong.product.api.ProductInfoApi;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.dto.TestRequirementFunctionPointsPageQuery;
import com.jettech.jettong.testm.dto.TestRequirementFunctionPointsSaveDTO;
import com.jettech.jettong.testm.dto.TestRequirementFunctionPointsUpdateDTO;
import com.jettech.jettong.testm.entity.TestRequirementFunctionPoints;
import com.jettech.jettong.testm.service.TestRequirementFunctionPointsService;
import com.jettech.jettong.testm.utils.xmind.SqlParseUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 测试分析功能要点表导入导出信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试分析功能要点表导入导出信息控制器
 * @projectName jettong
 * @package com.jettech.jettong.testm.controller.testReq
 * @className TestRequirementFunctionPointsExcelController
 * @date 2025-09-07
  * @copyright 2025 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/testRequirementFunctionPoints/excel")
@Api(value = "TestRequirementFunctionPoints", tags = "测试分析功能要点")
public class TestRequirementFunctionPointsExcelController extends
        SuperController<TestRequirementFunctionPointsService, Long, TestRequirementFunctionPoints, TestRequirementFunctionPointsPageQuery,
                        TestRequirementFunctionPointsSaveDTO, TestRequirementFunctionPointsUpdateDTO>
{

    private final RequirementApi requirementApi;
    private final ProductInfoApi productInfoApi;

    @InitBinder
    public void initBinder(WebDataBinder binder)
    {
        binder.setDisallowedFields(new String[]{"export"});
    }

    @Override
    public R<TestRequirementFunctionPoints> save( TestRequirementFunctionPointsSaveDTO saveDTO)
    {

        TestRequirementFunctionPoints model = BeanUtil.toBean(saveDTO, getEntityClass());
        getBaseService().save(model);
        return R.success(model);
    }

    @Override
    public R<TestRequirementFunctionPoints> update(TestRequirementFunctionPointsUpdateDTO updateDTO)
    {
        TestRequirementFunctionPoints model = BeanUtil.toBean(updateDTO, getEntityClass());
        getBaseService().updateById(model);
        return R.success(model);
    }

    @Override
    public QueryWrap<TestRequirementFunctionPoints> handlerWrapper(TestRequirementFunctionPoints model,
            PageParams<TestRequirementFunctionPointsPageQuery> params)
    {
        QueryWrap<TestRequirementFunctionPoints> queryWrap = Wraps.q(model, params.getExtra(), getEntityClass());

        //自定义筛选
        Map<String, Object> extra = params.getExtra();
        if (!extra.isEmpty())
        {
            String sql = SqlParseUtil.parseToString(new TestRequirementFunctionPoints(), extra);
            queryWrap.last(sql);
        }
        return queryWrap;
    }


    @GetMapping(value = "/template/{projectId}/{taskId}", produces = "application/octet-stream")
    @ApiOperation(value = "下载导入模板")
    @SysLog(value = "下载导入模板", optType = OptLogTypeEnum.DOWNLOAD)
    public void template(HttpServletResponse response,@PathVariable(value = "projectId") String projectId,@PathVariable(value = "taskId") String taskId)
    {
        Long prjId = null;
        Long tskId = null;
        if (!StringUtil.isNumeric(projectId)){
            throw new BizException("项目ID必须为数字");
        }
        prjId = Long.valueOf(projectId);
        if (StringUtil.isNumeric(taskId)){
            tskId = Long.valueOf(taskId);
        }

        baseService.template(prjId, tskId);
    }

    @PostMapping(value = "/export")
    @ApiOperation(value = "导出")
    @SysLog(value = "导出", optType = OptLogTypeEnum.EXPORT)
    public void export(HttpServletResponse response, @RequestBody @Validated TestRequirementFunctionPointsPageQuery query)
    {
        baseService.export(query, query.getIsWHL());
    }






}