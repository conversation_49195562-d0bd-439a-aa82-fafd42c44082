package com.jettech.jettong.testm.controller.testCase;

import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.jettong.common.util.poi.ExcelDownLoadUtil;
import com.jettech.jettong.testm.dto.TestCasePageQuery;
import com.jettech.jettong.testm.service.TestCaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * TestCase Excel 导入导出控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-16
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/testcase/excel")
@Api(value = "TestCase", tags = "测试用例导入导出")
@RequiredArgsConstructor
public class TestCaseExcelController {

    private final TestCaseService testCaseService;

    @ApiOperation(value = "导出Excel")
    @PostMapping(value = "/export", produces = "application/octet-stream")
    @SysLog(value = "导出测试用例Excel", optType = OptLogTypeEnum.EXPORT)
    public void exportExcel(@Validated @RequestBody com.jettech.jettong.testm.dto.TestCasePageQuery params, HttpServletResponse response) {
        Workbook workbook = testCaseService.exportTestCases(params);
        try {
            ExcelDownLoadUtil.export(response, workbook, "测试用例.xls");
        } catch (IOException e) {
            log.error("导出文件失败，原因:{}", e.getMessage(), e);
        }
    }

    @ApiOperation(value = "下载测试用例导入模板")
    @GetMapping(value = "/downloadImportTemplate", produces = "application/octet-stream")
    @SysLog(value = "下载测试用例导入模板", optType = OptLogTypeEnum.OTHER)
    public void downloadImportTemplate(HttpServletResponse response) {
        Workbook workbook = testCaseService.downloadTestCaseImportTemplate();
        try {
            ExcelDownLoadUtil.export(response, workbook, "测试用例导入模板.xls");
        } catch (IOException e) {
            log.error("下载测试用例导入模板失败，原因:{}", e.getMessage(), e);
        }
    }
}
