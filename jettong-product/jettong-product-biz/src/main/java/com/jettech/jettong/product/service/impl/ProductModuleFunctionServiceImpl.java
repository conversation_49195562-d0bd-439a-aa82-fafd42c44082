package com.jettech.jettong.product.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.entity.SuperEntity;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.utils.CollHelper;
import com.jettech.basic.utils.SpringUtils;
import com.jettech.basic.utils.TreeUtil;
import com.jettech.jettong.alm.api.ProjectProductApi;
import com.jettech.jettong.alm.api.RequirementApi;
import com.jettech.jettong.alm.api.TaskApi;
import com.jettech.jettong.alm.issue.entity.Task;
import com.jettech.jettong.product.dao.ProductInfoMapper;
import com.jettech.jettong.product.dao.ProductModuleFunctionMapper;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.product.entity.ProductModuleFunctionItem;
import com.jettech.jettong.product.service.ProductModuleFunctionItemService;
import com.jettech.jettong.product.service.ProductModuleFunctionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 产品功能模块功能表业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品功能模块功能表业务层
 * @projectName jettong
 * @package com.jettech.jettong.product.service.impl
 * @className ProductModuleFunctionServiceImpl
 * @date 2021-11-10
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DS("#thread.tenant")
public class ProductModuleFunctionServiceImpl
        extends SuperServiceImpl<ProductModuleFunctionMapper, ProductModuleFunction>
        implements ProductModuleFunctionService
{

    private final ProjectProductApi projectProductApi;
    private final ProductModuleFunctionItemService productModuleFunctionItemService;
    private final TaskApi taskApi;
    private final RequirementApi requirementApi;
    private final ProductInfoMapper productInfoMapper;

    @Override
    public boolean check(Long productVersionId, String code)
    {
        //判断当前产品标识是否存在
        return count(Wraps.<ProductModuleFunction>lbQ().eq(ProductModuleFunction::getProductVersionId, productVersionId)
                .eq(ProductModuleFunction::getCode, code)) > 0;
    }

    @Override
    public void deleteProductModuleFunction(List<Long> ids)
    {
        //根据ids查询功能模块子级数据
        List<Long> productModuleIds =
                super.listObjs(
                        Wraps.<ProductModuleFunction>lbQ().select(ProductModuleFunction::getId)
                                .in(ProductModuleFunction::getParentId, ids),
                        Convert::toLong);
        if (!productModuleIds.isEmpty())
        {
            ids.addAll(productModuleIds);
            Boolean status = true;
            int i = 1;
            //循环获取所有的子级数据
            while (status && i < 50)
            {
                i++;
                productModuleIds =
                        super.listObjs(
                                Wraps.<ProductModuleFunction>lbQ().select(ProductModuleFunction::getId)
                                        .in(ProductModuleFunction::getParentId, productModuleIds),
                                Convert::toLong);
                ids.addAll(productModuleIds);
                if (productModuleIds.isEmpty())
                {
                    status = false;
                }
            }
        }
        if (!ids.isEmpty())
        {
            //super.update(Wraps.<ProductModuleFunction>lbU().set(ProductModuleFunction::getUpdateState, "delete").in
            // (SuperEntity::getId, ids));
            super.remove(Wraps.<ProductModuleFunction>lbU().in(SuperEntity::getId, ids));
        }
    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids)
    {
        List<ProductModuleFunction> productModuleFunctions = super.listByIds(ids);
        SpringUtils.getBean(EchoService.class).action(productModuleFunctions);
        return CollHelper.uniqueIndex(productModuleFunctions, ProductModuleFunction::getId,
                productModuleFunction -> productModuleFunction);
    }

    @Override
    public List<ProductModuleFunction> findByProjectId(Long projectId)
    {
        // 通过Feign调用获取项目关联的所有产品ID
        // 这里需要注入ProjectProductApi
        List<ProductInfo> productInfos = projectProductApi.findAllProduct(projectId);

        if (productInfos == null || productInfos.isEmpty())
        {
            return new ArrayList<>();
        }

        List<Long> productIds = productInfos.stream()
                .map(ProductInfo::getId)
                .collect(Collectors.toList());

        // 根据产品ID查询功能模块
        LbqWrapper<ProductModuleFunction> wrapper = Wraps.lbQ();
        wrapper.in(ProductModuleFunction::getProductId, productIds);

        return super.list(wrapper);
    }

    @Override
    public List<ProductModuleFunction> findByProjectIdWithConditions(Long projectId, Long bizId, Long reqId,
            Boolean isWHL)
    {
        // 获取模块ID列表
        List<Long> moduleIds = getModuleIdsByConditions(projectId, bizId, reqId, isWHL);

        // 构建并返回树形结构
        return buildResultTree(moduleIds);
    }

    @Override
    public List<Long> getModuleIdsByConditions(Long projectId, Long bizId, Long reqId, Boolean isWHL)
    {
        // 分支处理：优先返回结果，减少嵌套
        if (bizId != null)
        {
            return getModuleIdsByBizId(bizId);
        }
        else if (isWHL != null && isWHL)
        {
            if (reqId == null)
            {
                throw BizException.validFail("需求id不能为空");
            }
            return getModuleIdsByWHLReqId(reqId);
        }
        else if (reqId != null)
        {
            return getModuleIdsByReqId(reqId);
        }
        else
        {
            return getModuleIdsByProjectId(projectId);
        }
    }

    @Override
    public List<ProductModuleFunction> batchQueryModuleFunctions(List<Long> moduleIds)
    {
        if (moduleIds == null || moduleIds.isEmpty())
        {
            return Collections.emptyList();
        }
        return super.list(Wraps.<ProductModuleFunction>lbQ().in(ProductModuleFunction::getId, moduleIds));
    }

    @Override
    public List<ProductModuleFunction> batchQueryModuleFunctions(Long projectId, Long bizId, Long reqId, Boolean isWHL)
    {
        // 先获取模块ID列表
        List<Long> moduleIds = getModuleIdsByConditions(projectId, bizId, reqId, isWHL);
        // 调用原有的批量查询方法
        return batchQueryModuleFunctions(moduleIds);
    }

    @Override
    public Map<Long, ProductInfo> batchQueryProductInfoMap(List<Long> productIds)
    {
        if (productIds == null || productIds.isEmpty())
        {
            return Collections.emptyMap();
        }
        return productInfoMapper.selectBatchIds(productIds).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ProductInfo::getId, Function.identity()));
    }

    @Override
    public Map<Long, ProductInfo> batchQueryProductInfoMap(Long projectId, Long bizId, Long reqId, Boolean isWHL)
    {
        // 先获取模块功能列表
        List<ProductModuleFunction> functions = batchQueryModuleFunctions(projectId, bizId, reqId, isWHL);

        // 提取产品ID列表
        List<Long> productIds = functions.stream()
                .map(ProductModuleFunction::getProductId)
                .distinct()
                .collect(Collectors.toList());

        // 调用原有的批量查询方法
        return batchQueryProductInfoMap(productIds);
    }

    // 根据bizId获取模块ID列表
    private List<Long> getModuleIdsByBizId(Long bizId)
    {
        List<ProductModuleFunctionItem> itemList = productModuleFunctionItemService.list(
                Wraps.<ProductModuleFunctionItem>lbQ().eq(ProductModuleFunctionItem::getBizId, bizId));
        if (itemList.isEmpty())
        {
            return Collections.emptyList();
        }
        return itemList.stream()
                .map(ProductModuleFunctionItem::getModuleFunctionId)
                .collect(Collectors.toList());
    }

    // 根据维护类reqId获取模块ID列表
    private List<Long> getModuleIdsByWHLReqId(Long reqId)
    {
        List<Long> functionsByReqId = requirementApi.findFunctionsByReqId(reqId);
        if (functionsByReqId.isEmpty())
        {
            return Collections.emptyList();
        }
        List<ProductModuleFunction> list = super.list(
                Wraps.<ProductModuleFunction>lbQ().in(ProductModuleFunction::getProductId, functionsByReqId));
        return list.stream()
                .map(ProductModuleFunction::getId)
                .collect(Collectors.toList());
    }


    // 根据reqId获取模块ID列表
    private List<Long> getModuleIdsByReqId(Long reqId)
    {
        List<Task> tasks = taskApi.getTasksByReqId(reqId);
        if (tasks.isEmpty())
        {
            return Collections.emptyList();
        }
        List<Long> taskIds = tasks.stream().map(Task::getId).collect(Collectors.toList());
        List<ProductModuleFunctionItem> itemList = productModuleFunctionItemService.list(
                Wraps.<ProductModuleFunctionItem>lbQ().in(ProductModuleFunctionItem::getBizId, taskIds));
        if (itemList.isEmpty())
        {
            return Collections.emptyList();
        }
        return itemList.stream()
                .map(ProductModuleFunctionItem::getModuleFunctionId)
                .collect(Collectors.toList());
    }


    // 根据projectId获取模块ID列表
    private List<Long> getModuleIdsByProjectId(Long projectId)
    {
        List<ProductModuleFunction> functions = findByProjectId(projectId);
        if (functions.isEmpty())
        {
            return Collections.emptyList();
        }
        SpringUtils.getBean(EchoService.class).action(functions);
        return functions.stream()
                .map(ProductModuleFunction::getId)
                .collect(Collectors.toList());
    }


    @Override
    public List<ProductModuleFunction> buildTreeByProductIds(List<Long> productIds)
    {
        if (productIds == null || productIds.isEmpty())
        {
            return Collections.emptyList();
        }
        return buildResultTreeByProductIds(productIds);
    }

    @Override
    public boolean save(ProductModuleFunction entity)
    {
        bulkValidate(Collections.singleton(entity));
        return super.save(entity);
    }

    @Override
    public boolean updateById(ProductModuleFunction entity)
    {
        bulkValidate(Collections.singleton(entity));
        return super.updateById(entity);
    }

    @Override
    public boolean saveBatch(Collection<ProductModuleFunction> entityList, int batchSize)
    {
        bulkValidate(entityList);
        return super.saveBatch(entityList, batchSize);
    }

    @Override
    public boolean saveOrUpdateBatch(Collection<ProductModuleFunction> entityList, int batchSize)
    {
        bulkValidate(entityList);
        return super.saveOrUpdateBatch(entityList, batchSize);
    }

    private void bulkValidate(Collection<ProductModuleFunction> entities)
    {
        if (entities == null || entities.isEmpty())
        {
            return;
        }

        // 类型为2的节点不能作为父节点
        Set<Long> parentIds = entities.stream().map(ProductModuleFunction::getParentId).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (!parentIds.isEmpty())
        {
            boolean parentIsNodeType2 = super.count(
                    Wraps.<ProductModuleFunction>lbQ().in(ProductModuleFunction::getId, parentIds)
                            .eq(ProductModuleFunction::getNodeType, 2)) > 0;
            if (parentIsNodeType2)
            {
                throw new BizException("操作失败：功能/交易节点不能作为父节点。");
            }
        }

        // 将实体按校验规则分组 A 非首级模块   B 首级模块和交易
        List<ProductModuleFunction> constraintA_entities = entities.stream().filter
                        (e -> e.getParentId() != null && !Integer.valueOf(2).equals(e.getNodeType()))
                .collect(Collectors.toList());
        List<ProductModuleFunction> constraintB_entities = entities.stream().filter
                (e -> e.getParentId() == null || Objects.equals(e.getProductId(), e.getParentId())
                        || Integer.valueOf(2).equals(e.getNodeType())).collect(Collectors.toList());

        // 批处理内部数据重复
        validateIntraBatch(constraintA_entities, constraintB_entities);

        // 与数据库已有数据重复
        validateAgainstDb(constraintA_entities, constraintB_entities, entities);
    }

    private void validateIntraBatch(List<ProductModuleFunction> constraintA, List<ProductModuleFunction> constraintB)
    {
        Set<String> constraintA_nameKeys = new HashSet<>();
        Set<String> constraintA_codeKeys = new HashSet<>();
        for (ProductModuleFunction entity : constraintA)
        {
            if (entity.getName() != null && !constraintA_nameKeys.add(entity.getParentId() + "#" + entity.getName()))
            {
                throw new BizException("批量操作失败：父节点下存在重复的名称：'" + entity.getName() + "'");
            }
            if (entity.getCode() != null && !constraintA_codeKeys.add(entity.getParentId() + "#" + entity.getCode()))
            {
                throw new BizException("批量操作失败：父节点下存在重复的标识：'" + entity.getCode() + "'");
            }
        }

        Set<String> constraintB_nameKeys = new HashSet<>();
        Set<String> constraintB_codeKeys = new HashSet<>();
        for (ProductModuleFunction entity : constraintB)
        {
            if (entity.getProductId() != null && entity.getName() != null &&
                    !constraintB_nameKeys.add(entity.getProductId() + "#" + entity.getName()))
            {
                throw new BizException(
                        "批量操作失败：产品内存在重复的顶级模块或功能/交易名称：'" + entity.getName() + "'");
            }
            if (entity.getProductId() != null && entity.getCode() != null &&
                    !constraintB_codeKeys.add(entity.getProductId() + "#" + entity.getCode()))
            {
                throw new BizException(
                        "批量操作失败：产品内存在重复的顶级模块或功能/交易标识：'" + entity.getCode() + "'");
            }
        }
    }

    private void validateAgainstDb(List<ProductModuleFunction> constraintA, List<ProductModuleFunction> constraintB,
            Collection<ProductModuleFunction> allEntities)
    {
        Set<Long> idsToIgnore = allEntities.stream().map(ProductModuleFunction::getId).filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 约束数据库校验：父节点下唯一
        if (!constraintA.isEmpty())
        {
            Set<Long> parentIds =
                    constraintA.stream().map(ProductModuleFunction::getParentId).collect(Collectors.toSet());
            performDbCheck(constraintA,
                    Wraps.<ProductModuleFunction>lbQ().in(ProductModuleFunction::getParentId, parentIds), idsToIgnore,
                    e -> e.getParentId() + "#" + e.getName(), e -> e.getParentId() + "#" + e.getCode(),
                    "在同一个父节点下");
        }

        // 约束数据库校验：产品下唯一
        if (!constraintB.isEmpty())
        {
            Set<Long> productIds =
                    constraintB.stream().map(ProductModuleFunction::getProductId).collect(Collectors.toSet());
            LbqWrapper<ProductModuleFunction> wrapper =
                    Wraps.<ProductModuleFunction>lbQ().in(ProductModuleFunction::getProductId, productIds)
                            .and(w -> w.isNull(ProductModuleFunction::getParentId).or()
                                    .eq(ProductModuleFunction::getNodeType, 2));
            performDbCheck(constraintB, wrapper, idsToIgnore, e -> e.getProductId() + "#" + e.getName(),
                    e -> e.getProductId() + "#" + e.getCode(), "在产品下");
        }
    }

    private void performDbCheck(List<ProductModuleFunction> entities, LbqWrapper<ProductModuleFunction> wrapper,
            Set<Long> idsToIgnore, Function<ProductModuleFunction, String> nameKeyMapper,
            Function<ProductModuleFunction, String> codeKeyMapper, String msgPrefix)
    {
        Set<String> names = entities.stream().map(ProductModuleFunction::getName).filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Set<String> codes = entities.stream().map(ProductModuleFunction::getCode).filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 按名称校验
        if (!names.isEmpty())
        {
            LbqWrapper<ProductModuleFunction> nameWrapper = wrapper.clone().in(ProductModuleFunction::getName, names);
            if (!idsToIgnore.isEmpty())
            {
                nameWrapper.notIn(ProductModuleFunction::getId, idsToIgnore);
            }
            List<ProductModuleFunction> existing = super.list(nameWrapper);
            if (!existing.isEmpty())
            {
                Map<String, ProductModuleFunction> map =
                        existing.stream().collect(Collectors.toMap(nameKeyMapper, p -> p, (p1, p2) -> p1));
                for (ProductModuleFunction entity : entities)
                {
                    if (entity.getName() != null && map.containsKey(nameKeyMapper.apply(entity)))
                    {
                        throw new BizException(msgPrefix + "，名称 '" + entity.getName() + "' 已存在。");
                    }
                }
            }
        }

        // 按标识校验
        if (!codes.isEmpty())
        {
            LbqWrapper<ProductModuleFunction> codeWrapper = wrapper.clone().in(ProductModuleFunction::getCode, codes);
            if (!idsToIgnore.isEmpty())
            {
                codeWrapper.notIn(ProductModuleFunction::getId, idsToIgnore);
            }
            List<ProductModuleFunction> existing = super.list(codeWrapper);
            if (!existing.isEmpty())
            {
                Map<String, ProductModuleFunction> map =
                        existing.stream().collect(Collectors.toMap(codeKeyMapper, p -> p, (p1, p2) -> p1));
                for (ProductModuleFunction entity : entities)
                {
                    if (entity.getCode() != null && map.containsKey(codeKeyMapper.apply(entity)))
                    {
                        throw new BizException(msgPrefix + "，标识 '" + entity.getCode() + "' 已存在。");
                    }
                }
            }
        }
    }

    // 核心优化：统一树构建逻辑（根据模块ID列表）
    private List<ProductModuleFunction> buildResultTree(List<Long> moduleIds)
    {
        if (moduleIds.isEmpty())
        {
            return Collections.emptyList();
        }

        // 批量查询模块功能数据
        List<ProductModuleFunction> functions = batchQueryModuleFunctions(moduleIds);

        // 批量加载产品信息（解决N+1查询）
        List<Long> productIds = functions.stream()
                .map(ProductModuleFunction::getProductId)
                .distinct()
                .collect(Collectors.toList());

        return buildResultTreeByProductIds(productIds, functions);
    }

    // 根据产品ID列表构建树形结构
    private List<ProductModuleFunction> buildResultTreeByProductIds(List<Long> productIds)
    {
        if (productIds.isEmpty())
        {
            return Collections.emptyList();
        }

        // 根据产品ID查询所有相关的模块功能
        LbqWrapper<ProductModuleFunction> wrapper = Wraps.lbQ();
        wrapper.in(ProductModuleFunction::getProductId, productIds);
        List<ProductModuleFunction> functions = super.list(wrapper);

        if (functions.isEmpty())
        {
            return Collections.emptyList();
        }

        // 执行echo填充
        SpringUtils.getBean(EchoService.class).action(functions);

        return buildResultTreeByProductIds(productIds, functions);
    }

    // 通用的树构建方法
    private List<ProductModuleFunction> buildResultTreeByProductIds(List<Long> productIds,
            List<ProductModuleFunction> functions)
    {
        if (productIds.isEmpty() || functions.isEmpty())
        {
            return Collections.emptyList();
        }

        // 批量加载产品信息（解决N+1查询）
        Map<Long, ProductInfo> productInfoMap = batchQueryProductInfoMap(productIds);

        // 构建树形结构
        List<ProductModuleFunction> tree = TreeUtil.buildTree(functions);
        List<ProductModuleFunction> resultTree = new ArrayList<>();

        // 按产品分组并组装树节点
        Map<Long, List<ProductModuleFunction>> functionMap = tree.stream()
                .collect(Collectors.groupingBy(ProductModuleFunction::getProductId));

        // 按照传入的产品ID顺序构建结果树
        for (Long productId : productIds)
        {
            ProductInfo productInfo = productInfoMap.get(productId);
            if (productInfo != null)
            {
                ProductModuleFunction rootNode = new ProductModuleFunction();
                rootNode.setId(productInfo.getId());
                rootNode.setCode(productInfo.getCode());
                rootNode.setName(productInfo.getName());
                rootNode.setNodeType(0);

                List<ProductModuleFunction> funcList = functionMap.get(productId);
                if (funcList != null && !funcList.isEmpty())
                {
                    funcList.forEach(func -> func.setParentId(productId)); // 设置父ID
                    rootNode.setChildren(funcList);
                }
                else
                {
                    rootNode.setChildren(Collections.emptyList());
                }
                resultTree.add(rootNode);
            }
        }
        return resultTree;
    }
}