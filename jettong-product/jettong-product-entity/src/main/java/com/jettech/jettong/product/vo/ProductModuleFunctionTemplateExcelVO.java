package com.jettech.jettong.product.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.handler.inter.IExcelDataModel;
import cn.afterturn.easypoi.handler.inter.IExcelModel;
import io.swagger.annotations.ApiModel;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 产品功能模块Excel导入模板对象
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品功能模块Excel导入模板对象
 * @projectName jettong
 * @package com.jettech.jettong.product.vo
 * @className ProductModuleFunctionTemplateExcelVO
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "ProductModuleFunctionTemplateExcelVO", description = "产品功能模块Excel导入模板")
public class ProductModuleFunctionTemplateExcelVO implements Serializable, IExcelModel, IExcelDataModel {

    private String errorMsg;

    private Integer rowNum;

    /**
     * 所属系统
     */
    @Excel(name = "*所属系统", width = 30, orderNum = "0")
    private String systemName;

    /**
     * 一级模块
     */
    @Excel(name = "一级模块", width = 30, orderNum = "1")
    private String firstModule;

    /**
     * 二级模块
     */
    @Excel(name = "二级模块", width = 30, orderNum = "2")
    private String secondModule;

    /**
     * 交易编码
     */
    @Excel(name = "*标识", width = 20, orderNum = "3")
    private String code;

    /**
     * 交易名称
     */
    @Excel(name = "*交易名称", width = 40, orderNum = "4")
    private String name;

    /**
     * 交易类型
     */
    @Excel(name = "*交易类型", width = 20, orderNum = "5")
    private String type;

    /**
     * 交易状态
     */
    @Excel(name = "*交易状态", width = 20, orderNum = "6")
    private String status;

    /**
     * 交易描述
     */
    @Excel(name = "交易描述", width = 100, orderNum = "7")
    private String description;

    /**
     * 执行方式
     */
    @Excel(name = "执行方式", width = 20, orderNum = "8")
    private String executionMode;

    /**
     * 归属测试经理
     */
    @Excel(name = "归属测试经理", width = 20, orderNum = "9")
    private String testManager;

    /**
     * 获取行号
     *
     * @return Integer
     */
    @Override
    public Integer getRowNum() {
        return rowNum;
    }

    /**
     * 设置行号
     *
     * @param rowNum 行号
     */
    @Override
    public void setRowNum(Integer rowNum) {
        this.rowNum = rowNum;
    }

    /**
     * 获取错误数据
     *
     * @return String
     */
    @Override
    public String getErrorMsg() {
        return errorMsg;
    }

    /**
     * 设置错误信息
     *
     * @param errorMsg 错误信息
     */
    @Override
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}