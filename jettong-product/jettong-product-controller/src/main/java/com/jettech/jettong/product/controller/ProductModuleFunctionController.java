package com.jettech.jettong.product.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.controller.SuperController;
import com.jettech.basic.base.entity.TreeEntity;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.log.entity.OptLogTypeEnum;
import com.jettech.basic.utils.ArgumentAssert;
import com.jettech.basic.utils.StringUtil;
import com.jettech.basic.utils.TreeUtil;
import com.jettech.jettong.alm.api.ProjectApi;
import com.jettech.jettong.alm.api.TestreqApi;
import com.jettech.jettong.alm.issue.entity.Testreq;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.base.api.DictionaryApi;
import com.jettech.jettong.base.api.PersonalizedTableViewApi;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.base.entity.sys.dictionary.Dictionary;
import com.jettech.jettong.common.constant.DictionaryType;
import com.jettech.jettong.common.util.poi.ExcelDownLoadUtil;
import com.jettech.jettong.product.dto.ProductInfoDTO;
import com.jettech.jettong.product.dto.ProductModuleFunctionPageQuery;
import com.jettech.jettong.product.dto.ProductModuleFunctionSaveDTO;
import com.jettech.jettong.product.dto.ProductModuleFunctionUpdateDTO;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.product.service.ProductInfoService;
import com.jettech.jettong.product.service.ProductModuleFunctionService;
import com.jettech.jettong.product.vo.ProductModuleFunctionImportExcelVO;
import com.jettech.jettong.product.vo.ProductModuleFunctionTemplateExcelVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 产品功能模块功能表控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 产品功能模块功能表控制器
 * @projectName jettong
 * @package com.jettech.jettong.product.controller
 * @className ProductModuleFunctionController
 * @date 2021-11-10
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/product/productModuleFunction")
@Api(value = "ProductModuleFunction", tags = "产品功能模块功能表")
@PreAuth(replace = "product:productModuleFunction:")
@RequiredArgsConstructor
public class ProductModuleFunctionController extends
        SuperController<ProductModuleFunctionService, Long, ProductModuleFunction, ProductModuleFunctionPageQuery,
                ProductModuleFunctionSaveDTO, ProductModuleFunctionUpdateDTO>
{

    private final EchoService echoService;

    private final PersonalizedTableViewApi tableViewApi;

    private final ProductInfoService productInfoService;
    private final TestreqApi testreqApi;
    private final ProjectApi projectApi;
    private final ProductModuleFunctionService productModuleFunctionService;
    private final DictionaryApi dictionaryApi;


    @ApiOperation(value = "查询产品下的功能模块树", notes = "查询产品下的功能模块树")
    @PostMapping("/tree")
    @PreAuth("hasAnyPermission('{}view')")
    @SysLog(value = "查询产品下的功能模块树", request = false)
    public R<List<ProductModuleFunction>> tree(@RequestBody() PageParams<ProductModuleFunctionPageQuery> params)
    {
        ProductModuleFunctionPageQuery query = params.getModel();
        LbqWrapper<ProductModuleFunction> wrapper = Wraps.lbQ();
        wrapper.like(TreeEntity::getName, query.getName())
                .eq(ProductModuleFunction::getProductId, query.getProductId())
                .like(ProductModuleFunction::getCode, query.getCode())
                .eq(ProductModuleFunction::getParentId,query.getParentId());//根据id查询子集
//                .in(ProductModuleFunction::getProductManager, query.getProductManager())
//                .in(ProductModuleFunction::getDevelopmentManager, query.getDevelopmentManager());

        List<ProductModuleFunction> list = baseService.list(wrapper);

      /*  list.forEach(item ->
        {
            if (item.getWorth() == null || item.getInvestment() == null || item.getWorth() == 0)
            {
                item.setProportion("0.0%");
            }
            else
            {
                item.setProportion(proportionDouble(item.getInvestment(), item.getWorth(), 2) + "%");
            }
            item.setValue(0);
        });*/

        List<ProductModuleFunction> moduleFunctionList = setValue(list);
        echoService.action(moduleFunctionList);

        // 保存最后一次查询条件
        tableViewApi.saveLastSearch(getUserId(), "productModuleTable", params);

        return this.success(TreeUtil.buildTree(moduleFunctionList));
    }

    @ApiOperation(value = "查询产品和功能模块树", notes = "查询产品和功能模块树")
    @PostMapping("/wiki/tree")
    @PreAuth("hasAnyPermission('{}view')")
    @SysLog(value = "查询产品和功能模块树", request = false)
    public R<List<ProductInfoDTO>> wikiTree(@RequestBody ProductModuleFunctionPageQuery query)
    {
        List<Long> productIds = new ArrayList<>();
        if (query.getProjectId() != null){
            ProjectInfo project = projectApi.findById(query.getProjectId());
            if (project != null){
                if (project.getProductId() != null){
                    productIds.add(project.getProductId());
                }
                if (project.getAssistProductIds() != null){
                    productIds.addAll(project.getAssistProductIds());
                }
            }
        }
        if (query.getTestreqId() != null){
            Testreq testreq = testreqApi.getTestreq(query.getTestreqId());
            if (testreq != null){
                if (testreq.getProjectId() != null){
                    productIds.add(testreq.getProductId());
                }
                 if (testreq.getAssistProductIds() != null){
                    productIds.addAll(testreq.getAssistProductIds());
                }
            }
        }

        Map<Long, List<ProductModuleFunction>>  map =
                baseService.list(Wraps.<ProductModuleFunction>lbQ().in(ProductModuleFunction::getProductId, productIds).eq(ProductModuleFunction::getNodeType,1)).stream().collect(Collectors.groupingBy(ProductModuleFunction::getProductId));
        if (CollUtil.isEmpty(map)){
            return this.success(Collections.emptyList());
        }
        List<ProductInfo> productInfos = productInfoService.listByIds(map.keySet());
        List<ProductInfoDTO> productInfoDTOS = BeanUtil.copyToList(productInfos, ProductInfoDTO.class);
        productInfoDTOS.forEach(item ->
            item.setChildren(TreeUtil.buildTree(map.get(item.getId())))
        );
        return success(productInfoDTOS);
    }

    //设置value值
    private List<ProductModuleFunction> setValue(List<ProductModuleFunction> list)
    {
        Map<Long, ProductModuleFunction> listMap =
                list.stream().collect(
                        Collectors.toMap(ProductModuleFunction::getId, productModuleFunction -> productModuleFunction));
        for (ProductModuleFunction pm : list)
        {
            //判断是否可以需要查询
            boolean isFor = true;
            //初始化赋值
            Long moduleFunctionId = pm.getId();
            while (isFor)
            {
                ProductModuleFunction pmf = listMap.get(moduleFunctionId);
                if (pmf != null)
                {
                    moduleFunctionId = pmf.getParentId();
                    if(pmf.getValue() != null){
                        pmf.setValue(pmf.getValue() + 1);
                    }
                    if (pmf.getChildren() == null)
                    {
                        pmf.setChildren(new ArrayList<>());
                    }
                }
                else
                {
                    isFor = false;
                }
            }
        }
        return list;
    }

    /**
     * 计算百分比 保留留n位小数
     *
     * @param divisor
     * @param dividend
     * @param bit
     * @return
     */
    private static String proportionDouble(Integer divisor, Integer dividend, Integer bit)
    {
        if (dividend == null || divisor == null || bit == null)
        {
            return "0";
        }
        NumberFormat numberFormat = NumberFormat.getInstance();
        // 设置精确到小数点后2位
        numberFormat.setMaximumFractionDigits(bit);
        String result = numberFormat.format((float) divisor / (float) dividend * 100);

        return result;
    }

    /**
     * 重写保存逻辑
     *
     * @param data 产品DTO
     * @return 数据
     */
    @Override
    public R<ProductModuleFunction> handlerSave(ProductModuleFunctionSaveDTO data)
    {
        ProductModuleFunction productModuleFunction = BeanUtil.toBean(data, ProductModuleFunction.class);
        if (null == data.getParentId())
        {
            productModuleFunction.setParentId(data.getProductVersionId());
        }
        productModuleFunction.setUpdateState("add");
        baseService.save(productModuleFunction);
        return success(productModuleFunction);
    }


    /**
     * 重写修改逻辑
     *
     * @param data 产品DTO
     * @return 数据
     */
    @Override
    public R<ProductModuleFunction> handlerUpdate(ProductModuleFunctionUpdateDTO data)
    {
        ProductModuleFunction productModuleFunction = BeanUtil.toBean(data, ProductModuleFunction.class);
        productModuleFunction.setUpdateState("update");
        baseService.updateById(productModuleFunction);
        return success(productModuleFunction);
    }

    @Override
    public R<ProductModuleFunction> get(Long id) {
        R<ProductModuleFunction> r = super.get(id);
        echoService.action(r.getData());
        return r;
    }

    @Override
    public R<List<ProductModuleFunction>> query(ProductModuleFunction data) {
        R<List<ProductModuleFunction>> query = super.query(data);
        echoService.action(query.getData());
        return query;
    }

    @Override
    public IPage<ProductModuleFunction> query(PageParams<ProductModuleFunctionPageQuery> params) {

        IPage<ProductModuleFunction> page = params.buildPage(getEntityClass());
        ProductModuleFunctionPageQuery model = params.getModel();

        LbqWrapper<ProductModuleFunction> wrapper = Wraps.lbQ();
        wrapper.like(TreeEntity::getName, model.getName())
                .eq(ProductModuleFunction::getProductId, model.getProductId())
                .like(ProductModuleFunction::getCode, model.getCode());
//                .in(ProductModuleFunction::getProductManager, model.getProductManager())
//                .in(ProductModuleFunction::getDevelopmentManager, model.getDevelopmentManager());
        baseService.page(page, wrapper);
        echoService.action(page);
        return page;
    }

    /**
     * 重写删除逻辑
     *
     * @param ids 功能模块id
     * @return 数据
     */
    @Override
    public R<Boolean> handlerDelete(List<Long> ids)
    {
        baseService.deleteProductModuleFunction(ids);
        return R.success(true);
    }

    @ApiOperation(value = "根据项目ID查询产品功能模块", notes = "根据项目ID查询产品功能模块")
    @GetMapping("/findByProjectId/{projectId}")
    @PreAuth("hasAnyPermission('{}view')")
    @SysLog(value = "根据项目ID查询产品功能模块", request = false)
    public R<List<ProductModuleFunction>> findByProjectId(
            @PathVariable Long projectId,
            @RequestParam(value = "bizId", required = false) Long bizId,
            @RequestParam(value = "reqId", required = false) Long reqId,
            @RequestParam(value = "isWHL",required = false)Boolean isWHL) {

        // 调用Service层的重构方法
        List<ProductModuleFunction> result = baseService.findByProjectIdWithConditions(projectId, bizId, reqId, isWHL);

        // 应用Echo服务处理
//        echoService.action(result);

        return success(result);
    }

    @ApiOperation(value = "根据产品ID列表查询产品功能模块树", notes = "根据项目ID查询产品功能模块树")
    @GetMapping("/findTreeByProductIds")
    @PreAuth("hasAnyPermission('{}view')")
    @SysLog(value = "根据产品ID列表查询产品功能模块树", request = false)
    public R<List<ProductModuleFunction>> findTreeByProductIds(@RequestParam("ids") List<String> ids) {
        List<Long> productIds = ids.stream().filter(StringUtil::isNumeric).map(Long::valueOf).collect(Collectors.toList());
        if (productIds.isEmpty()){
            return  fail("未传入产品ID");
        }
        // 调用Service层的重构方法
        List<ProductModuleFunction> result = baseService.buildTreeByProductIds(productIds);

        return success(result);
    }


    @GetMapping(value = "/moduleFunctionTemplate", produces = "application/octet-stream")
    @ApiOperation(value = "下载产品功能模块导入模板")
    @SysLog(value = "下载产品功能模块导入模板", optType = OptLogTypeEnum.DOWNLOAD)
    public void template(HttpServletResponse response)
    {
        ExportParams exportParams = new ExportParams(null,
                "模块交易导入模板");
        
        // 获取所属系统下拉框数据（product_info表中的name和code字段）
        List<ProductInfo> productInfoList = productInfoService.list();
        String[] systemOptions = productInfoList.stream()
                .map(p -> p.getName() + "(" + p.getCode() + ")")
                .toArray(String[]::new);

        // 交易类型下拉框选项
        String[] tradeTypeOptions = dictionaryApi.query(Dictionary.builder().type(DictionaryType.TRADE_TYPE).build())
                .stream().map(Dictionary::getName).toArray(String[]::new);;
        
        // 交易状态下拉框选项
        String[] statusOptions = {"禁用", "启用"};
        
        // 执行方式下拉框选项
        String[] executionModeOptions = dictionaryApi.query(Dictionary.builder().type(DictionaryType.EXECUTION_METHOD).build())
                .stream().map(Dictionary::getName).toArray(String[]::new);;
        
        // 创建模板数据对象
        ProductModuleFunctionTemplateExcelVO templateExcelVO = new ProductModuleFunctionTemplateExcelVO();
        List list = new ArrayList<>();
        list.add(templateExcelVO);

        // 导出Excel模板
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams,
                ProductModuleFunctionTemplateExcelVO.class, list);
        
        // 设置下拉框映射
        Map<Integer, Map<Integer, String[]>> dropDownMap = Maps.newHashMapWithExpectedSize(1);
        Map<Integer, String[]> columnMap = Maps.newHashMapWithExpectedSize(1);
        dropDownMap.put(0, columnMap);
        // 第0列：所属系统
        columnMap.put(0, systemOptions);
        // 第5列：交易类型
        columnMap.put(5, tradeTypeOptions);
        // 第6列：交易状态
        columnMap.put(6, statusOptions);
        // 第8列：执行方式
        columnMap.put(8, executionModeOptions);
        
        // 应用下拉框设置
        ExcelDownLoadUtil.makeDropDownMap(workbook, dropDownMap, 1);
        
        // 下载导入模板
        try
        {
            ExcelDownLoadUtil.export(response, workbook, "产品功能模块导入模板.xls");
        }
        catch (IOException e)
        {
            log.error("下载产品功能模块导入模板失败，原因:{}", e.getMessage(), e);
        }
    }

//    /**
//     * 导入产品功能模块数据
//     *
//     * @param file 上传的Excel文件
//     * @return 导入结果
//     */
//    @PostMapping(value = "/import")
//    @ApiOperation(value = "导入产品功能模块数据")
//    @SysLog(value = "导入产品功能模块数据", optType = OptLogTypeEnum.IMPORT)
//    public R<Boolean> importExcel(HttpServletResponse response, @RequestParam(value = "file") MultipartFile file)
//    {
//        if (file == null || file.isEmpty())
//        {
//            return R.fail("文件不能为空");
//        }
//
//        ImportParams params = new ImportParams();
//        params.setTitleRows(0);
//        params.setHeadRows(1);
//        params.setNeedVerify(true);
//
//        try
//        {
//            // 解析Excel数据
//            ExcelImportResult<ProductModuleFunctionImportExcelVO> result =
//                ExcelImportUtil.importExcelMore(file.getInputStream(),
//                ProductModuleFunctionImportExcelVO.class, params);
//
//            List<ProductModuleFunctionImportExcelVO> successList = result.getList();
//
//            if (!successList.isEmpty())
//            {
//                // 处理导入数据
//                processImportData(successList);
//            }
//        }
//        catch (Exception e)
//        {
//            log.error("导入产品功能模块失败，原因:{}", e.getMessage(), e);
//            return R.fail("导入失败：" + e.getMessage());
//        }
//        return R.success(true);
//    }







    /**
     * 导出产品功能模块数据
     * 
     * @param productIds 产品ID列表
     * @return 导出结果
     */
    @PostMapping(value = "/export")
    @ApiOperation(value = "导出产品功能模块数据")
    @SysLog(value = "导出产品功能模块数据", optType = OptLogTypeEnum.EXPORT)
    public void exportExcel(HttpServletResponse response, @RequestBody List<String> productIds)
    {
        if (CollUtil.isEmpty(productIds))
        {
            throw new BizException("产品ID列表不能为空");
        }

        try
        {
            // 查询所有产品功能模块数据
            List<ProductModuleFunction> allFunctions = baseService.list(
                Wraps.<ProductModuleFunction>lbQ().in(ProductModuleFunction::getProductId, productIds));

            // 使用EchoService处理关联数据
            echoService.action(allFunctions);

            List<ProductModuleFunction> tree = TreeUtil.buildTree(allFunctions);

            // 2. 预缓存每个节点的完整路径和层级深度（关键优化）
            Map<Long, List<ProductModuleFunction>> nodePathMap = new HashMap<>(); // 节点ID -> 完整路径
            Map<Long, Integer> nodeDepthMap = new HashMap<>(); // 节点ID -> 层级深度

            // 递归遍历树形结构，缓存路径和深度
            for (ProductModuleFunction root : tree) {
                // 根节点的路径就是自身，深度为1
                List<ProductModuleFunction> rootPath = new ArrayList<>();
                rootPath.add(root);
                nodePathMap.put(root.getId(), rootPath);
                nodeDepthMap.put(root.getId(), 1);

                // 递归处理子节点
                cacheNodePathAndDepth(root, rootPath, nodePathMap, nodeDepthMap);
            }

            // 构建导出数据列表
            List<ProductModuleFunctionImportExcelVO> exportList = new ArrayList<>();
            int maxModuleLevel = 0;


            // 先计算最大层级深度（直接从缓存获取，无需重复计算路径）
            for (ProductModuleFunction function : allFunctions) {
                maxModuleLevel = Math.max(maxModuleLevel, nodeDepthMap.getOrDefault(function.getId(), 0));
            }

            // 转换数据并设置模块层级
            for (ProductModuleFunction function : allFunctions)
            {
                if (function.getNodeType() == 2) // 只处理功能类型的数据
                {
                    ProductModuleFunctionImportExcelVO vo = new ProductModuleFunctionImportExcelVO();
                    
                    // 设置基本信息
                    vo.setCode(function.getCode());
                    vo.setName(function.getName());
                    
                    // 设置所属系统
                    ProductInfo productInfo = (ProductInfo)function.getEchoMap().get("productId");
                    if (productInfo != null)
                    {
                        vo.setSystemName(productInfo.getName());
                    }
//                    // 设置交易类型和状态
                   // Dictionary tradeType = (Dictionary)function.getEchoMap().get("tradeType");

                    Object tradeType = function.getEchoMap().get("tradeType");
                    if (tradeType!=null && tradeType instanceof Map) {
                        Map<?, ?> tradeTypeMap = (Map<?, ?>) tradeType;
                        Object nameObj = tradeTypeMap.get("name");
                        if (nameObj instanceof String) {
                            vo.setType((String) nameObj);
                        }
                    }
//                    if (tradeType != null)
//                    {
//                         vo.setType(tradeType.getName());
//                    }
                    
                    vo.setStatus(function.getTradeStatus() == 0 ? "启用" : "禁用");
                    
                    // 设置执行方式（从字典中查询名称）
//                     Dictionary executionMethod = (Dictionary)function.getEchoMap().get("executionMethod");
//                    if (executionMethod != null){
//                        vo.setExecutionMode(executionMethod.getName());
//                    }
                    Object executionMethod = function.getEchoMap().get("executionMethod");
                    if (executionMethod!=null && executionMethod instanceof Map) {
                        Map<?, ?> executionMethodMap = (Map<?, ?>) executionMethod;
                        Object nameObj = executionMethodMap.get("name"); // 注意键的名称是否与实际一致（可能是"name"或其他）
                        if (nameObj instanceof String) {
                            vo.setExecutionMode((String) nameObj);
                        }
                    }
                    Object testManager = function.getEchoMap().get("testManager");
                    if (testManager!=null && testManager instanceof User) {
                        User testManagerMap = (User) testManager;
                        vo.setTestManager(testManagerMap.getName()); // 注意键的名称是否与实际一致（可能是"name"或其他）
                    }
                    vo.setDescription(function.getDescription());
                    
                    // 获取并设置模块层级
                    List<ProductModuleFunction> modulePath = nodePathMap.getOrDefault(function.getId(), new ArrayList<>());
                    
                    // 按顺序设置一到十级模块
                    if (modulePath.size() >= 1) {
                        vo.setFirstModule(modulePath.get(0).getName());
                    }
                    if (modulePath.size() >= 2) {
                        vo.setSecondModule(modulePath.get(1).getName());
                    }
                    if (modulePath.size() >= 3) {
                        vo.setThirdModule(modulePath.get(2).getName());
                    }
                    if (modulePath.size() >= 4) {
                        vo.setFourthModule(modulePath.get(3).getName());
                    }
                    if (modulePath.size() >= 5) {
                        vo.setFifthModule(modulePath.get(4).getName());
                    }
                    if (modulePath.size() >= 6) {
                        vo.setSixthModule(modulePath.get(5).getName());
                    }
                    if (modulePath.size() >= 7) {
                        vo.setSeventhModule(modulePath.get(6).getName());
                    }
                    if (modulePath.size() >= 8) {
                        vo.setEighthModule(modulePath.get(7).getName());
                    }
                    if (modulePath.size() >= 9) {
                        vo.setNinthModule(modulePath.get(8).getName());
                    }
                    if (modulePath.size() >= 10) {
                        vo.setTenthModule(modulePath.get(9).getName());
                    }
                    
                    exportList.add(vo);
                }
            }

            // 创建导出参数
            ExportParams exportParams = new ExportParams();
            exportParams.setTitle("系统模块交易数据");
            exportParams.setSheetName("功能列表");

            // 创建Workbook
            Workbook workbook;

            // 创建导出实体列表，完全通过entityList控制导出列
            List<ExcelExportEntity> entityList = new ArrayList<>();

            // 添加系统名称字段
            ExcelExportEntity systemNameEntity = new ExcelExportEntity("*所属系统", "systemName");
            systemNameEntity.setWidth(30);
            entityList.add(systemNameEntity);

            // 添加一级模块字段（默认总是添加）
            ExcelExportEntity firstModuleEntity = new ExcelExportEntity("一级模块", "firstModule");
            firstModuleEntity.setWidth(30);
            entityList.add(firstModuleEntity);

            // 根据maxModuleLevel的值动态添加其他模块列
            if (maxModuleLevel >= 2) {
                ExcelExportEntity secondModuleEntity = new ExcelExportEntity("二级模块", "secondModule");
                secondModuleEntity.setWidth(30);
                entityList.add(secondModuleEntity);
            }
            if (maxModuleLevel >= 3) {
                ExcelExportEntity thirdModuleEntity = new ExcelExportEntity("三级模块", "thirdModule");
                thirdModuleEntity.setWidth(30);
                entityList.add(thirdModuleEntity);
            }
            if (maxModuleLevel >= 4) {
                ExcelExportEntity fourthModuleEntity = new ExcelExportEntity("四级模块", "fourthModule");
                fourthModuleEntity.setWidth(30);
                entityList.add(fourthModuleEntity);
            }
            if (maxModuleLevel >= 5) {
                ExcelExportEntity fifthModuleEntity = new ExcelExportEntity("五级模块", "fifthModule");
                fifthModuleEntity.setWidth(30);
                entityList.add(fifthModuleEntity);
            }
            if (maxModuleLevel >= 6) {
                ExcelExportEntity sixthModuleEntity = new ExcelExportEntity("六级模块", "sixthModule");
                sixthModuleEntity.setWidth(30);
                entityList.add(sixthModuleEntity);
            }
            if (maxModuleLevel >= 7) {
                ExcelExportEntity seventhModuleEntity = new ExcelExportEntity("七级模块", "seventhModule");
                seventhModuleEntity.setWidth(30);
                entityList.add(seventhModuleEntity);
            }
            if (maxModuleLevel >= 8) {
                ExcelExportEntity eighthModuleEntity = new ExcelExportEntity("八级模块", "eighthModule");
                eighthModuleEntity.setWidth(30);
                entityList.add(eighthModuleEntity);
            }
            if (maxModuleLevel >= 9) {
                ExcelExportEntity ninthModuleEntity = new ExcelExportEntity("九级模块", "ninthModule");
                ninthModuleEntity.setWidth(30);
                entityList.add(ninthModuleEntity);
            }
            if (maxModuleLevel >= 10) {
                ExcelExportEntity tenthModuleEntity = new ExcelExportEntity("十级模块", "tenthModule");
                tenthModuleEntity.setWidth(30);
                entityList.add(tenthModuleEntity);
            }

            // 添加功能编码字段
            ExcelExportEntity codeEntity = new ExcelExportEntity("标识", "code");
            codeEntity.setWidth(15);
            entityList.add(codeEntity);

            // 添加功能名称字段
            ExcelExportEntity nameEntity = new ExcelExportEntity("功能名称", "name");
            nameEntity.setWidth(20);
            entityList.add(nameEntity);

            // 添加交易类型字段
            ExcelExportEntity typeEntity = new ExcelExportEntity("交易类型", "type");
            typeEntity.setWidth(20);
            entityList.add(typeEntity);

            // 添加交易状态字段
            ExcelExportEntity statusEntity = new ExcelExportEntity("交易状态", "status");
            statusEntity.setWidth(20);
            entityList.add(statusEntity);

            // 添加交易描述字段
            ExcelExportEntity descriptionEntity = new ExcelExportEntity("交易描述", "description");
            descriptionEntity.setWidth(100);
            entityList.add(descriptionEntity);

            // 添加执行方式字段
            ExcelExportEntity executionModeEntity = new ExcelExportEntity("执行方式", "executionMode");
            executionModeEntity.setWidth(20);
            entityList.add(executionModeEntity);

            // 添加归属测试经理字段
            ExcelExportEntity testManagerEntity = new ExcelExportEntity("归属测试经理", "testManager");
            testManagerEntity.setWidth(20);
            entityList.add(testManagerEntity);
            //按照系统 模块 排序
            exportList.sort(
                    // null排在前面（或用nullsLast()排在后面）
                    Comparator.comparing(ProductModuleFunctionImportExcelVO::getSystemName,
                                    Comparator.nullsFirst(String::compareTo))
                            // 处理后续每个字段的null值
                            .thenComparing(ProductModuleFunctionImportExcelVO::getFirstModule,
                                    Comparator.nullsFirst(String::compareTo))
                            .thenComparing(ProductModuleFunctionImportExcelVO::getSecondModule,
                                    Comparator.nullsFirst(String::compareTo))
                            .thenComparing(ProductModuleFunctionImportExcelVO::getThirdModule,
                                    Comparator.nullsFirst(String::compareTo))
                            .thenComparing(ProductModuleFunctionImportExcelVO::getFourthModule,
                                    Comparator.nullsFirst(String::compareTo))
                            .thenComparing(ProductModuleFunctionImportExcelVO::getFifthModule,
                                    Comparator.nullsFirst(String::compareTo))
                            .thenComparing(ProductModuleFunctionImportExcelVO::getSixthModule,
                                    Comparator.nullsFirst(String::compareTo))
                            .thenComparing(ProductModuleFunctionImportExcelVO::getSeventhModule,
                                    Comparator.nullsFirst(String::compareTo))
                            .thenComparing(ProductModuleFunctionImportExcelVO::getEighthModule,
                                    Comparator.nullsFirst(String::compareTo))
                            .thenComparing(ProductModuleFunctionImportExcelVO::getNinthModule,
                                    Comparator.nullsFirst(String::compareTo))
                            .thenComparing(ProductModuleFunctionImportExcelVO::getTenthModule,
                                    Comparator.nullsFirst(String::compareTo))
            );

            // 使用entityList控制导出列
            workbook = ExcelExportUtil.exportExcel(exportParams, entityList, exportList);
            
            // 导出Excel
            ExcelDownLoadUtil.export(response, workbook, "产品功能模块数据_" + System.currentTimeMillis() + ".xls");
        }
        catch (Exception e)
        {
            log.error("导出产品功能模块数据失败，原因:{}", e.getMessage(), e);
            throw new BizException("导出失败：" + e.getMessage());
        }
    }

    private void cacheNodePathAndDepth(ProductModuleFunction parentNode, List<ProductModuleFunction> parentPath,Map<Long, List<ProductModuleFunction>> nodePathMap,Map<Long, Integer> nodeDepthMap) {
        // 假设节点有getChildren()方法获取子节点列表（TreeUtil构建的树需包含此属性）
        List<ProductModuleFunction> children = parentNode.getChildren();
        if (CollectionUtil.isEmpty(children)) {
            return;
        }

        for (ProductModuleFunction child : children) {
            // 子节点的路径 = 父节点路径 + 自身
            List<ProductModuleFunction> childPath = new ArrayList<>(parentPath);
            childPath.add(child);

            // 缓存路径和深度（深度 = 父路径长度 + 1）
            nodePathMap.put(child.getId(), childPath);
            nodeDepthMap.put(child.getId(), childPath.size());

            // 递归处理子节点的子节点
            cacheNodePathAndDepth(child, childPath, nodePathMap, nodeDepthMap);
        }
    }

}
