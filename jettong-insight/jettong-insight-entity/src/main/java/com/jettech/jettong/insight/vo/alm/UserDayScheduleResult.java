package com.jettech.jettong.insight.vo.alm;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description TODO
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.insight.vo.alm
 * @className UserDayScheduleResult
 * @date 2025/9/17 12:01
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class UserDayScheduleResult
{

    @ApiModelProperty(value = "日期")
    private LocalDate day;

    private LocalDate startDay;

    private LocalDate endDay;

    private List<Info> infoList;

    @ApiModelProperty(value = "优先级code")
    private String priorityCode;

    @ApiModelProperty(value = "颜色")
    private String color;

    @Data
    public static class Info
    {

        @ApiModelProperty(value = "优先级code")
        private String priorityCode;

        @ApiModelProperty(value = "颜色")
        private String color;

        @ApiModelProperty(value = "名称")
        private String name;

        @ApiModelProperty(value = "项目id")
        private Long projectId;

        @ApiModelProperty(value = "测试需求id")
        private Long testreqId;
    }
}
