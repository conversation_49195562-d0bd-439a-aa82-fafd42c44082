package com.jettech.jettong.insight.vo.alm;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 人员排期
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.alm.issue.dto
 * @className Userschedule
 * @date 2025/9/17 11:50
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class UserScheduleResult
{

    @ApiModelProperty(value = "人员id")
    private Long userId;

    @ApiModelProperty(value = "人员名称")
    private String userName;

    @ApiModelProperty(value = "人员类型")
    private String userType;

    @ApiModelProperty(value = "团队")
    private String teamName;

    @ApiModelProperty(value = "排期")
    private List<UserDayScheduleResult> schedule;
}
