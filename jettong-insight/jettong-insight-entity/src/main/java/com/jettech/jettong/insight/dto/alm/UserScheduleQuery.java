package com.jettech.jettong.insight.dto.alm;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 人员排期查询参数
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.insight.dto.alm
 * @className UserScheduleQuery
 * @date 2025/9/17 14:43
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Data
public class UserScheduleQuery
{

    @ApiModelProperty(value = "开始时间")
    private LocalDate startDate;

    @ApiModelProperty(value = "结束时间")
    private LocalDate endDate;

    @ApiModelProperty(value = "人员ID")
    private List<Long> userIds;

    @ApiModelProperty(value = "团队ID")
    private List<Long> teamIds;

    @ApiModelProperty(value = "人员类型")
    private List<String> userTypes;
}
