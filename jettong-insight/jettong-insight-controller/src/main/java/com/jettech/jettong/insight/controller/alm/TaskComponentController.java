package com.jettech.jettong.insight.controller.alm;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.jettong.alm.api.ProjectApi;
import com.jettech.jettong.alm.issue.dto.*;
import com.jettech.jettong.alm.issue.entity.Task;
import com.jettech.basic.annotation.log.SysLog;
import com.jettech.basic.annotation.security.PreAuth;
import com.jettech.basic.base.R;
import com.jettech.basic.base.request.PageParams;
import com.jettech.basic.echo.core.EchoService;
import com.jettech.jettong.alm.api.PriorityApi;
import com.jettech.jettong.alm.issue.entity.State;
import com.jettech.jettong.insight.dto.alm.UserScheduleQuery;
import com.jettech.jettong.insight.service.alm.BugComponentService;
import com.jettech.jettong.insight.service.alm.RequirementComponentService;
import com.jettech.jettong.insight.service.alm.TaskComponentService;
import com.jettech.jettong.insight.service.alm.UserScheduleService;
import com.jettech.jettong.insight.vo.alm.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.jettech.jettong.common.constant.SwaggerConstants.DATA_TYPE_LONG;
import static com.jettech.jettong.common.constant.SwaggerConstants.PARAM_TYPE_PATH;

/**
 * 任务组件控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description 任务组件控制器
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.insight.controller.alm
 * @className TaskComponentController
 * @date 2021/12/2 14:12
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/insight/taskComponent")
@Api(value = "TaskComponentController", tags = "任务组件")
@RequiredArgsConstructor
@PreAuth(replace = "insight:taskComponent:")
public class TaskComponentController
{
    private final TaskComponentService teakComponentService;
    private final EchoService echoService;
    private final BugComponentService bugComponentService;
    private final RequirementComponentService requirementComponentService;
    private final PriorityApi priorityApi;
    private final ProjectApi projectApi;
    private final UserScheduleService userScheduleService;


    @ApiOperation(value = "查询项目任务概览组件", notes = "查询项目任务概览组件")
    @GetMapping("/findTypeByProjectId/{projectId}")
    @SysLog("查询项目任务概览组件")
    @PreAuth("hasAnyPermission('{}view')")
    public R<TaskTypeComponentResult> findTypeByProjectId(@PathVariable("projectId") Long projectId)
    {
        return R.success(teakComponentService.findTypeByProjectId(projectId));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "projectId", value = "项目id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @ApiOperation(value = "查询项目任务状态按人员分布组件", notes = "查询项目任务状态按人员分布组件")
    @GetMapping("/findStateByProjectId/{projectId}")
    @SysLog("查询项目任务状态按人员分布组件")
    @PreAuth("hasAnyPermission('{}view')")
    public R<TaskStateComponentResult> findStateByProjectId(@PathVariable("projectId") Long projectId)
    {
        return R.success(teakComponentService.findStateByProjectId(projectId));
    }

    @ApiOperation(value = "根据计划id查询任务概览数据", notes = "根据计划id查询任务概览数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "planId", value = "计划id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @GetMapping("/findTaskComponentByPlanId/{planId}")
    @SysLog("根据计划id查询任务概览数据")
    @PreAuth("hasAnyPermission('{}view')")
    public R<TaskTypeComponentResult> findTaskComponentByPlanId(@PathVariable("planId") Long planId)
    {
        return R.success(teakComponentService.findTaskComponentByPlanId(planId));
    }

    @ApiImplicitParams({
            @ApiImplicitParam(name = "planId", value = "计划id", dataType = DATA_TYPE_LONG,
                    paramType = PARAM_TYPE_PATH)
    })
    @ApiOperation(value = "查询计划任务状态按人员分布组件", notes = "查询计划任务状态按人员分布组件")
    @GetMapping("/findStateByPlanId/{planId}")
    @SysLog("查询计划任务状态按人员分布组件")
    @PreAuth("hasAnyPermission('{}view')")
    public R<TaskStateComponentResult> findStateByPlanId(@PathVariable("planId") Long planId)
    {
        return R.success(teakComponentService.findStateByPlanId(planId));
    }

    @ApiOperation(value = "查询任务统计组件", notes = "查询任务统计组件")
    @PostMapping("/findCountByQuery")
    @SysLog("查询任务统计组件")
    @PreAuth("hasAnyPermission('{}view')")
    public R<TaskCountComponentResult> findCountByQuery(@RequestBody TaskCountQuery query)
    {
        return R.success(teakComponentService.findCountByQuery(query));
    }

    @ApiOperation(value = "查询工时统计组件", notes = "查询工时统计组件")
    @PostMapping("/findCountHourByQuery")
    @SysLog("查询工时统计组件")
    @PreAuth("hasAnyPermission('{}view')")
    public R<IssueHourCountComponentResult> findHourCountByQuery(@RequestBody TaskCountQuery query)
    {
        return R.success(teakComponentService.findHourCountByQuery(query));
    }

    @ApiOperation(value = "查询需求统计组件-数据钻取-分页查询", notes = "查询需求统计组件-数据钻取-分页查询")
    @PostMapping("/findTaskByQuery/page")
    @SysLog("查询需求统计组件-数据钻取-分页查询")
    public IPage<Task> findTaskByQuery(@RequestBody
                                                   PageParams<TaskCountQuery> params) {
        IPage<Task> page = params.buildPage(Task.class);
        TaskCountQuery queryPage = params.getModel();

        teakComponentService.findTaskByQuery(page, queryPage);
        Map<Serializable, Object> projectApiByIds = new HashMap<>();
        if (page.getRecords() != null && page.getRecords().size() > 0) {
            page.getRecords().forEach(view -> view.setPutBy(view.getCreatedBy()));
            List<Long> collect = page.getRecords().stream().map(Task::getProjectId).collect(Collectors.toList());
            Set<Serializable> serializableSet = new HashSet<>(collect);
            projectApiByIds = projectApi.findByIds(serializableSet);
        }
        Map<Serializable, Object> finalProjectApiByIds = projectApiByIds;
        echoService.action(page);
        Map<String, State> stateCodeByCode = priorityApi.findAllStateCodeByCode("");
        page.getRecords().forEach(
                workItemDTO -> {
                    if (stateCodeByCode.get(workItemDTO.getStateCode()) != null) {
                        workItemDTO.getEchoMap().put("stateCode", stateCodeByCode.get(workItemDTO.getStateCode()));
                    }
                    if (finalProjectApiByIds != null && workItemDTO.getProjectId() != null && finalProjectApiByIds.get(workItemDTO.getProjectId().toString()) != null) {
                        workItemDTO.getEchoMap().put("projectInfo", finalProjectApiByIds.get(workItemDTO.getProjectId().toString()));
                    }
                }
        );

        return page;
    }


    @ApiOperation(value = "领导工作台查询统计组件", notes = "领导工作台查询统计组件")
    @PostMapping("/findLeaderCountGroupBy")
    @SysLog("领导工作台查询统计组件")
    @PreAuth("hasAnyPermission('{}view')")
    public R<List<LeaderComponentResult>> findLeaderCountGroupBy(@RequestBody TaskCountQuery query)
    {
        List<TaskCountComponentResult> taskCountComponentResults;
        if (CollUtil.isNotEmpty(query.getOrgIds()) && CollUtil.isEmpty(query.getUserIds()))
        {
            taskCountComponentResults = query.getOrgIds().parallelStream().map(orgId ->
            {
                TaskCountQuery taskCountQuery = new TaskCountQuery();
                taskCountQuery.setOrgIds(Collections.singletonList(orgId));
                taskCountQuery.setEndDate(query.getEndDate());
                taskCountQuery.setStartDate(query.getStartDate());
                TaskCountComponentResult componentResult =
                        teakComponentService.findLeaderCountByQuery(taskCountQuery);
                componentResult.setBizId(orgId);
                return componentResult;
            }).collect(Collectors.toList());
        }
        else
        {
            TaskCountQuery taskCountQuery = new TaskCountQuery();
            taskCountQuery.setEndDate(query.getEndDate());
            taskCountQuery.setStartDate(query.getStartDate());
            taskCountComponentResults = teakComponentService.findLeaderCountGroupBy(query);
        }
        List<BugCountComponentResult> bugCountComponentResults;
        if (CollUtil.isNotEmpty(query.getOrgIds()) && CollUtil.isEmpty(query.getUserIds()))
        {
            bugCountComponentResults = query.getOrgIds().parallelStream().map(orgId ->
            {
                BugComponentBaseQuery bugComponentBaseQuery = new BugComponentBaseQuery();
                bugComponentBaseQuery.setOrgIds(Collections.singletonList(orgId));
                PeriodQuery<LocalDate> date = new PeriodQuery<>();
                date.setStart(query.getStartDate());
                date.setEnd(query.getEndDate().plusDays(-1));
                bugComponentBaseQuery.setDate(date);
                BugCountComponentResult componentResult =
                        bugComponentService.findBugCountComponentByQuery(bugComponentBaseQuery);
                componentResult.setBizId(orgId);
                return componentResult;
            }).collect(Collectors.toList());
        }
        else
        {
            BugComponentBaseQuery bugComponentBaseQuery = new BugComponentBaseQuery();
            BeanUtil.copyProperties(query, bugComponentBaseQuery);
            PeriodQuery<LocalDate> date = new PeriodQuery<>();
            date.setStart(query.getStartDate());
            date.setEnd(query.getEndDate().plusDays(-1));
            bugComponentBaseQuery.setDate(date);
            bugCountComponentResults = bugComponentService.findBugCountComponentGroupBy(bugComponentBaseQuery);
        }
        List<RequirementCountComponentResult> requirementCountComponentResults;
        if (CollUtil.isNotEmpty(query.getOrgIds()) && CollUtil.isEmpty(query.getUserIds()))
        {
            requirementCountComponentResults = query.getOrgIds().parallelStream().map(orgId ->
            {
                RequirementComponentBaseQuery requirementComponentBaseQuery = new RequirementComponentBaseQuery();
                requirementComponentBaseQuery.setOrgIds(Collections.singletonList(orgId));
                PeriodQuery<LocalDate> date = new PeriodQuery<>();
                date.setStart(query.getStartDate());
                date.setEnd(query.getEndDate().plusDays(-1));
                requirementComponentBaseQuery.setDate(date);
                RequirementCountComponentResult componentResult =
                        requirementComponentService.findRequirementCountComponentByQuery(requirementComponentBaseQuery);
                componentResult.setBizId(orgId);
                return componentResult;
            }).collect(Collectors.toList());
        }
        else
        {
            RequirementComponentBaseQuery requirementComponentBaseQuery = new RequirementComponentBaseQuery();
            BeanUtil.copyProperties(query, requirementComponentBaseQuery);
            PeriodQuery<LocalDate> date = new PeriodQuery<>();
            date.setStart(query.getStartDate());
            date.setEnd(query.getEndDate().plusDays(-1));
            requirementComponentBaseQuery.setDate(date);
            requirementCountComponentResults =
                    requirementComponentService.findRequirementCountComponentGroupBy(requirementComponentBaseQuery);
        }
        List<IssueHourCountComponentResult> issueHourCountComponentResults;
        if (CollUtil.isNotEmpty(query.getOrgIds()) && CollUtil.isEmpty(query.getUserIds()))
        {
            issueHourCountComponentResults = query.getOrgIds().parallelStream().map(orgId ->
            {
                TaskCountQuery taskCountQuery = new TaskCountQuery();
                taskCountQuery.setOrgIds(Collections.singletonList(orgId));
                IssueHourCountComponentResult componentResult =
                        teakComponentService.findHourCountByQuery(taskCountQuery);
                componentResult.setBizId(orgId);
                return componentResult;
            }).collect(Collectors.toList());

        }
        else
        {
            issueHourCountComponentResults = teakComponentService.findHourCountGroupBy(query);
        }
        Map<Long, BugCountComponentResult> bugMap = bugCountComponentResults.stream()
                .collect(Collectors.toMap(BugCountComponentResult::getBizId, Function.identity()));
        Map<Long, RequirementCountComponentResult> requirementMap = requirementCountComponentResults.stream()
                .collect(Collectors.toMap(RequirementCountComponentResult::getBizId, Function.identity()));
        Map<Long, IssueHourCountComponentResult> issueHourMap = issueHourCountComponentResults.stream()
                .collect(Collectors.toMap(IssueHourCountComponentResult::getBizId, Function.identity()));
        List<LeaderComponentResult> leaderComponentResults = taskCountComponentResults.stream().map(task ->
        {
            LeaderComponentResult leaderComponentResult = new LeaderComponentResult();
            leaderComponentResult.setBizId(task.getBizId());
            leaderComponentResult.setTaskResult(
                    Arrays.asList(new LeaderComponentResult.Pie("未开始", task.getTodoNum()),
                            new LeaderComponentResult.Pie("进行中", task.getProcessingNum()),
                            new LeaderComponentResult.Pie("已完成", task.getCompleteNum())));
            BugCountComponentResult bug = bugMap.get(task.getBizId());
            leaderComponentResult.setBugResult(
                    Arrays.asList(new LeaderComponentResult.Pie("待修复", bug.getTodoNum()),
                            new LeaderComponentResult.Pie("修复中", bug.getProcessingNum()),
                            new LeaderComponentResult.Pie("已修复", bug.getCompleteNum())));
            RequirementCountComponentResult requirement = requirementMap.get(task.getBizId());
            leaderComponentResult.setRequirementResult(
                    Arrays.asList(new LeaderComponentResult.Pie("待处理", requirement.getTodoNum()),
                            new LeaderComponentResult.Pie("处理中", requirement.getProcessingNum()),
                            new LeaderComponentResult.Pie("已完成", requirement.getCompleteNum())));
            IssueHourCountComponentResult issueHour = issueHourMap.get(task.getBizId());
            leaderComponentResult.setIssueHourResult(
                    Arrays.asList(new LeaderComponentResult.Pie("预估", issueHour.getEstimateHour()),
                            new LeaderComponentResult.Pie("登记", issueHour.getFilledHour()),
                            new LeaderComponentResult.Pie("完成", issueHour.getCompleteHour())));
            return leaderComponentResult;
        }).collect(Collectors.toList());
        return R.success(leaderComponentResults);
    }

    @ApiOperation(value = "领导工作台查询工时统计组件", notes = "领导工作台查询工时统计组件")
    @PostMapping("/findCountHourGroupBy")
    @SysLog("领导工作台查询工时统计组件")
    @PreAuth("hasAnyPermission('{}view')")
    public R<List<IssueHourCountComponentResult>> findCountHourGroupBy(@RequestBody TaskCountQuery query)
    {
        if (CollUtil.isNotEmpty(query.getOrgIds()))
        {
            List<IssueHourCountComponentResult> results = query.getOrgIds().parallelStream().map(orgId ->
            {
                TaskCountQuery taskCountQuery = new TaskCountQuery();
                taskCountQuery.setOrgIds(Collections.singletonList(orgId));
                IssueHourCountComponentResult componentResult =
                        teakComponentService.findHourCountByQuery(taskCountQuery);
                componentResult.setBizId(orgId);
                return componentResult;
            }).collect(Collectors.toList());
            return R.success(results);
        }
        else
        {
            return R.success(teakComponentService.findHourCountGroupBy(query));
        }
    }

    @ApiOperation(value = "领导工作台查询工作项组件", notes = "领导工作台查询工作项组件")
    @PostMapping("/queryWorkItemPageByQuery")
    @SysLog("领导工作台查询工作项组件")
    @PreAuth("hasAnyPermission('{}view')")
    public R<IPage<WorkItemDTO>> queryWorkItemPageByQuery(@RequestBody PageParams<TaskCountQuery> query)
    {
        IPage<WorkItemDTO> page =
                teakComponentService.queryWorkItemPageByQuery(query.buildPage(), query.getModel());
        if (CollUtil.isNotEmpty(page.getRecords()))
        {
            echoService.action(page.getRecords());
            Map<String, State> stateCodeByCode = priorityApi.findAllStateCodeByCode("");
            page.getRecords().forEach(
                    workItemDTO -> {
                        if(stateCodeByCode.get(workItemDTO.getStateCode())!=null){
                            workItemDTO.getEchoMap().put("stateCode",stateCodeByCode.get(workItemDTO.getStateCode()));
                        }
                    }
            );
        }
        return R.success(page);
    }

    @ApiOperation(value = "领导工作台查询所有工作项组件", notes = "领导工作台查询所有工作项组件")
    @PostMapping("/queryWorkItem")
    @SysLog("领导工作台查询所有工作项组件")
    @PreAuth("hasAnyPermission('{}view')")
    public R<List<Map<String,Object>>> queryWorkItem(@RequestBody TaskCountQuery query)
    {
        List<WorkItemDTO> workItemDTOS = teakComponentService.queryWorkItem(query);
        if (CollUtil.isNotEmpty(workItemDTOS))
        {
            echoService.action(workItemDTOS);
            Map<LocalDate, List<WorkItemDTO>> result = new HashMap<>();
            workItemDTOS.forEach(workItemDTO ->
            {
                if (workItemDTO.getPlanStime() == null&&workItemDTO.getPlanEtime()!=null)
                {
                    LocalDate localDate = workItemDTO.getPlanEtime().toLocalDate();
                    List<WorkItemDTO> list = result.get(localDate);
                    if (list == null)
                    {
                        list = new ArrayList<>();
                        result.put(localDate, list);
                    }
                    list.add(workItemDTO);
                }
                else if (workItemDTO.getPlanStime() != null&&workItemDTO.getPlanEtime()==null){
                    LocalDate localDate = workItemDTO.getPlanStime().toLocalDate();
                    List<WorkItemDTO> list = result.get(localDate);
                    if (list == null)
                    {
                        list = new ArrayList<>();
                        result.put(localDate, list);
                    }
                    list.add(workItemDTO);
                }
                else if (workItemDTO.getPlanStime() != null&&workItemDTO.getPlanEtime()!=null)
                {
                    LocalDate start = workItemDTO.getPlanStime().toLocalDate();
                    while (start.isBefore(workItemDTO.getPlanEtime().toLocalDate().plusDays(1)))
                    {
                        List<WorkItemDTO> list = result.get(start);
                        if (list == null)
                        {
                            list = new ArrayList<>();
                            result.put(start, list);
                        }
                        list.add(workItemDTO);
                        start = start.plusDays(1);
                    }
                }
            });
            List<Map<String,Object>> end = new ArrayList<>();
            result.forEach((k, v) ->{
                Map<String,Object> map = new HashMap<>();
                map.put("date",k);
                map.put("items",v);
                end.add(map);
            });
            return R.success(end);
        }
        return R.success(null);
    }

    @ApiOperation("人员排期")
    @GetMapping("/userSchedule")
    public R<List<UserScheduleResult>> userSchedule(@ModelAttribute UserScheduleQuery query) {
        List<UserScheduleResult> userSchedules = userScheduleService.getUserSchedules(query);
        return R.success(userSchedules.stream()
                .map(userScheduleService::processUserSchedule)
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));
    }

    @ApiOperation("个人排期")
    @GetMapping("/userSchedule/{userId}")
    public R<List<UserDayScheduleResult>> userSchedule(@RequestParam LocalDate startDate, @RequestParam LocalDate endDate, @PathVariable Long userId) {
        UserScheduleQuery query = new UserScheduleQuery();
        query.setStartDate(startDate);
        query.setEndDate(endDate);
        query.setUserIds(Arrays.asList(userId));
        List<UserScheduleResult> userSchedules = userScheduleService.getUserSchedules(query);
        if (CollUtil.isEmpty(userSchedules)) {
            return R.success(null);
        }
        return R.success(userSchedules.get(0).getSchedule());
    }

}
