<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jettech.jettong.insight.dao.base.UserMapper">

    <select id="selectIdByOrgIds" resultType="java.lang.Long">
        select `id`
        from `sys_user`
        where `org_id` in (select `child_id` from `sys_org_child` where `parent_id` in
        <foreach close=")" collection="orgIds" item="orgId" open="(" separator=",">
            #{orgId}
        </foreach>
        )
        or `org_id` in
        <foreach close=")" collection="orgIds" item="orgId" open="(" separator=",">
            #{orgId}
        </foreach>
    </select>
    <select id="selectCountByOrgId" resultType="java.lang.Long">
        select count(id)
        from `sys_user`
        where `org_id` in (select `child_id` from `sys_org_child` where `parent_id` = #{orgId})
            or org_id = #{orgId}
    </select>

    <select id="findPage" resultType="com.jettech.jettong.insight.vo.base.UserComponentResult">
        select *
        from sys_user
        <where>
            <if test="user.orgId != null">
                and `org_id` in (select `child_id` from `sys_org_child` where `parent_id` = #{user.orgId})
                or org_id = #{user.orgId}
            </if>
        </where>

    </select>
    <select id="findSchedule" resultType="com.jettech.jettong.insight.vo.alm.UserScheduleResult"
            parameterType="com.jettech.jettong.insight.dto.alm.UserScheduleQuery">
        select distinct u.id as userId,u.name as userName,di.name as userType,t.name as teamName from
         sys_user u left join sys_team_user tu on u.id = tu.user_id left join sys_team t on tu.team_id = t.id
                                     left join sys_dictionary di on u.type = di.code
        where 1=1
        <if test="query.userIds != null and query.userIds.size() > 0">
        and u.id in
        <foreach close=")" collection="query.userIds" item="userId" open="(" separator=",">
            #{userId}
        </foreach>
        </if>
        <if test="query.teamIds != null and query.teamIds.size() > 0">
        and t.id in
        <foreach close=")" collection="query.teamIds" item="teamId" open="(" separator=",">
            #{teamId}
        </foreach>
        </if>
        <if test="query.userTypes != null and query.userTypes.size() > 0">
        and u.type in
        <foreach close=")" collection="query.userTypes" item="userType" open="(" separator=",">
            #{userType}
        </foreach>
        </if>

    </select>


</mapper>
