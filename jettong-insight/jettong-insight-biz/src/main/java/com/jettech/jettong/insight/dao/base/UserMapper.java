package com.jettech.jettong.insight.dao.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.base.mapper.SuperMapper;
import com.jettech.jettong.base.dto.rbac.user.UserPageQuery;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.insight.dto.alm.UserScheduleQuery;
import com.jettech.jettong.insight.vo.alm.UserScheduleResult;
import com.jettech.jettong.insight.vo.base.UserComponentResult;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * 用户持久化层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 用户持久化层接口
 * @projectName jettong
 * @package com.jettech.jettong.insight.dao.base
 * @className UserMapper
 * @date 2021/9/15 9:43
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Repository
public interface UserMapper extends SuperMapper<User>
{
    /**
     * 根据机构id，查询该机构下所有用户id,包含子机构用户
     * @param orgIds 机构id集合
     * @return List<Long> 用户id
     * <AUTHOR>
     * @date 2024/5/8 10:40
     * @update 2024/5/8 10:40
     * @since 1.0
     */
    List<Long> selectIdByOrgIds(@Param("orgIds") Collection<?> orgIds);

    long  selectCountByOrgId(@Param("orgId") Long orgId);

    IPage<UserComponentResult> findPage(@Param("page") IPage<UserComponentResult> page, @Param("user") UserPageQuery user);

    List<UserScheduleResult> findSchedule(@Param("query") UserScheduleQuery query);
}
