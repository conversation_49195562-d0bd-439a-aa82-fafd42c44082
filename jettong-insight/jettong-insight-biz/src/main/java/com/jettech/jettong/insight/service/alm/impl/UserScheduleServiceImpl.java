package com.jettech.jettong.insight.service.alm.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jettech.jettong.alm.issue.entity.Priority;
import com.jettech.jettong.alm.issue.entity.Task;
import com.jettech.jettong.alm.issue.entity.TaskUser;
import com.jettech.jettong.alm.issue.entity.Testreq;
import com.jettech.jettong.alm.issue.enumeration.ItemUserType;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.insight.dao.alm.*;
import com.jettech.jettong.insight.dao.base.UserMapper;
import com.jettech.jettong.insight.dto.alm.UserScheduleQuery;
import com.jettech.jettong.insight.service.alm.UserScheduleService;
import com.jettech.jettong.insight.vo.alm.UserDayScheduleResult;
import com.jettech.jettong.insight.vo.alm.UserScheduleResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 人员排期服务实现（优化优先级逻辑：任务自身 > 测试需求 > 项目）
 */
@Service
@RequiredArgsConstructor
public class UserScheduleServiceImpl implements UserScheduleService {
    private final TaskMapper taskMapper;
    private final TaskUserMapper taskUserMapper;
    private final TestreqMapper testreqMapper;
    private final ProjectInfoMapper projectInfoMapper;
    private final UserMapper userMapper;
    private final PriorityMapper priorityMapper;

    @Override
    public List<UserScheduleResult> getUserSchedules(UserScheduleQuery query) {
        // 1. 筛选符合条件的用户
        List<UserScheduleResult> userList = userMapper.findSchedule(query);
        if (CollectionUtils.isEmpty(userList)) {
            return Collections.emptyList();
        }

        // 2. 转换日期为时间范围
        LocalDateTime startTime = LocalDateTime.of(query.getStartDate(), LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(query.getEndDate(), LocalTime.MAX);

        Set<Long> userIds = userList.stream().map(UserScheduleResult::getUserId).collect(Collectors.toSet());
        // 3. 查询用户相关任务（包含直接关联和TaskUser关联）
        List<Task> tasks = queryTasksByUsersAndTimeRange(userIds, startTime, endTime);

        // 4. 批量查询关联数据（减少N+1查询）
        Map<Long, Testreq> testreqMap = getTestreqMap(tasks);
        Map<Long, ProjectInfo> projectMap = getProjectMap(tasks);
        Map<String, Priority> priorityMap = getPriorityMap();

        // 5. 为任务补充处理人列表（含TaskUser关联的处理人）
        fillTaskHandleUsers(tasks);

        // 6. 按用户分组任务
        Map<Long, List<Task>> userTasksMap = groupTasksByUser(tasks, userIds);

        // 7. 获取用户信息
        Map<Long, UserScheduleResult> userMap = userList.stream().collect(Collectors.toMap(UserScheduleResult::getUserId, user->user,(k1,k2)->k2));

        // 8. 构建排期结果（如果没有任务，返回空排期）
        if (CollectionUtils.isEmpty(userTasksMap)) {
            return buildEmptyUserSchedules(userMap, query.getStartDate(), query.getEndDate());
        }

        // 9. 构建排期结果（使用缓存的关联数据）
        return buildUserScheduleResults(userTasksMap, userMap, query.getStartDate(), query.getEndDate(),
                testreqMap, projectMap, priorityMap);
    }

    /**
     * 处理单个用户的排期数据  合并时间范围
     */
    public UserScheduleResult processUserSchedule(UserScheduleResult original) {
        if (original == null || CollUtil.isEmpty(original.getSchedule())) {
            return null;
        }

        // 过滤infoList为空的记录
        List<UserDayScheduleResult> filteredSchedules = original.getSchedule().stream()
                .filter(day -> !CollUtil.isEmpty(day.getInfoList()))
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(filteredSchedules)) {
            return null; // 所有日期都没有任务，返回null
        }

        // 合并相同颜色的连续记录
        List<UserDayScheduleResult> mergedSchedules = mergeSchedulesByColor(filteredSchedules);

        original.setSchedule(mergedSchedules);

        return original;
    }

    /**
     * 按颜色合并连续的排期记录
     */
    private List<UserDayScheduleResult> mergeSchedulesByColor(List<UserDayScheduleResult> schedules) {
        if (CollUtil.isEmpty(schedules)) {
            return Collections.emptyList();
        }

        List<UserDayScheduleResult> merged = new ArrayList<>();
        // 按日期排序
        List<UserDayScheduleResult> sorted = schedules.stream()
                .sorted(Comparator.comparing(UserDayScheduleResult::getDay))
                .collect(Collectors.toList());

        // 初始化第一个合并组
        UserDayScheduleResult currentGroup = sorted.get(0);
        LocalDate startDate = currentGroup.getDay();
        LocalDate endDate = currentGroup.getDay();
        String currentColor = currentGroup.getColor();

        // 遍历剩余记录进行合并
        for (int i = 1; i < sorted.size(); i++) {
            UserDayScheduleResult current = sorted.get(i);
            // 检查是否颜色相同且日期连续
            if (current.getColor().equals(currentColor) &&
                    current.getDay().equals(endDate.plusDays(1))) {
                // 扩展当前合并组
                endDate = current.getDay();
            } else {
                // 保存当前合并组并开始新组
                merged.add(createMergedSchedule(startDate, endDate, currentColor, currentGroup.getInfoList()));
                currentGroup = current;
                startDate = current.getDay();
                endDate = current.getDay();
                currentColor = current.getColor();
            }
        }

        // 添加最后一个合并组
        merged.add(createMergedSchedule(startDate, endDate, currentColor, currentGroup.getInfoList()));

        return merged;
    }

    /**
     * 创建合并后的排期记录
     */
    private UserDayScheduleResult createMergedSchedule(LocalDate startDate, LocalDate endDate,
            String color, List<UserDayScheduleResult.Info> infoList) {
        UserDayScheduleResult merged = new UserDayScheduleResult();
        merged.setStartDay(startDate);      // 需要在VO中添加startDay字段
        merged.setEndDay(endDate);          // 需要在VO中添加endDay字段
        merged.setColor(color);
        merged.setInfoList(infoList);       // 保留原始信息列表，也可根据需求合并

        // 如果只需要保留合并后的时间范围和颜色，可注释掉上面的infoList设置
        // merged.setInfoList(null);

        return merged;
    }

    /**
     * 查询指定用户在时间范围内的任务（包含直接关联和TaskUser关联）
     */
    private List<Task> queryTasksByUsersAndTimeRange(Set<Long> userIds, LocalDateTime startTime, LocalDateTime endTime) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        // 1. 查询直接关联任务（Task.handleBy = 用户ID）
        LambdaQueryWrapper<Task> directQuery = new LambdaQueryWrapper<>();
        directQuery.and(wrapper -> wrapper
                        .between(Task::getPlanStime, startTime, endTime)
                        .or()
                        .between(Task::getPlanEtime, startTime, endTime)
                ).eq(Task::getIsFiled, false)
                .exists("select 1 from issue_task_user stu where stu.task_id = issue_task.id and type='HANDLE' and stu.user_id in ( "+StrUtil.join(StrUtil.COMMA,userIds)+" ) ");
        return taskMapper.selectList(directQuery);
    }

    /**
     * 批量获取测试需求映射（缓存用）
     */
    private Map<Long, Testreq> getTestreqMap(List<Task> tasks) {
        Set<Long> testreqIds = tasks.stream()
                .map(Task::getTestreqId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(testreqIds)) {
            return Collections.emptyMap();
        }

        return testreqMapper.selectBatchIds(testreqIds).stream()
                .collect(Collectors.toMap(Testreq::getId, testreq -> testreq));
    }

    /**
     * 批量获取项目映射（缓存用）
     */
    private Map<Long, ProjectInfo> getProjectMap(List<Task> tasks) {
        Set<Long> projectIds = tasks.stream()
                .map(Task::getProjectId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(projectIds)) {
            return Collections.emptyMap();
        }

        return projectInfoMapper.selectBatchIds(projectIds).stream()
                .collect(Collectors.toMap(ProjectInfo::getId, project -> project));
    }

    /**
     * 获取优先级映射（包含颜色配置）
     */
    private Map<String, Priority> getPriorityMap() {
        return priorityMapper.selectList(null).stream()
                .collect(Collectors.toMap(Priority::getCode, priority -> priority));
    }

    /**
     * 为任务补充处理人列表（含直接处理人和TaskUser关联的处理人）
     */
    private void fillTaskHandleUsers(List<Task> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }

        Set<Long> taskIds = tasks.stream()
                .map(Task::getId)
                .collect(Collectors.toSet());

        // 查询所有任务关联的处理人
        LambdaQueryWrapper<TaskUser> query = new LambdaQueryWrapper<>();
        query.in(TaskUser::getTaskId, taskIds)
                .eq(TaskUser::getType, ItemUserType.HANDLE);
        List<TaskUser> taskUsers = taskUserMapper.selectList(query);

        // 按任务ID分组处理人
        Map<Long, List<Long>> taskHandleUsers = taskUsers.stream().collect(Collectors.groupingBy(TaskUser::getTaskId, Collectors.mapping(TaskUser::getUserId, Collectors.toList())));

        // 为每个任务设置完整处理人列表
        tasks.forEach(task -> {
            List<Long> handleUsers = new ArrayList<>();
            // 添加直接处理人
            if (task.getHandleBy() != null) {
                handleUsers.add(task.getHandleBy());
            }
            // 添加关联处理人
            handleUsers.addAll(taskHandleUsers.getOrDefault(task.getId(), Collections.emptyList()));
            // 去重后设置
            task.setHandleByList(handleUsers.stream().distinct().collect(Collectors.toList()));
        });
    }

    /**
     * 按用户分组任务（基于处理人列表）
     */
    private Map<Long, List<Task>> groupTasksByUser(List<Task> tasks, Set<Long> userIds) {
        Map<Long, List<Task>> userTasksMap = new HashMap<>();
        if (CollectionUtils.isEmpty(tasks) || CollectionUtils.isEmpty(userIds)) {
            return userTasksMap;
        }

        userIds.forEach(userId -> {
            List<Task> userTasks = tasks.stream()
                    .filter(task -> !CollectionUtils.isEmpty(task.getHandleByList())
                            && task.getHandleByList().contains(userId))
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(userTasks)) {
                userTasksMap.put(userId, userTasks);
            }
        });
        return userTasksMap;
    }

    /**
     * 构建用户排期结果
     */
    private List<UserScheduleResult> buildUserScheduleResults(
            Map<Long, List<Task>> userTasksMap,
            Map<Long, UserScheduleResult> userMap,
            LocalDate startDate,
            LocalDate endDate,
            Map<Long, Testreq> testreqMap,
            Map<Long, ProjectInfo> projectMap,
            Map<String, Priority> priorityMap) {
        return userTasksMap.entrySet().stream()
                .map(entry -> {
                    Long userId = entry.getKey();
                    List<Task> tasks = entry.getValue();
                    UserScheduleResult result = userMap.get(userId); // 假设团队信息存在于orgName字段
                    result.setSchedule(buildDaySchedules(tasks, startDate, endDate, testreqMap, projectMap, priorityMap));
                    return result;
                })
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(UserScheduleResult::getUserName))
                .collect(Collectors.toList());
    }

    /**
     * 构建每日排期
     */
    private List<UserDayScheduleResult> buildDaySchedules(
            List<Task> tasks,
            LocalDate startDate,
            LocalDate endDate,
            Map<Long, Testreq> testreqMap,
            Map<Long, ProjectInfo> projectMap,
            Map<String, Priority> priorityMap) {

        List<UserDayScheduleResult> daySchedules = new ArrayList<>();
        LocalDate currentDate = startDate;

        while (!currentDate.isAfter(endDate)) {
            UserDayScheduleResult daySchedule = new UserDayScheduleResult();
            daySchedule.setDay(currentDate);

            List<Task> dailyTasks = getTasksForDay(tasks, currentDate);
            if (!CollectionUtils.isEmpty(dailyTasks)) {
                List<UserDayScheduleResult.Info> infoList = buildTaskInfoList(dailyTasks, testreqMap, projectMap, priorityMap);
                daySchedule.setInfoList(infoList);
                determineHighestPriority(daySchedule, infoList, priorityMap);
            }

            daySchedules.add(daySchedule);
            currentDate = currentDate.plusDays(1);
        }

        return daySchedules;
    }

    /**
     * 获取指定日期的任务
     */
    private List<Task> getTasksForDay(List<Task> tasks, LocalDate date) {
        LocalDateTime dayStart = LocalDateTime.of(date, LocalTime.MIN);
        LocalDateTime dayEnd = LocalDateTime.of(date, LocalTime.MAX);

        return tasks.stream()
                .filter(task -> {
                    LocalDateTime planStime = task.getPlanStime();
                    LocalDateTime planEtime = task.getPlanEtime();
                    if (planStime == null || planEtime == null) {
                        return false;
                    }
                    // 任务时间范围与当天有交集
                    return !planStime.isAfter(dayEnd) && !planEtime.isBefore(dayStart);
                })
                .collect(Collectors.toList());
    }

    /**
     * 构建任务信息列表（使用缓存的关联数据）
     */
    private List<UserDayScheduleResult.Info> buildTaskInfoList(
            List<Task> tasks,
            Map<Long, Testreq> testreqMap,
            Map<Long, ProjectInfo> projectMap,
            Map<String, Priority> priorityMap) {

        List<Long> projectIds = new ArrayList<>();
        List<Long> testreqIds = new ArrayList<>();
        return tasks.stream().map(task -> {
            UserDayScheduleResult.Info info = new UserDayScheduleResult.Info();
            info.setProjectId(task.getProjectId());
            if (task.getTestreqId()!=null&&!testreqIds.contains(task.getTestreqId())){
                testreqIds.add(task.getTestreqId());
                info.setTestreqId(task.getTestreqId());
                Testreq testreq = testreqMap.get(task.getTestreqId());
                if (testreq==null||testreq.getPriorityCode()==null){
                    return null;
                }
                info.setPriorityCode(testreq.getPriorityCode());
                info.setColor(priorityMap.get(testreq.getPriorityCode()).getColor());
                info.setName(testreq.getName());
            }else {
                projectIds.add(task.getProjectId());
                ProjectInfo projectInfo = projectMap.get(task.getProjectId());
                if (projectInfo==null||projectInfo.getPriorityCode()==null||projectIds.contains(task.getProjectId())){
                    return null;
                }
                info.setPriorityCode(projectInfo.getPriorityCode());
                info.setColor(priorityMap.get(projectInfo.getPriorityCode()).getColor());
                info.setName(projectInfo.getName());
            }
            return info;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }


    /**
     * 确定当天最高优先级及颜色
     */
    private void determineHighestPriority(
            UserDayScheduleResult daySchedule,
            List<UserDayScheduleResult.Info> infoList,
            Map<String, Priority> priorityMap) {

        // 从数据库优先级配置中获取排序权重（避免硬编码）
        Map<String, Integer> priorityOrder = priorityMap.values().stream()
                .collect(Collectors.toMap(
                        Priority::getCode,
                        priority -> priority.getSort() != null ? priority.getSort() : 0
                ));

        // 找到最高优先级（排序值越大优先级越高）
        String highestPriority = infoList.stream()
                .map(UserDayScheduleResult.Info::getPriorityCode)
                .max(Comparator.comparingInt(code -> priorityOrder.getOrDefault(code, 0)))
                .orElse("MEDIUM");

        // 设置最高优先级及对应的颜色
        daySchedule.setPriorityCode(highestPriority);
        Priority highestPriorityObj = priorityMap.get(highestPriority);
        daySchedule.setColor(highestPriorityObj != null ? highestPriorityObj.getColor() : "#8C8C8C");
    }

    /**
     * 为没有任务的用户构建空排期
     */
    private List<UserScheduleResult> buildEmptyUserSchedules(Map<Long, UserScheduleResult> userMap, LocalDate startDate, LocalDate endDate) {

        userMap.forEach( (userId, userSchedule) -> {
                    List<UserDayScheduleResult> emptySchedules = new ArrayList<>();
                    LocalDate currentDate = startDate;
                    while (!currentDate.isAfter(endDate)) {
                        UserDayScheduleResult dayResult = new UserDayScheduleResult();
                        dayResult.setDay(currentDate);
                        emptySchedules.add(dayResult);
                        currentDate = currentDate.plusDays(1);
                    }
                    userSchedule.setSchedule(emptySchedules);
                });
        return userMap.values().stream().filter(Objects::nonNull)
                .sorted(Comparator.comparing(UserScheduleResult::getUserName))
                .collect(Collectors.toList());
    }


}
