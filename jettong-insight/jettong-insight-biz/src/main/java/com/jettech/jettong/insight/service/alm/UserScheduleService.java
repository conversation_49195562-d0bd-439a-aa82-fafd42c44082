package com.jettech.jettong.insight.service.alm;


import com.jettech.jettong.insight.dto.alm.UserScheduleQuery;
import com.jettech.jettong.insight.vo.alm.UserScheduleResult;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 人员排期
 * @projectName jettong-base-cloud
 * @package com.jettech.jettong.insight.service.alm
 * @className UserScheduleService
 * @date 2025/9/17 14:53
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface UserScheduleService
{


    List<UserScheduleResult> getUserSchedules(UserScheduleQuery query);

    UserScheduleResult processUserSchedule(UserScheduleResult original);
}
