package com.jettech.jettong.insight.service.alm.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.QueryWrap;
import com.jettech.basic.exception.BizException;
import com.jettech.jettong.alm.issue.dto.TaskCountQuery;
import com.jettech.jettong.alm.issue.dto.WorkItemDTO;
import com.jettech.jettong.alm.issue.entity.State;
import com.jettech.jettong.alm.issue.entity.Task;
import com.jettech.jettong.alm.issue.entity.TaskUser;
import com.jettech.jettong.alm.issue.enumeration.ItemUserType;
import com.jettech.jettong.alm.issue.enumeration.TypeClassify;
import com.jettech.jettong.alm.project.entity.ProjectPlan;
import com.jettech.jettong.alm.project.entity.ProjectUser;
import com.jettech.jettong.alm.project.entity.ProjectWorkflow;
import com.jettech.jettong.alm.workflow.entity.WorkflowNode;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.insight.dao.alm.*;
import com.jettech.jettong.insight.dao.base.UserMapper;
import com.jettech.jettong.insight.service.alm.ProjectWorkflowService;
import com.jettech.jettong.insight.service.alm.TaskComponentService;
import com.jettech.jettong.insight.vo.alm.IssueHourCountComponentResult;
import com.jettech.jettong.insight.vo.alm.TaskCountComponentResult;
import com.jettech.jettong.insight.vo.alm.TaskStateComponentResult;
import com.jettech.jettong.insight.vo.alm.TaskTypeComponentResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 任务组件业务处理层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 任务组件业务处理层
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.insight.service.alm.impl
 * @className TaskComponentServiceImpl
 * @date 2021/12/2 17:58
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
@RequiredArgsConstructor
public class TaskComponentServiceImpl implements TaskComponentService
{
    private final TaskMapper taskMapper;

    private final StateMapper stateMapper;

    private final UserMapper userMapper;

    private final ProjectUserMapper projectUserMapper;

    private final ProjectPlanMapper projectPlanMapper;

    private final WorkflowNodeMapper workflowNodeMapper;

    private final ProjectWorkflowService projectWorkflowService;

    private final ProductInfoMapper productInfoMapper;

    private final TaskUserMapper taskUserMapper;

    @Override
    public TaskTypeComponentResult findTypeByProjectId(Long projectId)
    {
        // 根据项目id查询所有任务
        List<Task> tasks = taskMapper.selectList(Wraps.<Task>lbQ().eq(Task::getProjectId, projectId));

        // 查询所有状态
        List<State> states = stateMapper.selectList(Wraps.<State>lbQ());

        // 按code和name分组
        Map<String, State> codeNameMap = states.stream().collect(Collectors.toMap(State::getCode, state -> state));

        Map<String, List<Task>> groupStateCodeTasks = tasks.stream().collect(Collectors.groupingBy(Task::getStateCode));

        List<TaskTypeComponentResult.Pie> data = new LinkedList<>();

        for (Map.Entry<String, List<Task>> entry : groupStateCodeTasks.entrySet())
        {
            String stateCode = entry.getKey();
            List<Task> taskList = entry.getValue();
            State state = codeNameMap.get(stateCode);
            TaskTypeComponentResult.Pie pie = TaskTypeComponentResult.Pie.builder()
                    .name(state.getName())
                    .value(taskList.size())
                    .color(state.getColor())
                    .build();
            data.add(pie);
//            data.add(TaskTypeComponentResult.Pie.builder()
//                    .name(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getName() :
//                            entry.getKey())
//                    .color(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getColor() :
//                            entry.getKey())
//                    .value(entry.getValue().size()).build());
        }

        return TaskTypeComponentResult.builder().count(tasks.size()).data(data).build();
    }

    @Override
    public TaskTypeComponentResult findTaskComponentByPlanId(Long planId)
    {
        // 根据计划id查询所有任务
        List<Task> requirements = taskMapper.selectList(Wraps.<Task>lbQ().eq(Task::getPlanId, planId));
        ProjectPlan projectPlan = projectPlanMapper.selectById(planId);
        if (projectPlan == null || projectPlan.getProjectId() == null)
        {
            throw BizException.validFail("初始化数据异常，请联系管理员");
        }
        // 查询所有任务状态
        // 获取项目任务工作流
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(projectPlan.getProjectId(),
                        TypeClassify.TASK, null);
        if (projectWorkflow == null || projectWorkflow.getWorkflowId() == null)
        {
            throw BizException.validFail("初始化数据异常，请联系管理员");
        }
        List<WorkflowNode> workflowNodes = workflowNodeMapper.selectList(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId()));
        List<String> stateCodes = workflowNodes.stream().map(WorkflowNode::getStateCode).collect(Collectors.toList());
        List<State> states = stateMapper.selectList(Wraps.<State>lbQ().in(State::getCode, stateCodes));

        // 按code和name分组
        Map<String, State> codeNameMap = states.stream().collect(Collectors.toMap(State::getCode, state -> state));

        Map<String, List<Task>> groupStateCodeRequirements =
                requirements.stream().collect(Collectors.groupingBy(Task::getStateCode));

        List<TaskTypeComponentResult.Pie> data = new LinkedList<>();

        for (Map.Entry<String, List<Task>> entry : groupStateCodeRequirements.entrySet())
        {
            data.add(TaskTypeComponentResult.Pie.builder()
                    .name(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getName() :
                            entry.getKey())
                    .color(codeNameMap.containsKey(entry.getKey()) ? codeNameMap.get(entry.getKey()).getColor() :
                            entry.getKey())
                    .value(entry.getValue().size()).build());
        }

        return TaskTypeComponentResult.builder().count(requirements.size()).data(data).build();
    }

    @Override
    public TaskStateComponentResult findStateByProjectId(Long projectId)
    {
        // 根据项目id查询所有有执行人的任务
        List<Task> tasks =
                taskMapper.selectList(Wraps.<Task>lbQ().eq(Task::getProjectId, projectId).isNotNull(Task::getHandleBy));
        actionHandleById( tasks);

        // 查询所有任务状态
        // 获取项目任务工作流
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(projectId,
                        TypeClassify.TASK, null);
        if (projectWorkflow == null || projectWorkflow.getWorkflowId() == null)
        {
            throw BizException.validFail("初始化数据异常，请联系管理员");
        }
        List<WorkflowNode> workflowNodes = workflowNodeMapper.selectList(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId()));
        List<String> stateCodes = workflowNodes.stream().map(WorkflowNode::getStateCode).collect(Collectors.toList());
        List<State> states = stateMapper.selectList(Wraps.<State>lbQ().in(State::getCode, stateCodes));

        // 获取项目成员
        Set<Long> handleBys =
                projectUserMapper.selectList(Wraps.<ProjectUser>lbQ().eq(ProjectUser::getProjectId, projectId)).stream()
                        .map(ProjectUser::getUserId).collect(Collectors.toSet());
        List<String> name =new ArrayList<>();
        if(handleBys !=null && !handleBys.isEmpty()){
            // 查询任务人员信息
            name = userMapper.selectBatchIds(handleBys).stream().map(User::getName).collect(
                    Collectors.toList());
        }
        List<TaskStateComponentResult.Bar> data = new LinkedList<>();
        for (State state : states)
        {
            TaskStateComponentResult.Bar dataItem = new TaskStateComponentResult.Bar();

            dataItem.setName(state.getName());
            dataItem.setColor(state.getColor());
            List<Integer> taskNumData = new LinkedList<>();
            for (Long handleBy : handleBys)
            {
                //筛选状态和处理人条数
                Integer count = (int) tasks.stream().filter(task -> task.getHandleByList().contains(handleBy)&&
                        state.getCode().equals(task.getStateCode())).count();
                taskNumData.add(count);
            }

            dataItem.setData(taskNumData);

            data.add(dataItem);
        }

        return TaskStateComponentResult.builder().name(name).data(data).build();
    }

    @Override
    public TaskStateComponentResult findStateByPlanId(Long planId)
    {
        // 根据项目id查询所有有执行人的任务
        List<Task> tasks =
                taskMapper.selectList(Wraps.<Task>lbQ().eq(Task::getPlanId, planId).isNotNull(Task::getHandleBy));
        actionHandleById(tasks);
        ProjectPlan projectPlan = projectPlanMapper.selectById(planId);
        if (projectPlan == null || projectPlan.getProjectId() == null)
        {
            throw BizException.validFail("获取迭代信息失败");
        }
        // 查询所有状态
        // 获取项目任务工作流
        ProjectWorkflow projectWorkflow =
                projectWorkflowService.findProjectByProjectIdAndTypeClassifyAndTypeCode(projectPlan.getProjectId(),
                        TypeClassify.TASK, null);
        if (projectWorkflow == null || projectWorkflow.getWorkflowId() == null)
        {
            throw BizException.validFail("初始化数据异常，请联系管理员");
        }
        List<WorkflowNode> workflowNodes = workflowNodeMapper.selectList(
                Wraps.<WorkflowNode>lbQ().eq(WorkflowNode::getWorkflowId, projectWorkflow.getWorkflowId()));
        List<String> stateCodes = workflowNodes.stream().map(WorkflowNode::getStateCode).collect(Collectors.toList());
        List<State> states = stateMapper.selectList(Wraps.<State>lbQ().in(State::getCode, stateCodes));

        Set<Long> handleBys = projectUserMapper.selectList(
                        Wraps.<ProjectUser>lbQ().eq(ProjectUser::getProjectId, projectPlan.getProjectId())).stream()
                .map(ProjectUser::getUserId).collect(Collectors.toSet());

        // 查询任务人员信息
        List<String> name = userMapper.selectBatchIds(handleBys).stream().map(User::getName).collect(
                Collectors.toList());

        List<TaskStateComponentResult.Bar> data = new LinkedList<>();
        for (State state : states)
        {
            TaskStateComponentResult.Bar dataItem = new TaskStateComponentResult.Bar();

            dataItem.setName(state.getName());
            dataItem.setColor(state.getColor());
            List<Integer> taskNumData = new LinkedList<>();
            for (Long handleBy : handleBys)
            {
                //筛选状态和处理人条数
                Integer count = (int) tasks.stream().filter(task -> task.getHandleByList().contains(handleBy) &&
                        state.getCode().equals(task.getStateCode())).count();
                taskNumData.add(count);
            }

            dataItem.setData(taskNumData);

            data.add(dataItem);
        }

        return TaskStateComponentResult.builder().name(name).data(data).build();
    }

    @Override
    public TaskCountComponentResult findCountByQuery(TaskCountQuery query)
    {
        query.setQueryType(0);
        int total = taskMapper.queryCountByQuery(query);
        List<TaskCountComponentResult.Bar> bars =
                BeanUtil.copyToList(taskMapper.queryCountTypeByQuery(query), TaskCountComponentResult.Bar.class);
        query.setQueryType(1);
        int addNum = taskMapper.queryCountByQuery(query);
        query.setQueryType(2);
        int completeNum = taskMapper.queryCountByQuery(query);
        return TaskCountComponentResult.builder().total(total).addNum(addNum).completeNum(completeNum).data(bars).build();

    }

    @Override
    public TaskCountComponentResult findLeaderCountByQuery(TaskCountQuery query)
    {

        List<QueryWrap<Task>> queryWraps = buildWrapper(query);
        if (queryWraps==null){
            return new TaskCountComponentResult();
        }
        Long todoNum = taskMapper.selectCount(queryWraps.get(0));
        Long processingNum = taskMapper.selectCount(queryWraps.get(1));
        Long completeNum = taskMapper.selectCount(queryWraps.get(2));
        return TaskCountComponentResult.builder().todoNum(todoNum.intValue()).processingNum(processingNum.intValue()).completeNum(completeNum.intValue()).build();
    }

    @Override
    public IPage<Task> findTaskByQuery(IPage<Task> page,TaskCountQuery query) {
        List<QueryWrap<Task>> queryWraps = buildWrapper(query);
        if (queryWraps==null){
            return null;
        }
        if(query.getQueryType()==0){
            return taskMapper.selectPage(page, queryWraps.get(0));
        } else if (query.getQueryType()==1) {
            return taskMapper.selectPage(page, queryWraps.get(1));
        } else if (query.getQueryType()==2) {
            return taskMapper.selectPage(page, queryWraps.get(2));
        }
        return null;
    }

    @Override
    public List<TaskCountComponentResult> findLeaderCountGroupBy(TaskCountQuery query)
    {
        List<QueryWrap<Task>> lbqWrappers = buildWrapper(query);
        if (lbqWrappers==null){
            return Collections.emptyList();
        }
        QueryWrap<Task> todoWrap = lbqWrappers.get(0);
        QueryWrap<Task> processingWrap = lbqWrappers.get(1);
        QueryWrap<Task> completeWrap = lbqWrappers.get(2);
        List<TaskCountComponentResult> results = new ArrayList<>();
        if (CollUtil.isNotEmpty(query.getUserIds()))
        {
            todoWrap.lambda().groupBy(Task::getHandleBy);
            todoWrap.select("handle_by as bizId , count(id) as num");
            processingWrap.lambda().groupBy(Task::getHandleBy);
            processingWrap.select("handle_by as bizId , count(id) as num");
            completeWrap.lambda().groupBy(Task::getHandleBy);
            completeWrap.select("handle_by as bizId , count(id) as num");
            results = query.getUserIds().stream().map(userId ->  TaskCountComponentResult.builder().bizId(userId).build()).collect(Collectors.toList());
        }
        else if (CollUtil.isNotEmpty(query.getProjectIds()))
        {
            todoWrap.lambda().groupBy(Task::getProjectId);
            todoWrap.select("project_id as bizId , count(id) as num");
            processingWrap.lambda().groupBy(Task::getProjectId);
            processingWrap.select("project_id as bizId , count(id) as num");
            completeWrap.lambda().groupBy(Task::getProjectId);
            completeWrap.select("project_id as bizId , count(id) as num");
            results = query.getProjectIds().stream().map(projectId ->  TaskCountComponentResult.builder().bizId(projectId).build()).collect(Collectors.toList());
        }
        Map<Long, TaskCountComponentResult> resultMap =
                results.stream().collect(Collectors.toMap(TaskCountComponentResult::getBizId, Function.identity()));
        List<Map<String, Object>> todoMaps = taskMapper.selectMaps(todoWrap);
        todoMaps.forEach(map -> {
            TaskCountComponentResult result = resultMap.get(Long.valueOf(map.get("bizId").toString()));
            result.setTodoNum(Integer.valueOf(map.get("num").toString()));
        });
        List<Map<String, Object>> processingMaps = taskMapper.selectMaps(processingWrap);
        processingMaps.forEach(map -> {
            TaskCountComponentResult result = resultMap.get(Long.valueOf(map.get("bizId").toString()));
            result.setProcessingNum(Integer.valueOf(map.get("num").toString()));
        });
        List<Map<String, Object>> completeMaps = taskMapper.selectMaps(completeWrap);
        completeMaps.forEach(map -> {
            TaskCountComponentResult result = resultMap.get(Long.valueOf(map.get("bizId").toString()));
            result.setCompleteNum(Integer.valueOf(map.get("num").toString()));
        });
        return results;
    }

    List<QueryWrap<Task>> buildWrapper(TaskCountQuery query)
    {
        QueryWrap<Task> lbqWrapper = Wraps.q();

        if (null != query.getUserIds() && !query.getUserIds().isEmpty())
        {
            lbqWrapper.lambda().in(Task::getHandleBy, query.getUserIds());
        }
        // 组织机构不为null，添加当前组织机构下处理人查询条件
        if (null != query.getOrgIds() && !query.getOrgIds().isEmpty())
        {
            // 查询机构下所有人员信息
            List<Long> userIds = userMapper.selectIdByOrgIds(query.getOrgIds());
            // 如果机构下没有用户，直接返回
            if (userIds.isEmpty())
            {
                return null;
            }
            lbqWrapper.lambda().in(Task::getHandleBy, userIds);
        }

        // 项目不为null，添加项目查询条件
        if (null != query.getProjectIds() && !query.getProjectIds().isEmpty())
        {
            lbqWrapper.lambda().in(Task::getProjectId, query.getProjectIds());
        }

        // 产品集不为null，添加产品查询条件
        if (null != query.getProductsetIds() && !query.getProductsetIds().isEmpty())
        {
            List<Long> productIds = productInfoMapper.selectIdByProductsetIds(query.getProductsetIds());
            // 如果产品集下没有产品，直接返回
            if (productIds.isEmpty())
            {
                return null;
            }
            lbqWrapper.lambda().in(Task::getProductId, productIds);
        }

        //未开始
        QueryWrap<Task> todoLbqWrapper = lbqWrapper.clone();
        //1、创建时间小于当前月开始日期并且（结束时间大于当前月开始日期或者结束时间为空）
        //2、创建时间大于当前月开始日期并且小于当前月结束日期
        todoLbqWrapper.lambda().and( wrapper -> wrapper.and(w->w.le(Task::getCreateTime, query.getStartDate())
                                .and(innerWrapper -> innerWrapper.ge(Task::getPlanEtime, query.getStartDate()).or().isNull(Task::getEndTime)))
                        .or( w -> w.ge(Task::getCreateTime, query.getStartDate()).le(Task::getCreateTime, query.getEndDate())))
                .eq(Task::getRateProgress,0);
        //进行中
        QueryWrap<Task> processingLbqWrapper = lbqWrapper.clone();
        processingLbqWrapper.lambda().ne(Task::getRateProgress, 0).and(w->w
                        .le(Task::getCreateTime, query.getStartDate()))
                .and(w->w
                        .ge(Task::getEndTime, query.getStartDate()).or().isNull(Task::getEndTime));
        //已完成
        QueryWrap<Task> completeLbqWrapper = lbqWrapper.clone();
        completeLbqWrapper.lambda().and(w->w.between(Task::getEndTime, query.getStartDate(), query.getEndDate()));
        return Arrays.asList(todoLbqWrapper,processingLbqWrapper,completeLbqWrapper);
    }

    @Override
    public IssueHourCountComponentResult findHourCountByQuery(TaskCountQuery query)
    {
        query.setQueryType(4);
        IssueHourCountComponentResult result = taskMapper.querySumEstimateHour(query);
        if (result ==null)
        {
            result = new IssueHourCountComponentResult();
        }
        Double filledHour;
        if (CollUtil.isNotEmpty(query.getProductsetIds())){
            filledHour = taskMapper.queryFilledHourByProducset(query);
        }else {
            filledHour = taskMapper.queryFilledHour(query);
        }
        result.setFilledHour(filledHour);
        return result;
    }

    @Override
    public List<IssueHourCountComponentResult> findHourCountGroupBy(TaskCountQuery query)
    {
        if (CollUtil.isNotEmpty(query.getUserIds()))
        {
            //判断分组条件
            query.setQueryType(-1);
        }
        List<Map<String, Object>> sumEstimateHourGroupBy = taskMapper.querySumEstimateHourGroupBy(query);
        List<Map<String, Object>> filledHourGroupBy = taskMapper.queryFilledHourGroupBy(query);

        List<IssueHourCountComponentResult> results = new ArrayList<>();
        if (CollUtil.isNotEmpty(query.getUserIds()))
        {
            results = query.getUserIds().stream().map(userId->IssueHourCountComponentResult.builder().bizId(userId).build()).collect(Collectors.toList());
        }
        else if (CollUtil.isNotEmpty(query.getProjectIds()))
        {
            results = query.getProjectIds().stream().map(projectId->IssueHourCountComponentResult.builder().bizId(projectId).build()).collect(Collectors.toList());
        }
        Map<Long, IssueHourCountComponentResult> resultMap = results.stream().collect(
                Collectors.toMap(IssueHourCountComponentResult::getBizId, Function.identity(), (k1, k2) -> k1));
        sumEstimateHourGroupBy.forEach(map -> {
            IssueHourCountComponentResult result = resultMap.get(Long.valueOf(map.get("bizId").toString()));
            result.setEstimateHour(Double.valueOf(map.get("estimateHour").toString()));
            result.setCompleteHour(Double.valueOf(map.get("completeHour").toString()));
        });
        filledHourGroupBy.forEach(map -> {
            IssueHourCountComponentResult result = resultMap.get(Long.valueOf(map.get("bizId").toString()));
            result.setFilledHour(Double.valueOf(map.get("filledHour").toString()));
        });
        return results;
    }

    @Override
    public IPage<WorkItemDTO> queryWorkItemPageByQuery(IPage<WorkItemDTO> page, TaskCountQuery query)
    {
        query.setQueryType(-1);
        if(query.getHandleBy()!=null){
            List<Long> userIds = new ArrayList<>();
            userIds.add(query.getHandleBy());
            query.setUserIds(userIds);
        }
        return taskMapper.queryWorkItemPageByQuery(page, query);
    }

    @Override
    public List<WorkItemDTO> queryWorkItem(TaskCountQuery query)
    {
        query.setQueryType(-1);
        return taskMapper.queryWorkItemPageByQuery(query);
    }


    public void actionHandleById(List<Task> tasks)
    {
        List<Long> taskIds = tasks.stream().map(Task::getId).collect(Collectors.toList());
        List<TaskUser> taskUserList = taskUserMapper.selectList(Wraps.<TaskUser>lbQ().eq(TaskUser::getType, ItemUserType.HANDLE).in(TaskUser::getTaskId, taskIds));
        Map<Long, List<Long>> taskUserMap = taskUserList.stream().collect(Collectors.groupingBy(TaskUser::getTaskId,
                Collectors.mapping(TaskUser::getUserId, Collectors.toList())));
        tasks.forEach(task ->
                task.setHandleByList(taskUserMap.get(task.getId()))
        );
    }
}
