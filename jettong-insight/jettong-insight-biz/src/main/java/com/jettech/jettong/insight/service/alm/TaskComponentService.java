package com.jettech.jettong.insight.service.alm;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jettech.jettong.alm.issue.dto.TaskCountQuery;
import com.jettech.jettong.alm.issue.dto.WorkItemDTO;
import com.jettech.jettong.alm.issue.entity.Task;
import com.jettech.jettong.insight.vo.alm.*;

import java.util.List;

/**
 * 任务组件业务处理层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @description 任务组件业务处理层接口
 * @projectName jettong-enterprises
 * @package com.jettech.jettong.insight.service.alm
 * @className TaskComponentService
 * @date 2021/12/2 17:56
 * @copyright 2025 JET-TECH Technologies Co., Ltd. All Rights Reserved.
 * @ 注意：本内容仅限于北京捷科智诚科技有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
public interface TaskComponentService
{

    /**
     * 根据项目id查询任务概览信息
     *
     * @param projectId 项目id
     * @return TaskTypeComponentResult 任务概览信息
     * <AUTHOR>
     * @date 2021/12/2 17:57
     * @update zxy 2021/12/2 17:57
     * @since 1.0
     */
    TaskTypeComponentResult findTypeByProjectId(Long projectId);

    /**
     * 根据项目id查询任务状态人员分布信息
     *
     * @param projectId 项目id
     * @return TaskStateComponentResult 任务状态人员分布信息
     * <AUTHOR>
     * @date 2021/12/2 17:57
     * @update zxy 2021/12/2 17:57
     * @since 1.0
     */
    TaskStateComponentResult findStateByProjectId(Long projectId);

    /**
     * 根据计划id查询任务概览信息
     *
     * @param planId 计划id
     * @return TaskTypeComponentResult 任务概览信息
     * <AUTHOR>
     * @date 2021/12/04 16:27
     * @update lxr 2021/12/04 16:27
     * @since 1.0
     */
    TaskTypeComponentResult findTaskComponentByPlanId(Long planId);

    /**
     * 根据计划id查询任务状态人员分布信息
     *
     * @param planId 计划id
     * @return TaskStateComponentResult 任务状态人员分布信息
     * <AUTHOR>
     * @date 2021/12/04 15:20
     * @update lxr 2021/12/04 15:20
     * @since 1.0
     */
    TaskStateComponentResult findStateByPlanId(Long planId);

    /**
     * 研发工作台任务统计
     * @param query
     * @return {@link TaskCountComponentResult}
     * @throws
     * <AUTHOR>
     * @date 2024/5/7 9:41
     * @update ly 2024/5/7 9:41
     * @since 1.0
     */
    TaskCountComponentResult findCountByQuery(TaskCountQuery query);

    /**
     * 领导工作台任务统计
     * @param query
     * @return {@link TaskCountComponentResult}
     * @throws
     * <AUTHOR>
     * @date 2024/5/7 9:41
     * @update ly 2024/5/7 9:41
     * @since 1.0
     */
    TaskCountComponentResult findLeaderCountByQuery(TaskCountQuery query);

    IPage<Task> findTaskByQuery(IPage<Task> page,TaskCountQuery query);

    /**
     * 按照项目或人员分组查询
     * @param query
     * @return {@link IssueHourCountComponentResult}
     * @throws
     * <AUTHOR>
     * @date 2024/5/13 17:34
     * @update ly 2024/5/13 17:34
     * @since 1.0
     */
    List<TaskCountComponentResult> findLeaderCountGroupBy(TaskCountQuery query);

    /**
     * 研发工作台工时统计
     * @param query
     * @return {@link TaskCountComponentResult}
     * @throws
     * <AUTHOR>
     * @date 2024/5/7 9:41
     * @update ly 2024/5/7 9:41
     * @since 1.0
     */

    IssueHourCountComponentResult findHourCountByQuery(TaskCountQuery query);

    /**
     * 按照项目或人员分组查询统计工时
     * @param query
     * @return {@link IssueHourCountComponentResult}
     * @throws
     * <AUTHOR>
     * @date 2024/5/13 17:34
     * @update ly 2024/5/13 17:34
     * @since 1.0
     */
    List<IssueHourCountComponentResult> findHourCountGroupBy(TaskCountQuery query);


    IPage<WorkItemDTO> queryWorkItemPageByQuery(IPage<WorkItemDTO> page, TaskCountQuery query);

    List<WorkItemDTO> queryWorkItem( TaskCountQuery query);

}
